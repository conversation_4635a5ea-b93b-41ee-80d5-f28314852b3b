# TrustVault - Portfolio Views

from rest_framework import generics, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from django.db.models import Sum, Count, Q
from django.utils import timezone
from drf_spectacular.utils import extend_schema
from .models import Portfolio, Asset, Holding, Transaction, Watchlist
from .serializers import (
    PortfolioSerializer, PortfolioSummarySerializer, AssetSerializer,
    HoldingSerializer, TransactionSerializer, WatchlistSerializer,
    PortfolioAnalyticsSerializer
)
from apps.core.models import AuditLog


class PortfolioListCreateView(generics.ListCreateAPIView):
    """List and create portfolios."""
    
    permission_classes = [permissions.IsAuthenticated]
    
    def get_serializer_class(self):
        """Return appropriate serializer based on action."""
        if self.request.method == 'GET':
            return PortfolioSummarySerializer
        return PortfolioSerializer
    
    def get_queryset(self):
        """Get user's portfolios."""
        return Portfolio.objects.filter(user=self.request.user).annotate(
            holdings_count=Count('holdings')
        )
    
    @extend_schema(
        summary="List Portfolios",
        description="Get list of user's portfolios",
        responses={200: PortfolioSummarySerializer(many=True)},
        tags=["Portfolio"]
    )
    def get(self, request, *args, **kwargs):
        """List user's portfolios."""
        return super().get(request, *args, **kwargs)
    
    @extend_schema(
        summary="Create Portfolio",
        description="Create a new portfolio",
        request=PortfolioSerializer,
        responses={201: PortfolioSerializer},
        tags=["Portfolio"]
    )
    def post(self, request, *args, **kwargs):
        """Create new portfolio."""
        return super().post(request, *args, **kwargs)
    
    def perform_create(self, serializer):
        """Create portfolio for current user."""
        portfolio = serializer.save(user=self.request.user)
        
        # Log portfolio creation
        AuditLog.objects.create(
            user=self.request.user,
            action='CREATE',
            resource_type='Portfolio',
            resource_id=str(portfolio.id),
            ip_address=self._get_client_ip(),
            user_agent=self.request.META.get('HTTP_USER_AGENT', ''),
            details={'name': portfolio.name, 'type': portfolio.portfolio_type}
        )
    
    def _get_client_ip(self):
        """Get client IP address."""
        x_forwarded_for = self.request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = self.request.META.get('REMOTE_ADDR')
        return ip


class PortfolioDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update, and delete portfolio."""
    
    serializer_class = PortfolioSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Get user's portfolios."""
        return Portfolio.objects.filter(user=self.request.user)
    
    @extend_schema(
        summary="Get Portfolio",
        description="Get portfolio details with holdings",
        responses={200: PortfolioSerializer},
        tags=["Portfolio"]
    )
    def get(self, request, *args, **kwargs):
        """Get portfolio details."""
        return super().get(request, *args, **kwargs)
    
    @extend_schema(
        summary="Update Portfolio",
        description="Update portfolio information",
        request=PortfolioSerializer,
        responses={200: PortfolioSerializer},
        tags=["Portfolio"]
    )
    def put(self, request, *args, **kwargs):
        """Update portfolio."""
        return super().put(request, *args, **kwargs)
    
    @extend_schema(
        summary="Delete Portfolio",
        description="Delete portfolio and all associated data",
        responses={204: None},
        tags=["Portfolio"]
    )
    def delete(self, request, *args, **kwargs):
        """Delete portfolio."""
        return super().delete(request, *args, **kwargs)
    
    def perform_update(self, serializer):
        """Log portfolio update."""
        portfolio = serializer.save()
        
        AuditLog.objects.create(
            user=self.request.user,
            action='UPDATE',
            resource_type='Portfolio',
            resource_id=str(portfolio.id),
            ip_address=self._get_client_ip(),
            user_agent=self.request.META.get('HTTP_USER_AGENT', ''),
            details={'updated_fields': list(serializer.validated_data.keys())}
        )
    
    def perform_destroy(self, instance):
        """Log portfolio deletion."""
        AuditLog.objects.create(
            user=self.request.user,
            action='DELETE',
            resource_type='Portfolio',
            resource_id=str(instance.id),
            ip_address=self._get_client_ip(),
            user_agent=self.request.META.get('HTTP_USER_AGENT', ''),
            details={'name': instance.name},
            severity='HIGH'
        )
        
        super().perform_destroy(instance)
    
    def _get_client_ip(self):
        """Get client IP address."""
        x_forwarded_for = self.request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = self.request.META.get('REMOTE_ADDR')
        return ip


class AssetListView(generics.ListAPIView):
    """List available assets."""
    
    serializer_class = AssetSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Get assets with optional filtering."""
        queryset = Asset.objects.all()
        
        # Filter by asset type
        asset_type = self.request.query_params.get('type')
        if asset_type:
            queryset = queryset.filter(asset_type=asset_type)
        
        # Search by symbol or name
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(symbol__icontains=search) | Q(name__icontains=search)
            )
        
        # Filter by sector
        sector = self.request.query_params.get('sector')
        if sector:
            queryset = queryset.filter(sector=sector)
        
        return queryset.order_by('symbol')
    
    @extend_schema(
        summary="List Assets",
        description="Get list of available assets for investment",
        responses={200: AssetSerializer(many=True)},
        tags=["Assets"]
    )
    def get(self, request, *args, **kwargs):
        """List available assets."""
        return super().get(request, *args, **kwargs)


class HoldingListCreateView(generics.ListCreateAPIView):
    """List and create holdings for a portfolio."""
    
    serializer_class = HoldingSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Get holdings for the specified portfolio."""
        portfolio_id = self.kwargs['portfolio_id']
        return Holding.objects.filter(
            portfolio_id=portfolio_id,
            portfolio__user=self.request.user
        ).select_related('asset')
    
    @extend_schema(
        summary="List Holdings",
        description="Get holdings for a specific portfolio",
        responses={200: HoldingSerializer(many=True)},
        tags=["Holdings"]
    )
    def get(self, request, *args, **kwargs):
        """List portfolio holdings."""
        return super().get(request, *args, **kwargs)
    
    @extend_schema(
        summary="Create Holding",
        description="Add a new holding to the portfolio",
        request=HoldingSerializer,
        responses={201: HoldingSerializer},
        tags=["Holdings"]
    )
    def post(self, request, *args, **kwargs):
        """Create new holding."""
        return super().post(request, *args, **kwargs)
    
    def perform_create(self, serializer):
        """Create holding for the specified portfolio."""
        portfolio_id = self.kwargs['portfolio_id']
        portfolio = Portfolio.objects.get(id=portfolio_id, user=self.request.user)
        
        holding = serializer.save(portfolio=portfolio)
        holding.calculate_current_value()
        portfolio.calculate_total_value()
        
        # Log holding creation
        AuditLog.objects.create(
            user=self.request.user,
            action='CREATE',
            resource_type='Holding',
            resource_id=str(holding.id),
            ip_address=self._get_client_ip(),
            user_agent=self.request.META.get('HTTP_USER_AGENT', ''),
            details={
                'portfolio': portfolio.name,
                'asset': holding.asset.symbol,
                'quantity': str(holding.quantity)
            }
        )
    
    def _get_client_ip(self):
        """Get client IP address."""
        x_forwarded_for = self.request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = self.request.META.get('REMOTE_ADDR')
        return ip


class TransactionListCreateView(generics.ListCreateAPIView):
    """List and create transactions for a portfolio."""
    
    serializer_class = TransactionSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Get transactions for the specified portfolio."""
        portfolio_id = self.kwargs['portfolio_id']
        return Transaction.objects.filter(
            portfolio_id=portfolio_id,
            portfolio__user=self.request.user
        ).select_related('asset').order_by('-transaction_date')
    
    @extend_schema(
        summary="List Transactions",
        description="Get transaction history for a specific portfolio",
        responses={200: TransactionSerializer(many=True)},
        tags=["Transactions"]
    )
    def get(self, request, *args, **kwargs):
        """List portfolio transactions."""
        return super().get(request, *args, **kwargs)
    
    @extend_schema(
        summary="Create Transaction",
        description="Record a new transaction for the portfolio",
        request=TransactionSerializer,
        responses={201: TransactionSerializer},
        tags=["Transactions"]
    )
    def post(self, request, *args, **kwargs):
        """Create new transaction."""
        return super().post(request, *args, **kwargs)
    
    def perform_create(self, serializer):
        """Create transaction for the specified portfolio."""
        portfolio_id = self.kwargs['portfolio_id']
        portfolio = Portfolio.objects.get(id=portfolio_id, user=self.request.user)
        
        transaction = serializer.save(portfolio=portfolio)
        
        # Update holdings based on transaction
        self._update_holdings_from_transaction(transaction)
        
        # Recalculate portfolio value
        portfolio.calculate_total_value()
        
        # Log transaction
        AuditLog.objects.create(
            user=self.request.user,
            action='CREATE',
            resource_type='Transaction',
            resource_id=str(transaction.id),
            ip_address=self._get_client_ip(),
            user_agent=self.request.META.get('HTTP_USER_AGENT', ''),
            details={
                'portfolio': portfolio.name,
                'type': transaction.transaction_type,
                'asset': transaction.asset.symbol if transaction.asset else None,
                'amount': str(transaction.total_amount)
            }
        )
    
    def _update_holdings_from_transaction(self, transaction):
        """Update holdings based on transaction."""
        if transaction.transaction_type in ['BUY', 'SELL'] and transaction.asset:
            holding, created = Holding.objects.get_or_create(
                portfolio=transaction.portfolio,
                asset=transaction.asset,
                defaults={
                    'quantity': 0,
                    'average_cost': 0,
                    'current_value': 0
                }
            )
            
            if transaction.transaction_type == 'BUY':
                # Update average cost and quantity
                total_cost = (holding.quantity * holding.average_cost) + (transaction.quantity * transaction.price)
                total_quantity = holding.quantity + transaction.quantity
                
                holding.average_cost = total_cost / total_quantity if total_quantity > 0 else 0
                holding.quantity = total_quantity
            
            elif transaction.transaction_type == 'SELL':
                holding.quantity -= transaction.quantity
                if holding.quantity <= 0:
                    holding.delete()
                    return
            
            holding.calculate_current_value()
    
    def _get_client_ip(self):
        """Get client IP address."""
        x_forwarded_for = self.request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = self.request.META.get('REMOTE_ADDR')
        return ip
