{"ast": null, "code": "// TrustVault - Portfolio Detail Page\nimport React from'react';import{useParams,useNavigate}from'react-router-dom';import{Box,Typography,Button,Grid,Card,CardContent,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Paper,Chip,IconButton}from'@mui/material';import{ArrowBack,Edit,Add,TrendingUp,TrendingDown}from'@mui/icons-material';import{Helmet}from'react-helmet-async';import{useQuery}from'react-query';// Services\nimport apiService from'../../services/api';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const PortfolioDetailPage=()=>{var _portfolio$holdings;const{id}=useParams();const navigate=useNavigate();// Redirect if someone tries to access /portfolios/create through this component\nReact.useEffect(()=>{if(id==='create'){navigate('/portfolios/create',{replace:true});}},[id,navigate]);// Move hooks before any conditional returns\nconst{data:portfolio,isLoading}=useQuery(['portfolio',id],()=>apiService.getPortfolio(id),{enabled:!!id&&id!=='create'});// Don't render anything if ID is 'create'\nif(id==='create'){return null;}const formatCurrency=value=>{return new Intl.NumberFormat('en-US',{style:'currency',currency:'USD'}).format(parseFloat(value));};const formatPercentage=value=>{const num=parseFloat(value);return`${num>=0?'+':''}${num.toFixed(2)}%`;};if(isLoading){return/*#__PURE__*/_jsx(Typography,{children:\"Loading portfolio...\"});}if(!portfolio){return/*#__PURE__*/_jsx(Typography,{children:\"Portfolio not found\"});}return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(Helmet,{children:[/*#__PURE__*/_jsxs(\"title\",{children:[portfolio.name,\" - TrustVault\"]}),/*#__PURE__*/_jsx(\"meta\",{name:\"description\",content:`Portfolio details for ${portfolio.name}`})]}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",gap:2,mb:4,children:[/*#__PURE__*/_jsx(IconButton,{onClick:()=>navigate('/portfolios'),children:/*#__PURE__*/_jsx(ArrowBack,{})}),/*#__PURE__*/_jsxs(Box,{flexGrow:1,children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",component:\"h1\",gutterBottom:true,children:portfolio.name}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",color:\"text.secondary\",children:portfolio.description||'No description'})]}),/*#__PURE__*/_jsx(Button,{variant:\"outlined\",startIcon:/*#__PURE__*/_jsx(Edit,{}),onClick:()=>navigate(`/portfolios/${id}/edit`),children:\"Edit\"})]}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,mb:4,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:8,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Portfolio Overview\"}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,children:[/*#__PURE__*/_jsxs(Grid,{item:true,xs:6,md:3,children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Total Value\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",color:\"primary\",children:formatCurrency(portfolio.total_value)})]}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:6,md:3,children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Holdings\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",children:((_portfolio$holdings=portfolio.holdings)===null||_portfolio$holdings===void 0?void 0:_portfolio$holdings.length)||0})]}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:6,md:3,children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Type\"}),/*#__PURE__*/_jsx(Chip,{label:portfolio.portfolio_type,color:\"primary\",variant:\"outlined\"})]}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:6,md:3,children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Currency\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",children:portfolio.currency})]})]})]})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:4,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Quick Actions\"}),/*#__PURE__*/_jsxs(Box,{display:\"flex\",flexDirection:\"column\",gap:2,children:[/*#__PURE__*/_jsx(Button,{variant:\"contained\",startIcon:/*#__PURE__*/_jsx(Add,{}),onClick:()=>navigate(`/portfolios/${id}/add-holding`),children:\"Add Holding\"}),/*#__PURE__*/_jsx(Button,{variant:\"outlined\",onClick:()=>navigate(`/portfolios/${id}/transactions`),children:\"View Transactions\"}),/*#__PURE__*/_jsx(Button,{variant:\"outlined\",onClick:()=>navigate(`/portfolios/${id}/analytics`),children:\"Analytics\"})]})]})})})]}),/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsxs(Box,{display:\"flex\",justifyContent:\"space-between\",alignItems:\"center\",mb:3,children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:\"Holdings\"}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",size:\"small\",startIcon:/*#__PURE__*/_jsx(Add,{}),onClick:()=>navigate(`/portfolios/${id}/add-holding`),children:\"Add Holding\"})]}),portfolio.holdings&&portfolio.holdings.length>0?/*#__PURE__*/_jsx(TableContainer,{component:Paper,variant:\"outlined\",children:/*#__PURE__*/_jsxs(Table,{children:[/*#__PURE__*/_jsx(TableHead,{children:/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:\"Asset\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Symbol\"}),/*#__PURE__*/_jsx(TableCell,{align:\"right\",children:\"Quantity\"}),/*#__PURE__*/_jsx(TableCell,{align:\"right\",children:\"Avg Cost\"}),/*#__PURE__*/_jsx(TableCell,{align:\"right\",children:\"Current Value\"}),/*#__PURE__*/_jsx(TableCell,{align:\"right\",children:\"P&L\"}),/*#__PURE__*/_jsx(TableCell,{align:\"right\",children:\"P&L %\"})]})}),/*#__PURE__*/_jsx(TableBody,{children:portfolio.holdings.map(holding=>/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",fontWeight:\"medium\",children:holding.asset.name}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",children:holding.asset.asset_type})]})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(Typography,{variant:\"body2\",fontWeight:\"medium\",children:holding.asset.symbol})}),/*#__PURE__*/_jsx(TableCell,{align:\"right\",children:parseFloat(holding.quantity).toLocaleString()}),/*#__PURE__*/_jsx(TableCell,{align:\"right\",children:formatCurrency(holding.average_cost)}),/*#__PURE__*/_jsx(TableCell,{align:\"right\",children:formatCurrency(holding.current_value)}),/*#__PURE__*/_jsx(TableCell,{align:\"right\",children:/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",justifyContent:\"flex-end\",gap:1,children:[parseFloat(holding.profit_loss)>=0?/*#__PURE__*/_jsx(TrendingUp,{color:\"success\",fontSize:\"small\"}):/*#__PURE__*/_jsx(TrendingDown,{color:\"error\",fontSize:\"small\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:parseFloat(holding.profit_loss)>=0?'success.main':'error.main',children:formatCurrency(holding.profit_loss)})]})}),/*#__PURE__*/_jsx(TableCell,{align:\"right\",children:/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:parseFloat(holding.profit_loss_percentage)>=0?'success.main':'error.main',children:formatPercentage(holding.profit_loss_percentage)})})]},holding.id))})]})}):/*#__PURE__*/_jsxs(Box,{textAlign:\"center\",py:4,children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"text.secondary\",gutterBottom:true,children:\"No Holdings Yet\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",mb:3,children:\"Add your first holding to start tracking your investments\"}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",startIcon:/*#__PURE__*/_jsx(Add,{}),onClick:()=>navigate(`/portfolios/${id}/add-holding`),children:\"Add First Holding\"})]})]})})]})]});};export default PortfolioDetailPage;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}