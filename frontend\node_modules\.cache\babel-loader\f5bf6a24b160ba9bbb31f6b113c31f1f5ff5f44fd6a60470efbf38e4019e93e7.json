{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getHiddenCssUtilityClass(slot) {\n  return generateUtilityClass('PrivateHiddenCss', slot);\n}\nconst hiddenCssClasses = generateUtilityClasses('PrivateHiddenCss', ['root', 'xlDown', 'xlUp', 'onlyXl', 'lgDown', 'lgUp', 'onlyLg', 'mdDown', 'mdUp', 'onlyMd', 'smDown', 'smUp', 'onlySm', 'xsDown', 'xsUp', 'onlyXs']);\nexport default hiddenCssClasses;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}