{"ast": null, "code": "'use client';\n\nimport PropTypes from 'prop-types';\nimport ClassNameGenerator from '@mui/utils/ClassNameGenerator';\nimport createBox from '../createBox';\nimport boxClasses from './boxClasses';\nconst Box = createBox({\n  defaultClassName: boxClasses.root,\n  generateClassName: ClassNameGenerator.generate\n});\nprocess.env.NODE_ENV !== \"production\" ? Box.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Box;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}