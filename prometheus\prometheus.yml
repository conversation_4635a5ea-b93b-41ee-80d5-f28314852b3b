# TrustVault - Prometheus Configuration

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'trustvault-monitor'
    environment: 'production'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# Load rules once and periodically evaluate them
rule_files:
  - "trustvault_rules.yml"
  - "security_rules.yml"

# Scrape configurations
scrape_configs:
  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 5s
    metrics_path: /metrics

  # Django application metrics
  - job_name: 'django'
    static_configs:
      - targets: ['django:8000']
    scrape_interval: 10s
    metrics_path: /metrics
    scrape_timeout: 5s
    honor_labels: true
    params:
      format: ['prometheus']

  # Nginx metrics
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:9113']
    scrape_interval: 10s
    metrics_path: /metrics

  # PostgreSQL metrics
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 10s
    metrics_path: /metrics

  # Redis metrics
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 10s
    metrics_path: /metrics

  # Node exporter for system metrics
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 10s
    metrics_path: /metrics

  # Wazuh metrics
  - job_name: 'wazuh'
    static_configs:
      - targets: ['wazuh-manager:55000']
    scrape_interval: 30s
    metrics_path: /metrics
    basic_auth:
      username: 'wazuh'
      password: 'wazuh'

  # Container metrics (cAdvisor)
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 10s
    metrics_path: /metrics

  # Blackbox exporter for endpoint monitoring
  - job_name: 'blackbox'
    metrics_path: /probe
    params:
      module: [http_2xx]
    static_configs:
      - targets:
        - https://trustvault.local
        - https://api.trustvault.local
        - https://trustvault.local/health
        - https://api.trustvault.local/health
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115

  # SSL certificate monitoring
  - job_name: 'ssl-expiry'
    metrics_path: /probe
    params:
      module: [ssl_expiry]
    static_configs:
      - targets:
        - trustvault.local:443
        - api.trustvault.local:443
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115

  # Custom security metrics
  - job_name: 'security-metrics'
    static_configs:
      - targets: ['security-exporter:9200']
    scrape_interval: 30s
    metrics_path: /metrics
    honor_labels: true

# Remote write configuration for long-term storage
remote_write:
  - url: "http://victoriametrics:8428/api/v1/write"
    queue_config:
      max_samples_per_send: 10000
      max_shards: 200
      capacity: 20000

# Storage configuration
storage:
  tsdb:
    path: /prometheus/
    retention.time: 30d
    retention.size: 10GB
    wal-compression: true

# Web configuration
web:
  console.templates: /etc/prometheus/consoles
  console.libraries: /etc/prometheus/console_libraries
  enable-lifecycle: true
  enable-admin-api: true
  max-connections: 512
  read-timeout: 30s
