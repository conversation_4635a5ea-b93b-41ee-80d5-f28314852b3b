{"ast": null, "code": "// TrustVault - Profile Page\nimport React from'react';import{<PERSON>,<PERSON><PERSON><PERSON>,<PERSON>,CardContent,TextField,Button,Grid,Avatar,Di<PERSON>r,Alert}from'@mui/material';import{Person,Security,Save,Lock}from'@mui/icons-material';import{Helmet}from'react-helmet-async';import{useForm}from'react-hook-form';import{yupResolver}from'@hookform/resolvers/yup';import*as yup from'yup';import{toast}from'react-hot-toast';// Store\nimport{useAuthStore}from'../../store/authStore';// Types\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";// Validation schema\nconst profileSchema=yup.object({first_name:yup.string().min(2,'First name must be at least 2 characters').required('First name is required'),last_name:yup.string().min(2,'Last name must be at least 2 characters').required('Last name is required'),phone_number:yup.string().matches(/^\\+?[1-9]\\d{1,14}$/,'Invalid phone number format').optional(),timezone:yup.string().required('Timezone is required'),language:yup.string().required('Language is required')});const ProfilePage=()=>{var _errors$first_name,_errors$last_name,_errors$phone_number,_errors$timezone,_errors$language,_user$first_name,_user$last_name;const{user,updateProfile,isLoading}=useAuthStore();const{register,handleSubmit,formState:{errors,isDirty}}=useForm({resolver:yupResolver(profileSchema),defaultValues:{first_name:(user===null||user===void 0?void 0:user.first_name)||'',last_name:(user===null||user===void 0?void 0:user.last_name)||'',phone_number:(user===null||user===void 0?void 0:user.phone_number)||'',timezone:(user===null||user===void 0?void 0:user.timezone)||'UTC',language:(user===null||user===void 0?void 0:user.language)||'en'}});const onSubmit=async data=>{try{await updateProfile(data);toast.success('Profile updated successfully!');}catch(error){toast.error('Failed to update profile');}};if(!user){return/*#__PURE__*/_jsx(Typography,{children:\"Loading...\"});}return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(Helmet,{children:[/*#__PURE__*/_jsx(\"title\",{children:\"Profile - TrustVault\"}),/*#__PURE__*/_jsx(\"meta\",{name:\"description\",content:\"Manage your profile settings\"})]}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Box,{mb:4,children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",component:\"h1\",gutterBottom:true,children:\"Profile Settings\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",color:\"text.secondary\",children:\"Manage your personal information and account settings\"})]}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:8,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",gap:2,mb:3,children:[/*#__PURE__*/_jsx(Person,{color:\"primary\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:\"Personal Information\"})]}),/*#__PURE__*/_jsxs(Box,{component:\"form\",onSubmit:handleSubmit(onSubmit),children:[/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsx(TextField,{...register('first_name'),fullWidth:true,label:\"First Name\",error:!!errors.first_name,helperText:(_errors$first_name=errors.first_name)===null||_errors$first_name===void 0?void 0:_errors$first_name.message})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsx(TextField,{...register('last_name'),fullWidth:true,label:\"Last Name\",error:!!errors.last_name,helperText:(_errors$last_name=errors.last_name)===null||_errors$last_name===void 0?void 0:_errors$last_name.message})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(TextField,{value:user.email,fullWidth:true,label:\"Email Address\",disabled:true,helperText:\"Email cannot be changed\"})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(TextField,{value:user.username,fullWidth:true,label:\"Username\",disabled:true,helperText:\"Username cannot be changed\"})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(TextField,{...register('phone_number'),fullWidth:true,label:\"Phone Number\",type:\"tel\",error:!!errors.phone_number,helperText:(_errors$phone_number=errors.phone_number)===null||_errors$phone_number===void 0?void 0:_errors$phone_number.message})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsxs(TextField,{...register('timezone'),fullWidth:true,label:\"Timezone\",select:true,SelectProps:{native:true},error:!!errors.timezone,helperText:(_errors$timezone=errors.timezone)===null||_errors$timezone===void 0?void 0:_errors$timezone.message,children:[/*#__PURE__*/_jsx(\"option\",{value:\"UTC\",children:\"UTC\"}),/*#__PURE__*/_jsx(\"option\",{value:\"America/New_York\",children:\"Eastern Time\"}),/*#__PURE__*/_jsx(\"option\",{value:\"America/Chicago\",children:\"Central Time\"}),/*#__PURE__*/_jsx(\"option\",{value:\"America/Denver\",children:\"Mountain Time\"}),/*#__PURE__*/_jsx(\"option\",{value:\"America/Los_Angeles\",children:\"Pacific Time\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Europe/London\",children:\"London\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Europe/Paris\",children:\"Paris\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Asia/Tokyo\",children:\"Tokyo\"})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsxs(TextField,{...register('language'),fullWidth:true,label:\"Language\",select:true,SelectProps:{native:true},error:!!errors.language,helperText:(_errors$language=errors.language)===null||_errors$language===void 0?void 0:_errors$language.message,children:[/*#__PURE__*/_jsx(\"option\",{value:\"en\",children:\"English\"}),/*#__PURE__*/_jsx(\"option\",{value:\"fr\",children:\"Fran\\xE7ais\"}),/*#__PURE__*/_jsx(\"option\",{value:\"es\",children:\"Espa\\xF1ol\"}),/*#__PURE__*/_jsx(\"option\",{value:\"de\",children:\"Deutsch\"})]})})]}),/*#__PURE__*/_jsx(Box,{mt:3,children:/*#__PURE__*/_jsx(Button,{type:\"submit\",variant:\"contained\",startIcon:/*#__PURE__*/_jsx(Save,{}),disabled:!isDirty||isLoading,children:isLoading?'Saving...':'Save Changes'})})]})]})})}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,md:4,children:[/*#__PURE__*/_jsx(Card,{sx:{mb:3},children:/*#__PURE__*/_jsx(CardContent,{children:/*#__PURE__*/_jsxs(Box,{display:\"flex\",flexDirection:\"column\",alignItems:\"center\",textAlign:\"center\",children:[/*#__PURE__*/_jsxs(Avatar,{sx:{width:80,height:80,mb:2,fontSize:'2rem'},children:[(_user$first_name=user.first_name)===null||_user$first_name===void 0?void 0:_user$first_name[0],(_user$last_name=user.last_name)===null||_user$last_name===void 0?void 0:_user$last_name[0]]}),/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",gutterBottom:true,children:[user.first_name,\" \",user.last_name]}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",gutterBottom:true,children:user.email}),/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",color:\"text.secondary\",children:[\"Member since \",new Date(user.date_joined).toLocaleDateString()]})]})})}),/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",gap:2,mb:3,children:[/*#__PURE__*/_jsx(Security,{color:\"primary\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:\"Security\"})]}),/*#__PURE__*/_jsxs(Box,{mb:2,children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",gutterBottom:true,children:\"Two-Factor Authentication\"}),/*#__PURE__*/_jsx(Alert,{severity:user.is_mfa_enabled?'success':'warning',sx:{mb:2},children:user.is_mfa_enabled?'Enabled':'Disabled'}),!user.is_mfa_enabled&&/*#__PURE__*/_jsx(Button,{variant:\"outlined\",size:\"small\",fullWidth:true,sx:{mb:2},children:\"Enable 2FA\"})]}),/*#__PURE__*/_jsx(Divider,{sx:{my:2}}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",gutterBottom:true,children:\"Password\"}),/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",color:\"text.secondary\",display:\"block\",mb:2,children:[\"Last changed: \",new Date(user.date_joined).toLocaleDateString()]}),/*#__PURE__*/_jsx(Button,{variant:\"outlined\",size:\"small\",fullWidth:true,startIcon:/*#__PURE__*/_jsx(Lock,{}),children:\"Change Password\"})]})]})})]})]})]})]});};export default ProfilePage;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}