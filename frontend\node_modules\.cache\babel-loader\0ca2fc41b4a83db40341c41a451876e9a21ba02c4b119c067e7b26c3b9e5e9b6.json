{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\TrustVault\\\\frontend\\\\src\\\\pages\\\\Portfolio\\\\CreatePortfolioPage.tsx\",\n  _s = $RefreshSig$();\n// TrustVault - Create Portfolio Page\n\nimport React, { useState } from 'react';\nimport { Box, Typography, Paper, TextField, Button, FormControl, InputLabel, Select, MenuItem, FormControlLabel, Switch, Alert, CircularProgress } from '@mui/material';\nimport { ArrowBack, Save } from '@mui/icons-material';\nimport { Helmet } from 'react-helmet-async';\nimport { useNavigate } from 'react-router-dom';\nimport { useMutation, useQueryClient } from 'react-query';\nimport { toast } from 'react-hot-toast';\n\n// Services\nimport apiService from '../../services/api';\n\n// Types\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CreatePortfolioPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const queryClient = useQueryClient();\n\n  // Add console log to verify component is loading\n  console.log('CreatePortfolioPage: Component is loading');\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    portfolio_type: 'MODERATE',\n    is_public: false\n  });\n  const [errors, setErrors] = useState({});\n\n  // Move hooks before any conditional returns\n  const createPortfolioMutation = useMutation(data => apiService.createPortfolio(data), {\n    onSuccess: portfolio => {\n      toast.success('Portfolio created successfully!');\n      queryClient.invalidateQueries('portfolios');\n      navigate(`/portfolios/${portfolio.id}`);\n    },\n    onError: error => {\n      var _error$response;\n      console.error('Create portfolio error:', error);\n      if ((_error$response = error.response) !== null && _error$response !== void 0 && _error$response.data) {\n        setErrors(error.response.data);\n      } else {\n        toast.error('Failed to create portfolio. Please try again.');\n      }\n    }\n  });\n\n  // Early return for testing - remove this after debugging\n  if (process.env.NODE_ENV === 'development') {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        maxWidth: 800,\n        mx: 'auto',\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        component: \"h1\",\n        gutterBottom: true,\n        children: \"\\uD83D\\uDE80 Create New Portfolio - TEST MODE\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        color: \"text.secondary\",\n        gutterBottom: true,\n        children: \"This is a test version to verify the component is loading correctly.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: () => navigate('/portfolios'),\n        sx: {\n          mt: 2\n        },\n        children: \"Back to Portfolios\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this);\n  }\n  const handleInputChange = field => event => {\n    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n\n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: ''\n      }));\n    }\n  };\n  const handleSubmit = event => {\n    event.preventDefault();\n\n    // Basic validation\n    const newErrors = {};\n    if (!formData.name.trim()) {\n      newErrors.name = 'Portfolio name is required';\n    }\n    if (Object.keys(newErrors).length > 0) {\n      setErrors(newErrors);\n      return;\n    }\n    createPortfolioMutation.mutate(formData);\n  };\n  const portfolioTypes = [{\n    value: 'CONSERVATIVE',\n    label: 'Conservative',\n    description: 'Low risk, stable returns'\n  }, {\n    value: 'MODERATE',\n    label: 'Moderate',\n    description: 'Balanced risk and return'\n  }, {\n    value: 'AGGRESSIVE',\n    label: 'Aggressive',\n    description: 'High risk, high potential returns'\n  }, {\n    value: 'CUSTOM',\n    label: 'Custom',\n    description: 'Custom allocation strategy'\n  }];\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"title\", {\n        children: \"Create Portfolio - TrustVault\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"description\",\n        content: \"Create a new investment portfolio\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        maxWidth: 800,\n        mx: 'auto',\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'none'\n        },\n        children: \"CreatePortfolioPage-Loaded\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        alignItems: \"center\",\n        mb: 3,\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          startIcon: /*#__PURE__*/_jsxDEV(ArrowBack, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 24\n          }, this),\n          onClick: () => navigate('/portfolios'),\n          sx: {\n            mr: 2\n          },\n          children: \"Back to Portfolios\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          component: \"h1\",\n          children: \"Create New Portfolio\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            flexDirection: \"column\",\n            gap: 3,\n            children: [/*#__PURE__*/_jsxDEV(TextField, {\n              label: \"Portfolio Name\",\n              value: formData.name,\n              onChange: handleInputChange('name'),\n              error: !!errors.name,\n              helperText: errors.name,\n              required: true,\n              fullWidth: true,\n              placeholder: \"e.g., My Investment Portfolio\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              label: \"Description\",\n              value: formData.description,\n              onChange: handleInputChange('description'),\n              error: !!errors.description,\n              helperText: errors.description,\n              multiline: true,\n              rows: 3,\n              fullWidth: true,\n              placeholder: \"Describe your investment strategy and goals...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Portfolio Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: formData.portfolio_type,\n                onChange: handleInputChange('portfolio_type'),\n                label: \"Portfolio Type\",\n                children: portfolioTypes.map(type => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: type.value,\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body1\",\n                      children: type.label\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 200,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"text.secondary\",\n                      children: type.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 201,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 199,\n                    columnNumber: 23\n                  }, this)\n                }, type.value, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n              control: /*#__PURE__*/_jsxDEV(Switch, {\n                checked: formData.is_public,\n                onChange: handleInputChange('is_public')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 19\n              }, this),\n              label: /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: \"Make Portfolio Public\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"text.secondary\",\n                  children: \"Allow others to view your portfolio performance (holdings remain private)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this), Object.keys(errors).length > 0 && /*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"error\",\n              children: \"Please fix the errors above and try again.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              justifyContent: \"flex-end\",\n              gap: 2,\n              mt: 2,\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                onClick: () => navigate('/portfolios'),\n                disabled: createPortfolioMutation.isLoading,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                type: \"submit\",\n                variant: \"contained\",\n                startIcon: createPortfolioMutation.isLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 66\n                }, this) : /*#__PURE__*/_jsxDEV(Save, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 99\n                }, this),\n                disabled: createPortfolioMutation.isLoading,\n                children: createPortfolioMutation.isLoading ? 'Creating...' : 'Create Portfolio'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mt: 3,\n          bgcolor: 'background.default'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Getting Started\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: \"After creating your portfolio, you can:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          component: \"ul\",\n          sx: {\n            mt: 1,\n            pl: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            component: \"li\",\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Add assets and track your investments\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            component: \"li\",\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Record transactions and monitor performance\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            component: \"li\",\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"View detailed analytics and reports\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            component: \"li\",\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Set up alerts and notifications\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(CreatePortfolioPage, \"LVBbV6xPhmxwLcoYxEz/sjAItyM=\", false, function () {\n  return [useNavigate, useQueryClient, useMutation];\n});\n_c = CreatePortfolioPage;\nexport default CreatePortfolioPage;\nvar _c;\n$RefreshReg$(_c, \"CreatePortfolioPage\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Paper", "TextField", "<PERSON><PERSON>", "FormControl", "InputLabel", "Select", "MenuItem", "FormControlLabel", "Switch", "<PERSON><PERSON>", "CircularProgress", "ArrowBack", "Save", "<PERSON><PERSON><PERSON>", "useNavigate", "useMutation", "useQueryClient", "toast", "apiService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CreatePortfolioPage", "_s", "navigate", "queryClient", "console", "log", "formData", "setFormData", "name", "description", "portfolio_type", "is_public", "errors", "setErrors", "createPortfolioMutation", "data", "createPortfolio", "onSuccess", "portfolio", "success", "invalidateQueries", "id", "onError", "error", "_error$response", "response", "process", "env", "NODE_ENV", "sx", "max<PERSON><PERSON><PERSON>", "mx", "p", "children", "variant", "component", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "onClick", "mt", "handleInputChange", "field", "event", "value", "target", "type", "checked", "prev", "handleSubmit", "preventDefault", "newErrors", "trim", "Object", "keys", "length", "mutate", "portfolioTypes", "label", "content", "style", "display", "alignItems", "mb", "startIcon", "mr", "onSubmit", "flexDirection", "gap", "onChange", "helperText", "required", "fullWidth", "placeholder", "multiline", "rows", "map", "control", "severity", "justifyContent", "disabled", "isLoading", "size", "bgcolor", "pl", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/TrustVault/frontend/src/pages/Portfolio/CreatePortfolioPage.tsx"], "sourcesContent": ["// TrustVault - Create Portfolio Page\n\nimport React, { useState } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  TextField,\n  Button,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  FormControlLabel,\n  Switch,\n  Alert,\n  CircularProgress,\n} from '@mui/material';\nimport { ArrowBack, Save } from '@mui/icons-material';\nimport { Helmet } from 'react-helmet-async';\nimport { useNavigate } from 'react-router-dom';\nimport { useMutation, useQueryClient } from 'react-query';\nimport { toast } from 'react-hot-toast';\n\n// Services\nimport apiService from '../../services/api';\n\n// Types\ninterface CreatePortfolioForm {\n  name: string;\n  description: string;\n  portfolio_type: 'CONSERVATIVE' | 'MODERATE' | 'AGGRESSIVE' | 'CUSTOM';\n  is_public: boolean;\n}\n\nconst CreatePortfolioPage: React.FC = () => {\n  const navigate = useNavigate();\n  const queryClient = useQueryClient();\n\n  // Add console log to verify component is loading\n  console.log('CreatePortfolioPage: Component is loading');\n\n  const [formData, setFormData] = useState<CreatePortfolioForm>({\n    name: '',\n    description: '',\n    portfolio_type: 'MODERATE',\n    is_public: false,\n  });\n\n  const [errors, setErrors] = useState<Record<string, string>>({});\n\n  // Move hooks before any conditional returns\n  const createPortfolioMutation = useMutation(\n    (data: CreatePortfolioForm) => apiService.createPortfolio(data),\n    {\n      onSuccess: (portfolio) => {\n        toast.success('Portfolio created successfully!');\n        queryClient.invalidateQueries('portfolios');\n        navigate(`/portfolios/${portfolio.id}`);\n      },\n      onError: (error: any) => {\n        console.error('Create portfolio error:', error);\n        if (error.response?.data) {\n          setErrors(error.response.data);\n        } else {\n          toast.error('Failed to create portfolio. Please try again.');\n        }\n      },\n    }\n  );\n\n  // Early return for testing - remove this after debugging\n  if (process.env.NODE_ENV === 'development') {\n    return (\n      <Box sx={{ maxWidth: 800, mx: 'auto', p: 3 }}>\n        <Typography variant=\"h4\" component=\"h1\" gutterBottom>\n          🚀 Create New Portfolio - TEST MODE\n        </Typography>\n        <Typography variant=\"body1\" color=\"text.secondary\" gutterBottom>\n          This is a test version to verify the component is loading correctly.\n        </Typography>\n        <Button\n          variant=\"contained\"\n          onClick={() => navigate('/portfolios')}\n          sx={{ mt: 2 }}\n        >\n          Back to Portfolios\n        </Button>\n      </Box>\n    );\n  }\n\n  const handleInputChange = (field: keyof CreatePortfolioForm) => (\n    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | any\n  ) => {\n    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;\n    setFormData(prev => ({\n      ...prev,\n      [field]: value,\n    }));\n    \n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: '',\n      }));\n    }\n  };\n\n  const handleSubmit = (event: React.FormEvent) => {\n    event.preventDefault();\n    \n    // Basic validation\n    const newErrors: Record<string, string> = {};\n    \n    if (!formData.name.trim()) {\n      newErrors.name = 'Portfolio name is required';\n    }\n    \n    if (Object.keys(newErrors).length > 0) {\n      setErrors(newErrors);\n      return;\n    }\n    \n    createPortfolioMutation.mutate(formData);\n  };\n\n  const portfolioTypes = [\n    { value: 'CONSERVATIVE', label: 'Conservative', description: 'Low risk, stable returns' },\n    { value: 'MODERATE', label: 'Moderate', description: 'Balanced risk and return' },\n    { value: 'AGGRESSIVE', label: 'Aggressive', description: 'High risk, high potential returns' },\n    { value: 'CUSTOM', label: 'Custom', description: 'Custom allocation strategy' },\n  ];\n\n  return (\n    <>\n      <Helmet>\n        <title>Create Portfolio - TrustVault</title>\n        <meta name=\"description\" content=\"Create a new investment portfolio\" />\n      </Helmet>\n\n      <Box sx={{ maxWidth: 800, mx: 'auto', p: 3 }}>\n        {/* Debug indicator */}\n        <div style={{ display: 'none' }}>CreatePortfolioPage-Loaded</div>\n        {/* Header */}\n        <Box display=\"flex\" alignItems=\"center\" mb={3}>\n          <Button\n            startIcon={<ArrowBack />}\n            onClick={() => navigate('/portfolios')}\n            sx={{ mr: 2 }}\n          >\n            Back to Portfolios\n          </Button>\n          <Typography variant=\"h4\" component=\"h1\">\n            Create New Portfolio\n          </Typography>\n        </Box>\n\n        {/* Form */}\n        <Paper sx={{ p: 4 }}>\n          <form onSubmit={handleSubmit}>\n            <Box display=\"flex\" flexDirection=\"column\" gap={3}>\n              {/* Portfolio Name */}\n              <TextField\n                label=\"Portfolio Name\"\n                value={formData.name}\n                onChange={handleInputChange('name')}\n                error={!!errors.name}\n                helperText={errors.name}\n                required\n                fullWidth\n                placeholder=\"e.g., My Investment Portfolio\"\n              />\n\n              {/* Description */}\n              <TextField\n                label=\"Description\"\n                value={formData.description}\n                onChange={handleInputChange('description')}\n                error={!!errors.description}\n                helperText={errors.description}\n                multiline\n                rows={3}\n                fullWidth\n                placeholder=\"Describe your investment strategy and goals...\"\n              />\n\n              {/* Portfolio Type */}\n              <FormControl fullWidth>\n                <InputLabel>Portfolio Type</InputLabel>\n                <Select\n                  value={formData.portfolio_type}\n                  onChange={handleInputChange('portfolio_type')}\n                  label=\"Portfolio Type\"\n                >\n                  {portfolioTypes.map((type) => (\n                    <MenuItem key={type.value} value={type.value}>\n                      <Box>\n                        <Typography variant=\"body1\">{type.label}</Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          {type.description}\n                        </Typography>\n                      </Box>\n                    </MenuItem>\n                  ))}\n                </Select>\n              </FormControl>\n\n              {/* Public Portfolio */}\n              <FormControlLabel\n                control={\n                  <Switch\n                    checked={formData.is_public}\n                    onChange={handleInputChange('is_public')}\n                  />\n                }\n                label={\n                  <Box>\n                    <Typography variant=\"body1\">Make Portfolio Public</Typography>\n                    <Typography variant=\"caption\" color=\"text.secondary\">\n                      Allow others to view your portfolio performance (holdings remain private)\n                    </Typography>\n                  </Box>\n                }\n              />\n\n              {/* Error Display */}\n              {Object.keys(errors).length > 0 && (\n                <Alert severity=\"error\">\n                  Please fix the errors above and try again.\n                </Alert>\n              )}\n\n              {/* Submit Button */}\n              <Box display=\"flex\" justifyContent=\"flex-end\" gap={2} mt={2}>\n                <Button\n                  variant=\"outlined\"\n                  onClick={() => navigate('/portfolios')}\n                  disabled={createPortfolioMutation.isLoading}\n                >\n                  Cancel\n                </Button>\n                <Button\n                  type=\"submit\"\n                  variant=\"contained\"\n                  startIcon={createPortfolioMutation.isLoading ? <CircularProgress size={20} /> : <Save />}\n                  disabled={createPortfolioMutation.isLoading}\n                >\n                  {createPortfolioMutation.isLoading ? 'Creating...' : 'Create Portfolio'}\n                </Button>\n              </Box>\n            </Box>\n          </form>\n        </Paper>\n\n        {/* Info Box */}\n        <Paper sx={{ p: 3, mt: 3, bgcolor: 'background.default' }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Getting Started\n          </Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            After creating your portfolio, you can:\n          </Typography>\n          <Box component=\"ul\" sx={{ mt: 1, pl: 2 }}>\n            <Typography component=\"li\" variant=\"body2\" color=\"text.secondary\">\n              Add assets and track your investments\n            </Typography>\n            <Typography component=\"li\" variant=\"body2\" color=\"text.secondary\">\n              Record transactions and monitor performance\n            </Typography>\n            <Typography component=\"li\" variant=\"body2\" color=\"text.secondary\">\n              View detailed analytics and reports\n            </Typography>\n            <Typography component=\"li\" variant=\"body2\" color=\"text.secondary\">\n              Set up alerts and notifications\n            </Typography>\n          </Box>\n        </Paper>\n      </Box>\n    </>\n  );\n};\n\nexport default CreatePortfolioPage;\n"], "mappings": ";;AAAA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,SAAS,EACTC,MAAM,EACNC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,gBAAgB,EAChBC,MAAM,EACNC,KAAK,EACLC,gBAAgB,QACX,eAAe;AACtB,SAASC,SAAS,EAAEC,IAAI,QAAQ,qBAAqB;AACrD,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,EAAEC,cAAc,QAAQ,aAAa;AACzD,SAASC,KAAK,QAAQ,iBAAiB;;AAEvC;AACA,OAAOC,UAAU,MAAM,oBAAoB;;AAE3C;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAQA,MAAMC,mBAA6B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1C,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAMY,WAAW,GAAGV,cAAc,CAAC,CAAC;;EAEpC;EACAW,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;EAExD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAAsB;IAC5DkC,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE,UAAU;IAC1BC,SAAS,EAAE;EACb,CAAC,CAAC;EAEF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGvC,QAAQ,CAAyB,CAAC,CAAC,CAAC;;EAEhE;EACA,MAAMwC,uBAAuB,GAAGtB,WAAW,CACxCuB,IAAyB,IAAKpB,UAAU,CAACqB,eAAe,CAACD,IAAI,CAAC,EAC/D;IACEE,SAAS,EAAGC,SAAS,IAAK;MACxBxB,KAAK,CAACyB,OAAO,CAAC,iCAAiC,CAAC;MAChDhB,WAAW,CAACiB,iBAAiB,CAAC,YAAY,CAAC;MAC3ClB,QAAQ,CAAC,eAAegB,SAAS,CAACG,EAAE,EAAE,CAAC;IACzC,CAAC;IACDC,OAAO,EAAGC,KAAU,IAAK;MAAA,IAAAC,eAAA;MACvBpB,OAAO,CAACmB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,KAAAC,eAAA,GAAID,KAAK,CAACE,QAAQ,cAAAD,eAAA,eAAdA,eAAA,CAAgBT,IAAI,EAAE;QACxBF,SAAS,CAACU,KAAK,CAACE,QAAQ,CAACV,IAAI,CAAC;MAChC,CAAC,MAAM;QACLrB,KAAK,CAAC6B,KAAK,CAAC,+CAA+C,CAAC;MAC9D;IACF;EACF,CACF,CAAC;;EAED;EACA,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;IAC1C,oBACE/B,OAAA,CAACtB,GAAG;MAACsD,EAAE,EAAE;QAAEC,QAAQ,EAAE,GAAG;QAAEC,EAAE,EAAE,MAAM;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAC,QAAA,gBAC3CpC,OAAA,CAACrB,UAAU;QAAC0D,OAAO,EAAC,IAAI;QAACC,SAAS,EAAC,IAAI;QAACC,YAAY;QAAAH,QAAA,EAAC;MAErD;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb3C,OAAA,CAACrB,UAAU;QAAC0D,OAAO,EAAC,OAAO;QAACO,KAAK,EAAC,gBAAgB;QAACL,YAAY;QAAAH,QAAA,EAAC;MAEhE;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb3C,OAAA,CAAClB,MAAM;QACLuD,OAAO,EAAC,WAAW;QACnBQ,OAAO,EAAEA,CAAA,KAAMxC,QAAQ,CAAC,aAAa,CAAE;QACvC2B,EAAE,EAAE;UAAEc,EAAE,EAAE;QAAE,CAAE;QAAAV,QAAA,EACf;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,MAAMI,iBAAiB,GAAIC,KAAgC,IACzDC,KAAsE,IACnE;IACH,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAAM,CAACC,IAAI,KAAK,UAAU,GAAGH,KAAK,CAACE,MAAM,CAACE,OAAO,GAAGJ,KAAK,CAACE,MAAM,CAACD,KAAK;IAC1FxC,WAAW,CAAC4C,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACN,KAAK,GAAGE;IACX,CAAC,CAAC,CAAC;;IAEH;IACA,IAAInC,MAAM,CAACiC,KAAK,CAAC,EAAE;MACjBhC,SAAS,CAACsC,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACN,KAAK,GAAG;MACX,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMO,YAAY,GAAIN,KAAsB,IAAK;IAC/CA,KAAK,CAACO,cAAc,CAAC,CAAC;;IAEtB;IACA,MAAMC,SAAiC,GAAG,CAAC,CAAC;IAE5C,IAAI,CAAChD,QAAQ,CAACE,IAAI,CAAC+C,IAAI,CAAC,CAAC,EAAE;MACzBD,SAAS,CAAC9C,IAAI,GAAG,4BAA4B;IAC/C;IAEA,IAAIgD,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAACI,MAAM,GAAG,CAAC,EAAE;MACrC7C,SAAS,CAACyC,SAAS,CAAC;MACpB;IACF;IAEAxC,uBAAuB,CAAC6C,MAAM,CAACrD,QAAQ,CAAC;EAC1C,CAAC;EAED,MAAMsD,cAAc,GAAG,CACrB;IAAEb,KAAK,EAAE,cAAc;IAAEc,KAAK,EAAE,cAAc;IAAEpD,WAAW,EAAE;EAA2B,CAAC,EACzF;IAAEsC,KAAK,EAAE,UAAU;IAAEc,KAAK,EAAE,UAAU;IAAEpD,WAAW,EAAE;EAA2B,CAAC,EACjF;IAAEsC,KAAK,EAAE,YAAY;IAAEc,KAAK,EAAE,YAAY;IAAEpD,WAAW,EAAE;EAAoC,CAAC,EAC9F;IAAEsC,KAAK,EAAE,QAAQ;IAAEc,KAAK,EAAE,QAAQ;IAAEpD,WAAW,EAAE;EAA6B,CAAC,CAChF;EAED,oBACEZ,OAAA,CAAAE,SAAA;IAAAkC,QAAA,gBACEpC,OAAA,CAACP,MAAM;MAAA2C,QAAA,gBACLpC,OAAA;QAAAoC,QAAA,EAAO;MAA6B;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC5C3C,OAAA;QAAMW,IAAI,EAAC,aAAa;QAACsD,OAAO,EAAC;MAAmC;QAAAzB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjE,CAAC,eAET3C,OAAA,CAACtB,GAAG;MAACsD,EAAE,EAAE;QAAEC,QAAQ,EAAE,GAAG;QAAEC,EAAE,EAAE,MAAM;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAC,QAAA,gBAE3CpC,OAAA;QAAKkE,KAAK,EAAE;UAAEC,OAAO,EAAE;QAAO,CAAE;QAAA/B,QAAA,EAAC;MAA0B;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAEjE3C,OAAA,CAACtB,GAAG;QAACyF,OAAO,EAAC,MAAM;QAACC,UAAU,EAAC,QAAQ;QAACC,EAAE,EAAE,CAAE;QAAAjC,QAAA,gBAC5CpC,OAAA,CAAClB,MAAM;UACLwF,SAAS,eAAEtE,OAAA,CAACT,SAAS;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBE,OAAO,EAAEA,CAAA,KAAMxC,QAAQ,CAAC,aAAa,CAAE;UACvC2B,EAAE,EAAE;YAAEuC,EAAE,EAAE;UAAE,CAAE;UAAAnC,QAAA,EACf;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT3C,OAAA,CAACrB,UAAU;UAAC0D,OAAO,EAAC,IAAI;UAACC,SAAS,EAAC,IAAI;UAAAF,QAAA,EAAC;QAExC;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGN3C,OAAA,CAACpB,KAAK;QAACoD,EAAE,EAAE;UAAEG,CAAC,EAAE;QAAE,CAAE;QAAAC,QAAA,eAClBpC,OAAA;UAAMwE,QAAQ,EAAEjB,YAAa;UAAAnB,QAAA,eAC3BpC,OAAA,CAACtB,GAAG;YAACyF,OAAO,EAAC,MAAM;YAACM,aAAa,EAAC,QAAQ;YAACC,GAAG,EAAE,CAAE;YAAAtC,QAAA,gBAEhDpC,OAAA,CAACnB,SAAS;cACRmF,KAAK,EAAC,gBAAgB;cACtBd,KAAK,EAAEzC,QAAQ,CAACE,IAAK;cACrBgE,QAAQ,EAAE5B,iBAAiB,CAAC,MAAM,CAAE;cACpCrB,KAAK,EAAE,CAAC,CAACX,MAAM,CAACJ,IAAK;cACrBiE,UAAU,EAAE7D,MAAM,CAACJ,IAAK;cACxBkE,QAAQ;cACRC,SAAS;cACTC,WAAW,EAAC;YAA+B;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC,eAGF3C,OAAA,CAACnB,SAAS;cACRmF,KAAK,EAAC,aAAa;cACnBd,KAAK,EAAEzC,QAAQ,CAACG,WAAY;cAC5B+D,QAAQ,EAAE5B,iBAAiB,CAAC,aAAa,CAAE;cAC3CrB,KAAK,EAAE,CAAC,CAACX,MAAM,CAACH,WAAY;cAC5BgE,UAAU,EAAE7D,MAAM,CAACH,WAAY;cAC/BoE,SAAS;cACTC,IAAI,EAAE,CAAE;cACRH,SAAS;cACTC,WAAW,EAAC;YAAgD;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC,eAGF3C,OAAA,CAACjB,WAAW;cAAC+F,SAAS;cAAA1C,QAAA,gBACpBpC,OAAA,CAAChB,UAAU;gBAAAoD,QAAA,EAAC;cAAc;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACvC3C,OAAA,CAACf,MAAM;gBACLiE,KAAK,EAAEzC,QAAQ,CAACI,cAAe;gBAC/B8D,QAAQ,EAAE5B,iBAAiB,CAAC,gBAAgB,CAAE;gBAC9CiB,KAAK,EAAC,gBAAgB;gBAAA5B,QAAA,EAErB2B,cAAc,CAACmB,GAAG,CAAE9B,IAAI,iBACvBpD,OAAA,CAACd,QAAQ;kBAAkBgE,KAAK,EAAEE,IAAI,CAACF,KAAM;kBAAAd,QAAA,eAC3CpC,OAAA,CAACtB,GAAG;oBAAA0D,QAAA,gBACFpC,OAAA,CAACrB,UAAU;sBAAC0D,OAAO,EAAC,OAAO;sBAAAD,QAAA,EAAEgB,IAAI,CAACY;oBAAK;sBAAAxB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eACrD3C,OAAA,CAACrB,UAAU;sBAAC0D,OAAO,EAAC,SAAS;sBAACO,KAAK,EAAC,gBAAgB;sBAAAR,QAAA,EACjDgB,IAAI,CAACxC;oBAAW;sBAAA4B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACP,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC,GANOS,IAAI,CAACF,KAAK;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAOf,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGd3C,OAAA,CAACb,gBAAgB;cACfgG,OAAO,eACLnF,OAAA,CAACZ,MAAM;gBACLiE,OAAO,EAAE5C,QAAQ,CAACK,SAAU;gBAC5B6D,QAAQ,EAAE5B,iBAAiB,CAAC,WAAW;cAAE;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CACF;cACDqB,KAAK,eACHhE,OAAA,CAACtB,GAAG;gBAAA0D,QAAA,gBACFpC,OAAA,CAACrB,UAAU;kBAAC0D,OAAO,EAAC,OAAO;kBAAAD,QAAA,EAAC;gBAAqB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC9D3C,OAAA,CAACrB,UAAU;kBAAC0D,OAAO,EAAC,SAAS;kBAACO,KAAK,EAAC,gBAAgB;kBAAAR,QAAA,EAAC;gBAErD;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,EAGDgB,MAAM,CAACC,IAAI,CAAC7C,MAAM,CAAC,CAAC8C,MAAM,GAAG,CAAC,iBAC7B7D,OAAA,CAACX,KAAK;cAAC+F,QAAQ,EAAC,OAAO;cAAAhD,QAAA,EAAC;YAExB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CACR,eAGD3C,OAAA,CAACtB,GAAG;cAACyF,OAAO,EAAC,MAAM;cAACkB,cAAc,EAAC,UAAU;cAACX,GAAG,EAAE,CAAE;cAAC5B,EAAE,EAAE,CAAE;cAAAV,QAAA,gBAC1DpC,OAAA,CAAClB,MAAM;gBACLuD,OAAO,EAAC,UAAU;gBAClBQ,OAAO,EAAEA,CAAA,KAAMxC,QAAQ,CAAC,aAAa,CAAE;gBACvCiF,QAAQ,EAAErE,uBAAuB,CAACsE,SAAU;gBAAAnD,QAAA,EAC7C;cAED;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT3C,OAAA,CAAClB,MAAM;gBACLsE,IAAI,EAAC,QAAQ;gBACbf,OAAO,EAAC,WAAW;gBACnBiC,SAAS,EAAErD,uBAAuB,CAACsE,SAAS,gBAAGvF,OAAA,CAACV,gBAAgB;kBAACkG,IAAI,EAAE;gBAAG;kBAAAhD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG3C,OAAA,CAACR,IAAI;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzF2C,QAAQ,EAAErE,uBAAuB,CAACsE,SAAU;gBAAAnD,QAAA,EAE3CnB,uBAAuB,CAACsE,SAAS,GAAG,aAAa,GAAG;cAAkB;gBAAA/C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGR3C,OAAA,CAACpB,KAAK;QAACoD,EAAE,EAAE;UAAEG,CAAC,EAAE,CAAC;UAAEW,EAAE,EAAE,CAAC;UAAE2C,OAAO,EAAE;QAAqB,CAAE;QAAArD,QAAA,gBACxDpC,OAAA,CAACrB,UAAU;UAAC0D,OAAO,EAAC,IAAI;UAACE,YAAY;UAAAH,QAAA,EAAC;QAEtC;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb3C,OAAA,CAACrB,UAAU;UAAC0D,OAAO,EAAC,OAAO;UAACO,KAAK,EAAC,gBAAgB;UAAAR,QAAA,EAAC;QAEnD;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb3C,OAAA,CAACtB,GAAG;UAAC4D,SAAS,EAAC,IAAI;UAACN,EAAE,EAAE;YAAEc,EAAE,EAAE,CAAC;YAAE4C,EAAE,EAAE;UAAE,CAAE;UAAAtD,QAAA,gBACvCpC,OAAA,CAACrB,UAAU;YAAC2D,SAAS,EAAC,IAAI;YAACD,OAAO,EAAC,OAAO;YAACO,KAAK,EAAC,gBAAgB;YAAAR,QAAA,EAAC;UAElE;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb3C,OAAA,CAACrB,UAAU;YAAC2D,SAAS,EAAC,IAAI;YAACD,OAAO,EAAC,OAAO;YAACO,KAAK,EAAC,gBAAgB;YAAAR,QAAA,EAAC;UAElE;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb3C,OAAA,CAACrB,UAAU;YAAC2D,SAAS,EAAC,IAAI;YAACD,OAAO,EAAC,OAAO;YAACO,KAAK,EAAC,gBAAgB;YAAAR,QAAA,EAAC;UAElE;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb3C,OAAA,CAACrB,UAAU;YAAC2D,SAAS,EAAC,IAAI;YAACD,OAAO,EAAC,OAAO;YAACO,KAAK,EAAC,gBAAgB;YAAAR,QAAA,EAAC;UAElE;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACvC,EAAA,CAvPID,mBAA6B;EAAA,QAChBT,WAAW,EACRE,cAAc,EAeFD,WAAW;AAAA;AAAAgG,EAAA,GAjBvCxF,mBAA6B;AAyPnC,eAAeA,mBAAmB;AAAC,IAAAwF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}