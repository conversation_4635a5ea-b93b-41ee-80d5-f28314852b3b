{"ast": null, "code": "// TrustVault - API Service\nimport axios from'axios';import{toast}from'react-hot-toast';// Types\nclass ApiService{constructor(){this.api=void 0;this.baseURL=void 0;this.baseURL=process.env.REACT_APP_API_URL||'/api/v1';this.api=axios.create({baseURL:this.baseURL,timeout:30000,headers:{'Content-Type':'application/json'}});this.setupInterceptors();}setupInterceptors(){// Request interceptor\nthis.api.interceptors.request.use(config=>{// Add auth token if available\nconst token=this.getAccessToken();if(token){config.headers.Authorization=`Bearer ${token}`;}// Add security headers\nconfig.headers['X-Requested-With']='XMLHttpRequest';config.headers['X-Client-Version']='1.0.0';// Log request in development\nif(process.env.NODE_ENV==='development'){var _config$method;console.log(`API Request: ${(_config$method=config.method)===null||_config$method===void 0?void 0:_config$method.toUpperCase()} ${config.url}`);}return config;},error=>{return Promise.reject(error);});// Response interceptor\nthis.api.interceptors.response.use(response=>{return response;},async error=>{var _error$response;const originalRequest=error.config;// Handle 401 errors (unauthorized)\nif(((_error$response=error.response)===null||_error$response===void 0?void 0:_error$response.status)===401&&!originalRequest._retry){originalRequest._retry=true;try{// Try to refresh token\nconst refreshToken=this.getRefreshToken();if(refreshToken){const response=await this.refreshAccessToken(refreshToken);this.setTokens(response.data.access_token,refreshToken);// Retry original request\noriginalRequest.headers.Authorization=`Bearer ${response.data.access_token}`;return this.api(originalRequest);}}catch(refreshError){// Refresh failed, redirect to login\nthis.clearTokens();window.location.href='/login';return Promise.reject(refreshError);}}// Handle other errors\nthis.handleApiError(error);return Promise.reject(error);});}handleApiError(error){var _error$response2,_error$response2$data,_error$response3;const message=((_error$response2=error.response)===null||_error$response2===void 0?void 0:(_error$response2$data=_error$response2.data)===null||_error$response2$data===void 0?void 0:_error$response2$data.message)||error.message||'An error occurred';// Don't show toast for certain errors\nconst silentErrors=[401,403];if(!silentErrors.includes((_error$response3=error.response)===null||_error$response3===void 0?void 0:_error$response3.status)){toast.error(message);}// Log error in development\nif(process.env.NODE_ENV==='development'){var _error$response4;console.error('API Error:',((_error$response4=error.response)===null||_error$response4===void 0?void 0:_error$response4.data)||error.message);}}// Token management\ngetAccessToken(){return localStorage.getItem('access_token');}getRefreshToken(){return localStorage.getItem('refresh_token');}setTokens(accessToken,refreshToken){localStorage.setItem('access_token',accessToken);localStorage.setItem('refresh_token',refreshToken);}clearTokens(){localStorage.removeItem('access_token');localStorage.removeItem('refresh_token');}async refreshAccessToken(refreshToken){return this.api.post('/auth/token/refresh/',{refresh:refreshToken});}// Authentication endpoints\nasync login(credentials){const response=await this.api.post('/auth/login/',credentials);if(response.data.access_token){this.setTokens(response.data.access_token,response.data.refresh_token);}return response.data;}async register(data){const response=await this.api.post('/auth/register/',data);return response.data;}async logout(){const refreshToken=this.getRefreshToken();try{await this.api.post('/auth/logout/',{refresh_token:refreshToken});}finally{this.clearTokens();}}async getCurrentUser(){const response=await this.api.get('/auth/profile/');return response.data;}async updateProfile(data){const response=await this.api.put('/auth/profile/',data);return response.data;}async changePassword(data){const response=await this.api.post('/auth/change-password/',data);return response.data;}// Portfolio endpoints\nasync getPortfolios(){const response=await this.api.get('/portfolio/');return response.data;}async getPortfolio(id){const response=await this.api.get(`/portfolio/${id}/`);return response.data;}async createPortfolio(data){const response=await this.api.post('/portfolio/',data);return response.data;}async updatePortfolio(id,data){const response=await this.api.put(`/portfolio/${id}/`,data);return response.data;}async deletePortfolio(id){await this.api.delete(`/portfolio/${id}/`);}// Asset endpoints\nasync getAssets(params){const response=await this.api.get('/portfolio/assets/',{params});return response.data;}// Holdings endpoints\nasync getHoldings(portfolioId){const response=await this.api.get(`/portfolio/${portfolioId}/holdings/`);return response.data;}async createHolding(portfolioId,data){const response=await this.api.post(`/portfolio/${portfolioId}/holdings/`,data);return response.data;}// Transaction endpoints\nasync getTransactions(portfolioId){const response=await this.api.get(`/portfolio/${portfolioId}/transactions/`);return response.data;}async createTransaction(portfolioId,data){const response=await this.api.post(`/portfolio/${portfolioId}/transactions/`,data);return response.data;}// Security endpoints\nasync getSecurityDashboard(){const response=await this.api.get('/security/dashboard/');return response.data;}async getSecurityEvents(params){const response=await this.api.get('/security/events/',{params});return response.data;}async getAuditLogs(params){const response=await this.api.get('/security/audit-logs/',{params});return response.data;}// Health check\nasync healthCheck(){const response=await this.api.get('/core/status/');return response.data;}// Generic request method\nasync request(config){const response=await this.api.request(config);return response.data;}}// Create singleton instance\nconst apiService=new ApiService();export default apiService;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}