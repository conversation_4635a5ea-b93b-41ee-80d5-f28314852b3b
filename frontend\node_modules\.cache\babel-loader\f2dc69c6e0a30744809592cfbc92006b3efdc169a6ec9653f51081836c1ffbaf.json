{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"color\", \"value\", \"valueBuffer\", \"variant\"];\nlet _ = t => t,\n  _t,\n  _t2,\n  _t3,\n  _t4,\n  _t5,\n  _t6;\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { keyframes, css } from '@mui/system';\nimport { darken, lighten } from '@mui/system/colorManipulator';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport capitalize from '../utils/capitalize';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport { getLinearProgressUtilityClass } from './linearProgressClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst TRANSITION_DURATION = 4; // seconds\nconst indeterminate1Keyframe = keyframes(_t || (_t = _`\n  0% {\n    left: -35%;\n    right: 100%;\n  }\n\n  60% {\n    left: 100%;\n    right: -90%;\n  }\n\n  100% {\n    left: 100%;\n    right: -90%;\n  }\n`));\nconst indeterminate2Keyframe = keyframes(_t2 || (_t2 = _`\n  0% {\n    left: -200%;\n    right: 100%;\n  }\n\n  60% {\n    left: 107%;\n    right: -8%;\n  }\n\n  100% {\n    left: 107%;\n    right: -8%;\n  }\n`));\nconst bufferKeyframe = keyframes(_t3 || (_t3 = _`\n  0% {\n    opacity: 1;\n    background-position: 0 -23px;\n  }\n\n  60% {\n    opacity: 0;\n    background-position: 0 -23px;\n  }\n\n  100% {\n    opacity: 1;\n    background-position: -200px -23px;\n  }\n`));\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    variant,\n    color\n  } = ownerState;\n  const slots = {\n    root: ['root', `color${capitalize(color)}`, variant],\n    dashed: ['dashed', `dashedColor${capitalize(color)}`],\n    bar1: ['bar', `barColor${capitalize(color)}`, (variant === 'indeterminate' || variant === 'query') && 'bar1Indeterminate', variant === 'determinate' && 'bar1Determinate', variant === 'buffer' && 'bar1Buffer'],\n    bar2: ['bar', variant !== 'buffer' && `barColor${capitalize(color)}`, variant === 'buffer' && `color${capitalize(color)}`, (variant === 'indeterminate' || variant === 'query') && 'bar2Indeterminate', variant === 'buffer' && 'bar2Buffer']\n  };\n  return composeClasses(slots, getLinearProgressUtilityClass, classes);\n};\nconst getColorShade = (theme, color) => {\n  if (color === 'inherit') {\n    return 'currentColor';\n  }\n  if (theme.vars) {\n    return theme.vars.palette.LinearProgress[`${color}Bg`];\n  }\n  return theme.palette.mode === 'light' ? lighten(theme.palette[color].main, 0.62) : darken(theme.palette[color].main, 0.5);\n};\nconst LinearProgressRoot = styled('span', {\n  name: 'MuiLinearProgress',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[`color${capitalize(ownerState.color)}`], styles[ownerState.variant]];\n  }\n})(_ref => {\n  let {\n    ownerState,\n    theme\n  } = _ref;\n  return _extends({\n    position: 'relative',\n    overflow: 'hidden',\n    display: 'block',\n    height: 4,\n    zIndex: 0,\n    // Fix Safari's bug during composition of different paint.\n    '@media print': {\n      colorAdjust: 'exact'\n    },\n    backgroundColor: getColorShade(theme, ownerState.color)\n  }, ownerState.color === 'inherit' && ownerState.variant !== 'buffer' && {\n    backgroundColor: 'none',\n    '&::before': {\n      content: '\"\"',\n      position: 'absolute',\n      left: 0,\n      top: 0,\n      right: 0,\n      bottom: 0,\n      backgroundColor: 'currentColor',\n      opacity: 0.3\n    }\n  }, ownerState.variant === 'buffer' && {\n    backgroundColor: 'transparent'\n  }, ownerState.variant === 'query' && {\n    transform: 'rotate(180deg)'\n  });\n});\nconst LinearProgressDashed = styled('span', {\n  name: 'MuiLinearProgress',\n  slot: 'Dashed',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.dashed, styles[`dashedColor${capitalize(ownerState.color)}`]];\n  }\n})(_ref2 => {\n  let {\n    ownerState,\n    theme\n  } = _ref2;\n  const backgroundColor = getColorShade(theme, ownerState.color);\n  return _extends({\n    position: 'absolute',\n    marginTop: 0,\n    height: '100%',\n    width: '100%'\n  }, ownerState.color === 'inherit' && {\n    opacity: 0.3\n  }, {\n    backgroundImage: `radial-gradient(${backgroundColor} 0%, ${backgroundColor} 16%, transparent 42%)`,\n    backgroundSize: '10px 10px',\n    backgroundPosition: '0 -23px'\n  });\n}, css(_t4 || (_t4 = _`\n    animation: ${0} 3s infinite linear;\n  `), bufferKeyframe));\nconst LinearProgressBar1 = styled('span', {\n  name: 'MuiLinearProgress',\n  slot: 'Bar1',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.bar, styles[`barColor${capitalize(ownerState.color)}`], (ownerState.variant === 'indeterminate' || ownerState.variant === 'query') && styles.bar1Indeterminate, ownerState.variant === 'determinate' && styles.bar1Determinate, ownerState.variant === 'buffer' && styles.bar1Buffer];\n  }\n})(_ref3 => {\n  let {\n    ownerState,\n    theme\n  } = _ref3;\n  return _extends({\n    width: '100%',\n    position: 'absolute',\n    left: 0,\n    bottom: 0,\n    top: 0,\n    transition: 'transform 0.2s linear',\n    transformOrigin: 'left',\n    backgroundColor: ownerState.color === 'inherit' ? 'currentColor' : (theme.vars || theme).palette[ownerState.color].main\n  }, ownerState.variant === 'determinate' && {\n    transition: `transform .${TRANSITION_DURATION}s linear`\n  }, ownerState.variant === 'buffer' && {\n    zIndex: 1,\n    transition: `transform .${TRANSITION_DURATION}s linear`\n  });\n}, _ref4 => {\n  let {\n    ownerState\n  } = _ref4;\n  return (ownerState.variant === 'indeterminate' || ownerState.variant === 'query') && css(_t5 || (_t5 = _`\n      width: auto;\n      animation: ${0} 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395) infinite;\n    `), indeterminate1Keyframe);\n});\nconst LinearProgressBar2 = styled('span', {\n  name: 'MuiLinearProgress',\n  slot: 'Bar2',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.bar, styles[`barColor${capitalize(ownerState.color)}`], (ownerState.variant === 'indeterminate' || ownerState.variant === 'query') && styles.bar2Indeterminate, ownerState.variant === 'buffer' && styles.bar2Buffer];\n  }\n})(_ref5 => {\n  let {\n    ownerState,\n    theme\n  } = _ref5;\n  return _extends({\n    width: '100%',\n    position: 'absolute',\n    left: 0,\n    bottom: 0,\n    top: 0,\n    transition: 'transform 0.2s linear',\n    transformOrigin: 'left'\n  }, ownerState.variant !== 'buffer' && {\n    backgroundColor: ownerState.color === 'inherit' ? 'currentColor' : (theme.vars || theme).palette[ownerState.color].main\n  }, ownerState.color === 'inherit' && {\n    opacity: 0.3\n  }, ownerState.variant === 'buffer' && {\n    backgroundColor: getColorShade(theme, ownerState.color),\n    transition: `transform .${TRANSITION_DURATION}s linear`\n  });\n}, _ref6 => {\n  let {\n    ownerState\n  } = _ref6;\n  return (ownerState.variant === 'indeterminate' || ownerState.variant === 'query') && css(_t6 || (_t6 = _`\n      width: auto;\n      animation: ${0} 2.1s cubic-bezier(0.165, 0.84, 0.44, 1) 1.15s infinite;\n    `), indeterminate2Keyframe);\n});\n\n/**\n * ## ARIA\n *\n * If the progress bar is describing the loading progress of a particular region of a page,\n * you should use `aria-describedby` to point to the progress bar, and set the `aria-busy`\n * attribute to `true` on that region until it has finished loading.\n */\nconst LinearProgress = /*#__PURE__*/React.forwardRef(function LinearProgress(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiLinearProgress'\n  });\n  const {\n      className,\n      color = 'primary',\n      value,\n      valueBuffer,\n      variant = 'indeterminate'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    color,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  const isRtl = useRtl();\n  const rootProps = {};\n  const inlineStyles = {\n    bar1: {},\n    bar2: {}\n  };\n  if (variant === 'determinate' || variant === 'buffer') {\n    if (value !== undefined) {\n      rootProps['aria-valuenow'] = Math.round(value);\n      rootProps['aria-valuemin'] = 0;\n      rootProps['aria-valuemax'] = 100;\n      let transform = value - 100;\n      if (isRtl) {\n        transform = -transform;\n      }\n      inlineStyles.bar1.transform = `translateX(${transform}%)`;\n    } else if (process.env.NODE_ENV !== 'production') {\n      console.error('MUI: You need to provide a value prop ' + 'when using the determinate or buffer variant of LinearProgress .');\n    }\n  }\n  if (variant === 'buffer') {\n    if (valueBuffer !== undefined) {\n      let transform = (valueBuffer || 0) - 100;\n      if (isRtl) {\n        transform = -transform;\n      }\n      inlineStyles.bar2.transform = `translateX(${transform}%)`;\n    } else if (process.env.NODE_ENV !== 'production') {\n      console.error('MUI: You need to provide a valueBuffer prop ' + 'when using the buffer variant of LinearProgress.');\n    }\n  }\n  return /*#__PURE__*/_jsxs(LinearProgressRoot, _extends({\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    role: \"progressbar\"\n  }, rootProps, {\n    ref: ref\n  }, other, {\n    children: [variant === 'buffer' ? /*#__PURE__*/_jsx(LinearProgressDashed, {\n      className: classes.dashed,\n      ownerState: ownerState\n    }) : null, /*#__PURE__*/_jsx(LinearProgressBar1, {\n      className: classes.bar1,\n      ownerState: ownerState,\n      style: inlineStyles.bar1\n    }), variant === 'determinate' ? null : /*#__PURE__*/_jsx(LinearProgressBar2, {\n      className: classes.bar2,\n      ownerState: ownerState,\n      style: inlineStyles.bar2\n    })]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? LinearProgress.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   * @default 'primary'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['inherit', 'primary', 'secondary']), PropTypes.string]),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The value of the progress indicator for the determinate and buffer variants.\n   * Value between 0 and 100.\n   */\n  value: PropTypes.number,\n  /**\n   * The value for the buffer variant.\n   * Value between 0 and 100.\n   */\n  valueBuffer: PropTypes.number,\n  /**\n   * The variant to use.\n   * Use indeterminate or query when there is no progress value.\n   * @default 'indeterminate'\n   */\n  variant: PropTypes.oneOf(['buffer', 'determinate', 'indeterminate', 'query'])\n} : void 0;\nexport default LinearProgress;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}