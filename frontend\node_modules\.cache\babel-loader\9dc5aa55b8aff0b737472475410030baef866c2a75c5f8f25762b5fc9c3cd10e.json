{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"alignItems\", \"autoFocus\", \"component\", \"children\", \"dense\", \"disableGutters\", \"divider\", \"focusVisibleClassName\", \"selected\", \"className\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha } from '@mui/system/colorManipulator';\nimport styled, { rootShouldForwardProp } from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport ButtonBase from '../ButtonBase';\nimport useEnhancedEffect from '../utils/useEnhancedEffect';\nimport useForkRef from '../utils/useForkRef';\nimport ListContext from '../List/ListContext';\nimport listItemButtonClasses, { getListItemButtonUtilityClass } from './listItemButtonClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const overridesResolver = (props, styles) => {\n  const {\n    ownerState\n  } = props;\n  return [styles.root, ownerState.dense && styles.dense, ownerState.alignItems === 'flex-start' && styles.alignItemsFlexStart, ownerState.divider && styles.divider, !ownerState.disableGutters && styles.gutters];\n};\nconst useUtilityClasses = ownerState => {\n  const {\n    alignItems,\n    classes,\n    dense,\n    disabled,\n    disableGutters,\n    divider,\n    selected\n  } = ownerState;\n  const slots = {\n    root: ['root', dense && 'dense', !disableGutters && 'gutters', divider && 'divider', disabled && 'disabled', alignItems === 'flex-start' && 'alignItemsFlexStart', selected && 'selected']\n  };\n  const composedClasses = composeClasses(slots, getListItemButtonUtilityClass, classes);\n  return _extends({}, classes, composedClasses);\n};\nconst ListItemButtonRoot = styled(ButtonBase, {\n  shouldForwardProp: prop => rootShouldForwardProp(prop) || prop === 'classes',\n  name: 'MuiListItemButton',\n  slot: 'Root',\n  overridesResolver\n})(_ref => {\n  let {\n    theme,\n    ownerState\n  } = _ref;\n  return _extends({\n    display: 'flex',\n    flexGrow: 1,\n    justifyContent: 'flex-start',\n    alignItems: 'center',\n    position: 'relative',\n    textDecoration: 'none',\n    minWidth: 0,\n    boxSizing: 'border-box',\n    textAlign: 'left',\n    paddingTop: 8,\n    paddingBottom: 8,\n    transition: theme.transitions.create('background-color', {\n      duration: theme.transitions.duration.shortest\n    }),\n    '&:hover': {\n      textDecoration: 'none',\n      backgroundColor: (theme.vars || theme).palette.action.hover,\n      // Reset on touch devices, it doesn't add specificity\n      '@media (hover: none)': {\n        backgroundColor: 'transparent'\n      }\n    },\n    [`&.${listItemButtonClasses.selected}`]: {\n      backgroundColor: theme.vars ? `rgba(${theme.vars.palette.primary.mainChannel} / ${theme.vars.palette.action.selectedOpacity})` : alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity),\n      [`&.${listItemButtonClasses.focusVisible}`]: {\n        backgroundColor: theme.vars ? `rgba(${theme.vars.palette.primary.mainChannel} / calc(${theme.vars.palette.action.selectedOpacity} + ${theme.vars.palette.action.focusOpacity}))` : alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity + theme.palette.action.focusOpacity)\n      }\n    },\n    [`&.${listItemButtonClasses.selected}:hover`]: {\n      backgroundColor: theme.vars ? `rgba(${theme.vars.palette.primary.mainChannel} / calc(${theme.vars.palette.action.selectedOpacity} + ${theme.vars.palette.action.hoverOpacity}))` : alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity + theme.palette.action.hoverOpacity),\n      // Reset on touch devices, it doesn't add specificity\n      '@media (hover: none)': {\n        backgroundColor: theme.vars ? `rgba(${theme.vars.palette.primary.mainChannel} / ${theme.vars.palette.action.selectedOpacity})` : alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity)\n      }\n    },\n    [`&.${listItemButtonClasses.focusVisible}`]: {\n      backgroundColor: (theme.vars || theme).palette.action.focus\n    },\n    [`&.${listItemButtonClasses.disabled}`]: {\n      opacity: (theme.vars || theme).palette.action.disabledOpacity\n    }\n  }, ownerState.divider && {\n    borderBottom: `1px solid ${(theme.vars || theme).palette.divider}`,\n    backgroundClip: 'padding-box'\n  }, ownerState.alignItems === 'flex-start' && {\n    alignItems: 'flex-start'\n  }, !ownerState.disableGutters && {\n    paddingLeft: 16,\n    paddingRight: 16\n  }, ownerState.dense && {\n    paddingTop: 4,\n    paddingBottom: 4\n  });\n});\nconst ListItemButton = /*#__PURE__*/React.forwardRef(function ListItemButton(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiListItemButton'\n  });\n  const {\n      alignItems = 'center',\n      autoFocus = false,\n      component = 'div',\n      children,\n      dense = false,\n      disableGutters = false,\n      divider = false,\n      focusVisibleClassName,\n      selected = false,\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const context = React.useContext(ListContext);\n  const childContext = React.useMemo(() => ({\n    dense: dense || context.dense || false,\n    alignItems,\n    disableGutters\n  }), [alignItems, context.dense, dense, disableGutters]);\n  const listItemRef = React.useRef(null);\n  useEnhancedEffect(() => {\n    if (autoFocus) {\n      if (listItemRef.current) {\n        listItemRef.current.focus();\n      } else if (process.env.NODE_ENV !== 'production') {\n        console.error('MUI: Unable to set focus to a ListItemButton whose component has not been rendered.');\n      }\n    }\n  }, [autoFocus]);\n  const ownerState = _extends({}, props, {\n    alignItems,\n    dense: childContext.dense,\n    disableGutters,\n    divider,\n    selected\n  });\n  const classes = useUtilityClasses(ownerState);\n  const handleRef = useForkRef(listItemRef, ref);\n  return /*#__PURE__*/_jsx(ListContext.Provider, {\n    value: childContext,\n    children: /*#__PURE__*/_jsx(ListItemButtonRoot, _extends({\n      ref: handleRef,\n      href: other.href || other.to\n      // `ButtonBase` processes `href` or `to` if `component` is set to 'button'\n      ,\n\n      component: (other.href || other.to) && component === 'div' ? 'button' : component,\n      focusVisibleClassName: clsx(classes.focusVisible, focusVisibleClassName),\n      ownerState: ownerState,\n      className: clsx(classes.root, className)\n    }, other, {\n      classes: classes,\n      children: children\n    }))\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? ListItemButton.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Defines the `align-items` style property.\n   * @default 'center'\n   */\n  alignItems: PropTypes.oneOf(['center', 'flex-start']),\n  /**\n   * If `true`, the list item is focused during the first mount.\n   * Focus will also be triggered if the value changes from false to true.\n   * @default false\n   */\n  autoFocus: PropTypes.bool,\n  /**\n   * The content of the component if a `ListItemSecondaryAction` is used it must\n   * be the last child.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, compact vertical padding designed for keyboard and mouse input is used.\n   * The prop defaults to the value inherited from the parent List component.\n   * @default false\n   */\n  dense: PropTypes.bool,\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the left and right padding is removed.\n   * @default false\n   */\n  disableGutters: PropTypes.bool,\n  /**\n   * If `true`, a 1px light border is added to the bottom of the list item.\n   * @default false\n   */\n  divider: PropTypes.bool,\n  /**\n   * This prop can help identify which element has keyboard focus.\n   * The class name will be applied when the element gains the focus through keyboard interaction.\n   * It's a polyfill for the [CSS :focus-visible selector](https://drafts.csswg.org/selectors-4/#the-focus-visible-pseudo).\n   * The rationale for using this feature [is explained here](https://github.com/WICG/focus-visible/blob/HEAD/explainer.md).\n   * A [polyfill can be used](https://github.com/WICG/focus-visible) to apply a `focus-visible` class to other components\n   * if needed.\n   */\n  focusVisibleClassName: PropTypes.string,\n  /**\n   * @ignore\n   */\n  href: PropTypes.string,\n  /**\n   * Use to apply selected styling.\n   * @default false\n   */\n  selected: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default ListItemButton;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}