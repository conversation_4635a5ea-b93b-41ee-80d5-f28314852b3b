{"ast": null, "code": "// TrustVault - Register Page\nimport React,{useState}from'react';import{Link,useNavigate}from'react-router-dom';import{Box,Card,CardContent,TextField,Button,Typography,FormControlLabel,Checkbox,Alert,InputAdornment,IconButton,Container,LinearProgress}from'@mui/material';import{Visibility,VisibilityOff,Security,Email,Lock,Person,Phone}from'@mui/icons-material';import{useForm}from'react-hook-form';import{yupResolver}from'@hookform/resolvers/yup';import*as yup from'yup';import{toast}from'react-hot-toast';import{Helmet}from'react-helmet-async';// Store\nimport{useAuthStore}from'../../store/authStore';// Types\n// Utils\nimport{validatePasswordStrength}from'../../utils/security';// Validation schema\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const registerSchema=yup.object({email:yup.string().email('Invalid email format').required('Email is required'),username:yup.string().min(3,'Username must be at least 3 characters').max(30,'Username must be less than 30 characters').matches(/^[a-zA-Z0-9_]+$/,'Username can only contain letters, numbers, and underscores').required('Username is required'),first_name:yup.string().min(2,'First name must be at least 2 characters').required('First name is required'),last_name:yup.string().min(2,'Last name must be at least 2 characters').required('Last name is required'),phone_number:yup.string().matches(/^\\+?[1-9]\\d{1,14}$/,'Invalid phone number format').optional(),password:yup.string().test('password-strength','Password does not meet security requirements',value=>{if(!value)return false;const{isValid}=validatePasswordStrength(value);return isValid;}).required('Password is required'),password_confirm:yup.string().oneOf([yup.ref('password')],'Passwords must match').required('Password confirmation is required'),gdpr_consent:yup.boolean().oneOf([true],'You must accept the privacy policy').required('GDPR consent is required'),marketing_consent:yup.boolean().optional()});const RegisterPage=()=>{var _errors$first_name,_errors$last_name,_errors$email,_errors$username,_errors$phone_number,_errors$password,_errors$password_conf;const[showPassword,setShowPassword]=useState(false);const[showPasswordConfirm,setShowPasswordConfirm]=useState(false);const[passwordStrength,setPasswordStrength]=useState({score:0,errors:[]});const{register:registerUser,isLoading,error,clearError}=useAuthStore();const navigate=useNavigate();const{register,handleSubmit,watch,formState:{errors}}=useForm({resolver:yupResolver(registerSchema),defaultValues:{email:'',username:'',first_name:'',last_name:'',phone_number:'',password:'',password_confirm:'',gdpr_consent:false,marketing_consent:false}});const watchPassword=watch('password');React.useEffect(()=>{if(watchPassword){const strength=validatePasswordStrength(watchPassword);setPasswordStrength(strength);}else{setPasswordStrength({score:0,errors:[]});}},[watchPassword]);const onSubmit=async data=>{try{clearError();await registerUser(data);toast.success('Registration successful! Please log in.');navigate('/login');}catch(error){// Error is handled by the store and displayed below\n}};const getPasswordStrengthColor=score=>{if(score<=1)return'error';if(score<=3)return'warning';return'success';};const getPasswordStrengthText=score=>{if(score<=1)return'Weak';if(score<=3)return'Medium';return'Strong';};return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(Helmet,{children:[/*#__PURE__*/_jsx(\"title\",{children:\"Register - TrustVault\"}),/*#__PURE__*/_jsx(\"meta\",{name:\"description\",content:\"Create your secure TrustVault account\"})]}),/*#__PURE__*/_jsx(Container,{maxWidth:\"sm\",children:/*#__PURE__*/_jsxs(Box,{display:\"flex\",flexDirection:\"column\",alignItems:\"center\",justifyContent:\"center\",minHeight:\"100vh\",py:4,children:[/*#__PURE__*/_jsxs(Box,{textAlign:\"center\",mb:4,children:[/*#__PURE__*/_jsx(Security,{sx:{fontSize:64,color:'primary.main',mb:2}}),/*#__PURE__*/_jsx(Typography,{variant:\"h3\",component:\"h1\",gutterBottom:true,children:\"TrustVault\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"text.secondary\",children:\"Create Your Secure Account\"})]}),/*#__PURE__*/_jsx(Card,{sx:{width:'100%',maxWidth:500},children:/*#__PURE__*/_jsxs(CardContent,{sx:{p:4},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",component:\"h2\",textAlign:\"center\",mb:3,children:\"Sign Up\"}),error&&/*#__PURE__*/_jsx(Alert,{severity:\"error\",sx:{mb:3},children:error}),/*#__PURE__*/_jsxs(Box,{component:\"form\",onSubmit:handleSubmit(onSubmit),children:[/*#__PURE__*/_jsxs(Box,{display:\"flex\",gap:2,mb:2,children:[/*#__PURE__*/_jsx(TextField,{...register('first_name'),fullWidth:true,label:\"First Name\",autoComplete:\"given-name\",error:!!errors.first_name,helperText:(_errors$first_name=errors.first_name)===null||_errors$first_name===void 0?void 0:_errors$first_name.message,InputProps:{startAdornment:/*#__PURE__*/_jsx(InputAdornment,{position:\"start\",children:/*#__PURE__*/_jsx(Person,{color:\"action\"})})}}),/*#__PURE__*/_jsx(TextField,{...register('last_name'),fullWidth:true,label:\"Last Name\",autoComplete:\"family-name\",error:!!errors.last_name,helperText:(_errors$last_name=errors.last_name)===null||_errors$last_name===void 0?void 0:_errors$last_name.message})]}),/*#__PURE__*/_jsx(TextField,{...register('email'),fullWidth:true,label:\"Email Address\",type:\"email\",autoComplete:\"email\",margin:\"normal\",error:!!errors.email,helperText:(_errors$email=errors.email)===null||_errors$email===void 0?void 0:_errors$email.message,InputProps:{startAdornment:/*#__PURE__*/_jsx(InputAdornment,{position:\"start\",children:/*#__PURE__*/_jsx(Email,{color:\"action\"})})}}),/*#__PURE__*/_jsx(TextField,{...register('username'),fullWidth:true,label:\"Username\",autoComplete:\"username\",margin:\"normal\",error:!!errors.username,helperText:(_errors$username=errors.username)===null||_errors$username===void 0?void 0:_errors$username.message}),/*#__PURE__*/_jsx(TextField,{...register('phone_number'),fullWidth:true,label:\"Phone Number (Optional)\",type:\"tel\",autoComplete:\"tel\",margin:\"normal\",error:!!errors.phone_number,helperText:(_errors$phone_number=errors.phone_number)===null||_errors$phone_number===void 0?void 0:_errors$phone_number.message,InputProps:{startAdornment:/*#__PURE__*/_jsx(InputAdornment,{position:\"start\",children:/*#__PURE__*/_jsx(Phone,{color:\"action\"})})}}),/*#__PURE__*/_jsx(TextField,{...register('password'),fullWidth:true,label:\"Password\",type:showPassword?'text':'password',autoComplete:\"new-password\",margin:\"normal\",error:!!errors.password,helperText:(_errors$password=errors.password)===null||_errors$password===void 0?void 0:_errors$password.message,InputProps:{startAdornment:/*#__PURE__*/_jsx(InputAdornment,{position:\"start\",children:/*#__PURE__*/_jsx(Lock,{color:\"action\"})}),endAdornment:/*#__PURE__*/_jsx(InputAdornment,{position:\"end\",children:/*#__PURE__*/_jsx(IconButton,{onClick:()=>setShowPassword(!showPassword),edge:\"end\",children:showPassword?/*#__PURE__*/_jsx(VisibilityOff,{}):/*#__PURE__*/_jsx(Visibility,{})})})}}),watchPassword&&/*#__PURE__*/_jsxs(Box,{mt:1,mb:2,children:[/*#__PURE__*/_jsxs(Box,{display:\"flex\",justifyContent:\"space-between\",alignItems:\"center\",mb:1,children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Password Strength\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:`${getPasswordStrengthColor(passwordStrength.score)}.main`,children:getPasswordStrengthText(passwordStrength.score)})]}),/*#__PURE__*/_jsx(LinearProgress,{variant:\"determinate\",value:passwordStrength.score/5*100,color:getPasswordStrengthColor(passwordStrength.score),sx:{height:6,borderRadius:3}}),passwordStrength.errors.length>0&&/*#__PURE__*/_jsx(Box,{mt:1,children:passwordStrength.errors.map((error,index)=>/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",color:\"error\",display:\"block\",children:[\"\\u2022 \",error]},index))})]}),/*#__PURE__*/_jsx(TextField,{...register('password_confirm'),fullWidth:true,label:\"Confirm Password\",type:showPasswordConfirm?'text':'password',autoComplete:\"new-password\",margin:\"normal\",error:!!errors.password_confirm,helperText:(_errors$password_conf=errors.password_confirm)===null||_errors$password_conf===void 0?void 0:_errors$password_conf.message,InputProps:{startAdornment:/*#__PURE__*/_jsx(InputAdornment,{position:\"start\",children:/*#__PURE__*/_jsx(Lock,{color:\"action\"})}),endAdornment:/*#__PURE__*/_jsx(InputAdornment,{position:\"end\",children:/*#__PURE__*/_jsx(IconButton,{onClick:()=>setShowPasswordConfirm(!showPasswordConfirm),edge:\"end\",children:showPasswordConfirm?/*#__PURE__*/_jsx(VisibilityOff,{}):/*#__PURE__*/_jsx(Visibility,{})})})}}),/*#__PURE__*/_jsx(FormControlLabel,{control:/*#__PURE__*/_jsx(Checkbox,{...register('gdpr_consent'),color:\"primary\"}),label:/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[\"I agree to the\",' ',/*#__PURE__*/_jsx(Link,{to:\"/privacy\",style:{color:'inherit'},children:\"Privacy Policy\"}),' ',\"and\",' ',/*#__PURE__*/_jsx(Link,{to:\"/terms\",style:{color:'inherit'},children:\"Terms of Service\"})]}),sx:{mt:2,alignItems:'flex-start'}}),errors.gdpr_consent&&/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"error\",children:errors.gdpr_consent.message}),/*#__PURE__*/_jsx(FormControlLabel,{control:/*#__PURE__*/_jsx(Checkbox,{...register('marketing_consent'),color:\"primary\"}),label:\"I would like to receive marketing communications\",sx:{mt:1,mb:2}}),/*#__PURE__*/_jsx(Button,{type:\"submit\",fullWidth:true,variant:\"contained\",size:\"large\",disabled:isLoading,sx:{mt:2,mb:2,py:1.5},children:isLoading?'Creating Account...':'Create Account'}),/*#__PURE__*/_jsx(Box,{textAlign:\"center\",mt:2,children:/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",color:\"text.secondary\",children:[\"Already have an account?\",' ',/*#__PURE__*/_jsx(Link,{to:\"/login\",style:{color:'inherit',textDecoration:'none',fontWeight:600},children:\"Sign in\"})]})})]})]})})]})})]});};export default RegisterPage;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}