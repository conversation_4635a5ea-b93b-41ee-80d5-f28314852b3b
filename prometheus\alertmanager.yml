# TrustVault - AlertManager Configuration

global:
  smtp_smarthost: 'localhost:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'your_email_password'

# Templates for notifications
templates:
  - '/etc/alertmanager/templates/*.tmpl'

# Route configuration
route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'default-receiver'
  routes:
    # Critical security alerts
    - match:
        severity: critical
      receiver: 'security-team'
      group_wait: 0s
      repeat_interval: 5m
      
    # Authentication alerts
    - match:
        category: authentication
      receiver: 'security-team'
      group_wait: 30s
      repeat_interval: 15m
      
    # Infrastructure alerts
    - match:
        category: infrastructure_security
      receiver: 'ops-team'
      group_wait: 1m
      repeat_interval: 30m

# Receivers configuration
receivers:
  - name: 'default-receiver'
    email_configs:
      - to: '<EMAIL>'
        subject: 'TrustVault Alert: {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Severity: {{ .Labels.severity }}
          Time: {{ .StartsAt }}
          {{ end }}

  - name: 'security-team'
    email_configs:
      - to: '<EMAIL>'
        subject: '🚨 SECURITY ALERT: {{ .GroupLabels.alertname }}'
        body: |
          SECURITY INCIDENT DETECTED
          =========================
          
          {{ range .Alerts }}
          🔴 Alert: {{ .Annotations.summary }}
          📝 Description: {{ .Annotations.description }}
          ⚠️  Severity: {{ .Labels.severity }}
          🕐 Time: {{ .StartsAt }}
          🏷️  Labels: {{ range .Labels.SortedPairs }}{{ .Name }}={{ .Value }} {{ end }}
          {{ end }}
          
          Please investigate immediately.
          Dashboard: http://grafana:3000/d/security-overview
    
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#security-alerts'
        title: '🚨 TrustVault Security Alert'
        text: |
          {{ range .Alerts }}
          *Alert:* {{ .Annotations.summary }}
          *Description:* {{ .Annotations.description }}
          *Severity:* {{ .Labels.severity }}
          *Time:* {{ .StartsAt }}
          {{ end }}
        color: 'danger'

  - name: 'ops-team'
    email_configs:
      - to: '<EMAIL>'
        subject: '⚠️ TrustVault Infrastructure Alert: {{ .GroupLabels.alertname }}'
        body: |
          INFRASTRUCTURE ALERT
          ===================
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Severity: {{ .Labels.severity }}
          Time: {{ .StartsAt }}
          {{ end }}

# Inhibit rules
inhibit_rules:
  # Inhibit any warning-level alert if the same alert is already critical
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'cluster', 'service']

  # Inhibit specific combinations
  - source_match:
      alertname: 'ServiceDown'
    target_match_re:
      alertname: '(HighErrorRate|HighLatency)'
    equal: ['service']
