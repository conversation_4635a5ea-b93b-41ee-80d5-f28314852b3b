{"ast": null, "code": "/* eslint-disable */\n// Inspired by https://github.com/garycourt/murmurhash-js\n// Ported from https://github.com/aappleby/smhasher/blob/61a0530f28277f2e850bfc39600ce61d02b518de/src/MurmurHash2.cpp#L37-L86\nfunction murmur2(str) {\n  // 'm' and 'r' are mixing constants generated offline.\n  // They're not really 'magic', they just happen to work well.\n  // const m = 0x5bd1e995;\n  // const r = 24;\n  // Initialize the hash\n  var h = 0; // Mix 4 bytes at a time into the hash\n\n  var k,\n    i = 0,\n    len = str.length;\n  for (; len >= 4; ++i, len -= 4) {\n    k = str.charCodeAt(i) & 0xff | (str.charCodeAt(++i) & 0xff) << 8 | (str.charCodeAt(++i) & 0xff) << 16 | (str.charCodeAt(++i) & 0xff) << 24;\n    k = /* Math.imul(k, m): */\n    (k & 0xffff) * 0x5bd1e995 + ((k >>> 16) * 0xe995 << 16);\n    k ^= /* k >>> r: */\n    k >>> 24;\n    h = /* Math.imul(k, m): */\n    (k & 0xffff) * 0x5bd1e995 + ((k >>> 16) * 0xe995 << 16) ^ /* Math.imul(h, m): */\n    (h & 0xffff) * 0x5bd1e995 + ((h >>> 16) * 0xe995 << 16);\n  } // Handle the last few bytes of the input array\n\n  switch (len) {\n    case 3:\n      h ^= (str.charCodeAt(i + 2) & 0xff) << 16;\n    case 2:\n      h ^= (str.charCodeAt(i + 1) & 0xff) << 8;\n    case 1:\n      h ^= str.charCodeAt(i) & 0xff;\n      h = /* Math.imul(h, m): */\n      (h & 0xffff) * 0x5bd1e995 + ((h >>> 16) * 0xe995 << 16);\n  } // Do a few final mixes of the hash to ensure the last few\n  // bytes are well-incorporated.\n\n  h ^= h >>> 13;\n  h = /* Math.imul(h, m): */\n  (h & 0xffff) * 0x5bd1e995 + ((h >>> 16) * 0xe995 << 16);\n  return ((h ^ h >>> 15) >>> 0).toString(36);\n}\nexport { murmur2 as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}