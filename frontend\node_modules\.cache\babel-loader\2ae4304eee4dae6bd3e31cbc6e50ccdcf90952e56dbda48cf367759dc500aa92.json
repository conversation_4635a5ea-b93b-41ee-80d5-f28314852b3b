{"ast": null, "code": "/**\n * @license React\n * use-sync-external-store-shim.production.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\nvar React = require(\"react\");\nfunction is(x, y) {\n  return x === y && (0 !== x || 1 / x === 1 / y) || x !== x && y !== y;\n}\nvar objectIs = \"function\" === typeof Object.is ? Object.is : is,\n  useState = React.useState,\n  useEffect = React.useEffect,\n  useLayoutEffect = React.useLayoutEffect,\n  useDebugValue = React.useDebugValue;\nfunction useSyncExternalStore$2(subscribe, getSnapshot) {\n  var value = getSnapshot(),\n    _useState = useState({\n      inst: {\n        value: value,\n        getSnapshot: getSnapshot\n      }\n    }),\n    inst = _useState[0].inst,\n    forceUpdate = _useState[1];\n  useLayoutEffect(function () {\n    inst.value = value;\n    inst.getSnapshot = getSnapshot;\n    checkIfSnapshotChanged(inst) && forceUpdate({\n      inst: inst\n    });\n  }, [subscribe, value, getSnapshot]);\n  useEffect(function () {\n    checkIfSnapshotChanged(inst) && forceUpdate({\n      inst: inst\n    });\n    return subscribe(function () {\n      checkIfSnapshotChanged(inst) && forceUpdate({\n        inst: inst\n      });\n    });\n  }, [subscribe]);\n  useDebugValue(value);\n  return value;\n}\nfunction checkIfSnapshotChanged(inst) {\n  var latestGetSnapshot = inst.getSnapshot;\n  inst = inst.value;\n  try {\n    var nextValue = latestGetSnapshot();\n    return !objectIs(inst, nextValue);\n  } catch (error) {\n    return !0;\n  }\n}\nfunction useSyncExternalStore$1(subscribe, getSnapshot) {\n  return getSnapshot();\n}\nvar shim = \"undefined\" === typeof window || \"undefined\" === typeof window.document || \"undefined\" === typeof window.document.createElement ? useSyncExternalStore$1 : useSyncExternalStore$2;\nexports.useSyncExternalStore = void 0 !== React.useSyncExternalStore ? React.useSyncExternalStore : shim;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}