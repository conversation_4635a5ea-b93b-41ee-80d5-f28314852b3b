# TrustVault - Portfolio URLs

from django.urls import path
from . import views

app_name = 'portfolio'

urlpatterns = [
    # Portfolios
    path('', views.PortfolioListCreateView.as_view(), name='portfolio-list'),
    path('create/', views.PortfolioCreateInfoView.as_view(), name='portfolio-create-info'),  # Temporary fix
    path('<uuid:pk>/', views.PortfolioDetailView.as_view(), name='portfolio-detail'),

    # Assets
    path('assets/', views.AssetListView.as_view(), name='asset-list'),

    # Holdings
    path('<uuid:portfolio_id>/holdings/', views.HoldingListCreateView.as_view(), name='holding-list'),

    # Transactions
    path('<uuid:portfolio_id>/transactions/', views.TransactionListCreateView.as_view(), name='transaction-list'),
]
