# TrustVault - Authentication Unit Tests

import pytest
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework import status
from apps.authentication.models import LoginAttempt, PasswordHistory
from apps.authentication.validators import CustomPasswordValidator

User = get_user_model()


@pytest.mark.django_db
class TestUserModel:
    """Test User model functionality."""

    def test_create_user(self, user_data):
        """Test user creation."""
        user = User.objects.create_user(**user_data)
        
        assert user.email == user_data['email']
        assert user.username == user_data['username']
        assert user.first_name == user_data['first_name']
        assert user.last_name == user_data['last_name']
        assert user.check_password(user_data['password'])
        assert user.gdpr_consent == user_data['gdpr_consent']
        assert user.is_active is True
        assert user.is_staff is False
        assert user.is_superuser is False

    def test_create_superuser(self):
        """Test superuser creation."""
        user = User.objects.create_superuser(
            email='<EMAIL>',
            username='admin',
            password='AdminPass123!',
            first_name='Admin',
            last_name='User'
        )
        
        assert user.is_active is True
        assert user.is_staff is True
        assert user.is_superuser is True

    def test_account_locking(self, user):
        """Test account locking functionality."""
        assert not user.is_account_locked
        
        # Lock account
        user.lock_account(30)
        assert user.is_account_locked
        
        # Unlock account
        user.unlock_account()
        assert not user.is_account_locked
        assert user.failed_login_attempts == 0

    def test_failed_login_increment(self, user):
        """Test failed login attempt increment."""
        initial_attempts = user.failed_login_attempts
        
        user.increment_failed_login()
        assert user.failed_login_attempts == initial_attempts + 1
        
        # Test account locking after 5 attempts
        for _ in range(4):
            user.increment_failed_login()
        
        assert user.is_account_locked


@pytest.mark.django_db
class TestUserRegistration:
    """Test user registration API."""

    def test_valid_registration(self, api_client, user_data):
        """Test valid user registration."""
        user_data['password_confirm'] = user_data['password']
        
        response = api_client.post(
            reverse('authentication:register'),
            data=user_data,
            format='json'
        )
        
        assert response.status_code == status.HTTP_201_CREATED
        assert 'user_id' in response.data
        assert User.objects.filter(email=user_data['email']).exists()

    def test_invalid_email_registration(self, api_client, user_data):
        """Test registration with invalid email."""
        user_data['email'] = 'invalid-email'
        user_data['password_confirm'] = user_data['password']
        
        response = api_client.post(
            reverse('authentication:register'),
            data=user_data,
            format='json'
        )
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'email' in response.data

    def test_password_mismatch_registration(self, api_client, user_data):
        """Test registration with password mismatch."""
        user_data['password_confirm'] = 'DifferentPassword123!'
        
        response = api_client.post(
            reverse('authentication:register'),
            data=user_data,
            format='json'
        )
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST

    def test_missing_gdpr_consent(self, api_client, user_data):
        """Test registration without GDPR consent."""
        user_data['gdpr_consent'] = False
        user_data['password_confirm'] = user_data['password']
        
        response = api_client.post(
            reverse('authentication:register'),
            data=user_data,
            format='json'
        )
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST


@pytest.mark.django_db
class TestUserLogin:
    """Test user login API."""

    def test_valid_login(self, api_client, user):
        """Test valid user login."""
        response = api_client.post(
            reverse('authentication:login'),
            data={
                'email': user.email,
                'password': 'TestPassword123!',
            },
            format='json'
        )
        
        assert response.status_code == status.HTTP_200_OK
        assert 'access_token' in response.data
        assert 'refresh_token' in response.data
        assert 'user' in response.data

    def test_invalid_credentials_login(self, api_client, user):
        """Test login with invalid credentials."""
        response = api_client.post(
            reverse('authentication:login'),
            data={
                'email': user.email,
                'password': 'WrongPassword',
            },
            format='json'
        )
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        
        # Check that failed login attempt was logged
        assert LoginAttempt.objects.filter(
            email_attempted=user.email,
            attempt_type='FAILED_PASSWORD'
        ).exists()

    def test_nonexistent_user_login(self, api_client):
        """Test login with nonexistent user."""
        response = api_client.post(
            reverse('authentication:login'),
            data={
                'email': '<EMAIL>',
                'password': 'SomePassword123!',
            },
            format='json'
        )
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST

    def test_locked_account_login(self, api_client, user):
        """Test login with locked account."""
        user.lock_account(30)
        
        response = api_client.post(
            reverse('authentication:login'),
            data={
                'email': user.email,
                'password': 'TestPassword123!',
            },
            format='json'
        )
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST


@pytest.mark.django_db
class TestPasswordValidator:
    """Test custom password validator."""

    def test_valid_password(self):
        """Test valid password validation."""
        validator = CustomPasswordValidator()
        
        # Should not raise ValidationError
        validator.validate('SecurePassword123!')

    def test_short_password(self):
        """Test password too short."""
        validator = CustomPasswordValidator()
        
        with pytest.raises(Exception):
            validator.validate('Short1!')

    def test_no_uppercase(self):
        """Test password without uppercase."""
        validator = CustomPasswordValidator()
        
        with pytest.raises(Exception):
            validator.validate('securepassword123!')

    def test_no_lowercase(self):
        """Test password without lowercase."""
        validator = CustomPasswordValidator()
        
        with pytest.raises(Exception):
            validator.validate('SECUREPASSWORD123!')

    def test_no_digit(self):
        """Test password without digit."""
        validator = CustomPasswordValidator()
        
        with pytest.raises(Exception):
            validator.validate('SecurePassword!')

    def test_no_special_char(self):
        """Test password without special character."""
        validator = CustomPasswordValidator()
        
        with pytest.raises(Exception):
            validator.validate('SecurePassword123')

    def test_common_pattern(self):
        """Test password with common pattern."""
        validator = CustomPasswordValidator()
        
        with pytest.raises(Exception):
            validator.validate('Password123!')


@pytest.mark.django_db
class TestPasswordHistory:
    """Test password history functionality."""

    def test_password_history_creation(self, user):
        """Test password history is created when password changes."""
        old_password = user.password
        
        # Change password
        user.set_password('NewPassword123!')
        user.save()
        
        # Check password history
        history = PasswordHistory.objects.filter(user=user).first()
        assert history is not None
        assert history.password_hash == old_password
