{"ast": null, "code": "/**\n * Safe chained function.\n *\n * Will only create a new function if needed,\n * otherwise will pass back existing functions or null.\n */\nexport default function createChainedFunction() {\n  for (var _len = arguments.length, funcs = new Array(_len), _key = 0; _key < _len; _key++) {\n    funcs[_key] = arguments[_key];\n  }\n  return funcs.reduce((acc, func) => {\n    if (func == null) {\n      return acc;\n    }\n    return function chainedFunction() {\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n      acc.apply(this, args);\n      func.apply(this, args);\n    };\n  }, () => {});\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}