{"ast": null, "code": "// TrustVault - Portfolio Transactions Page\nimport React,{useState}from'react';import{<PERSON>,<PERSON>po<PERSON>,Button,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Paper,Chip,IconButton,Menu,MenuItem,TextField,FormControl,InputLabel,Select,Grid}from'@mui/material';import{ArrowBack,Add,MoreVert,TrendingUp,TrendingDown}from'@mui/icons-material';import{Helmet}from'react-helmet-async';import{useNavigate,useParams}from'react-router-dom';import{useQuery}from'react-query';// Services\nimport apiService from'../../services/api';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const TransactionsPage=()=>{const navigate=useNavigate();const{id:portfolioId}=useParams();const[anchorEl,setAnchorEl]=useState(null);const[selectedTransaction,setSelectedTransaction]=useState(null);const[filterType,setFilterType]=useState('ALL');const[searchTerm,setSearchTerm]=useState('');// Fetch portfolio details\nconst{data:portfolio}=useQuery(['portfolio',portfolioId],()=>apiService.getPortfolio(portfolioId),{enabled:!!portfolioId});// Fetch transactions\nconst{data:transactions,isLoading}=useQuery(['transactions',portfolioId],()=>apiService.getTransactions(portfolioId),{enabled:!!portfolioId});const handleMenuOpen=(event,transaction)=>{setAnchorEl(event.currentTarget);setSelectedTransaction(transaction);};const handleMenuClose=()=>{setAnchorEl(null);setSelectedTransaction(null);};const getTransactionTypeColor=type=>{switch(type){case'BUY':return'success';case'SELL':return'error';case'DIVIDEND':return'info';case'DEPOSIT':return'primary';case'WITHDRAWAL':return'warning';default:return'default';}};const getTransactionIcon=type=>{switch(type){case'BUY':case'DEPOSIT':return/*#__PURE__*/_jsx(TrendingUp,{});case'SELL':case'WITHDRAWAL':return/*#__PURE__*/_jsx(TrendingDown,{});default:return undefined;}};const formatCurrency=value=>{return new Intl.NumberFormat('en-US',{style:'currency',currency:'USD'}).format(typeof value==='string'?parseFloat(value):value);};const formatDate=dateString=>{return new Date(dateString).toLocaleDateString('en-US',{year:'numeric',month:'short',day:'numeric'});};const filteredTransactions=(transactions===null||transactions===void 0?void 0:transactions.filter(transaction=>{var _transaction$asset,_transaction$asset2;const matchesType=filterType==='ALL'||transaction.transaction_type===filterType;const matchesSearch=!searchTerm||((_transaction$asset=transaction.asset)===null||_transaction$asset===void 0?void 0:_transaction$asset.symbol.toLowerCase().includes(searchTerm.toLowerCase()))||((_transaction$asset2=transaction.asset)===null||_transaction$asset2===void 0?void 0:_transaction$asset2.name.toLowerCase().includes(searchTerm.toLowerCase()));return matchesType&&matchesSearch;}))||[];if(!portfolioId){return/*#__PURE__*/_jsx(Box,{sx:{maxWidth:1200,mx:'auto',p:3},children:/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"error\",children:\"Portfolio ID is required\"})});}return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(Helmet,{children:[/*#__PURE__*/_jsx(\"title\",{children:\"Transactions - TrustVault\"}),/*#__PURE__*/_jsx(\"meta\",{name:\"description\",content:\"View portfolio transaction history\"})]}),/*#__PURE__*/_jsxs(Box,{sx:{maxWidth:1200,mx:'auto',p:3},children:[/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",justifyContent:\"space-between\",mb:3,children:[/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",children:[/*#__PURE__*/_jsx(Button,{startIcon:/*#__PURE__*/_jsx(ArrowBack,{}),onClick:()=>navigate(`/portfolios/${portfolioId}`),sx:{mr:2},children:\"Back to Portfolio\"}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",component:\"h1\",children:\"Transaction History\"}),portfolio&&/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:portfolio.name})]})]}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",startIcon:/*#__PURE__*/_jsx(Add,{}),onClick:()=>navigate(`/portfolios/${portfolioId}/add-transaction`),children:\"Add Transaction\"})]}),/*#__PURE__*/_jsx(Paper,{sx:{p:3,mb:3},children:/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,alignItems:\"center\",children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:4,children:/*#__PURE__*/_jsx(TextField,{label:\"Search transactions\",value:searchTerm,onChange:e=>setSearchTerm(e.target.value),fullWidth:true,size:\"small\"})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:3,children:/*#__PURE__*/_jsxs(FormControl,{fullWidth:true,size:\"small\",children:[/*#__PURE__*/_jsx(InputLabel,{children:\"Transaction Type\"}),/*#__PURE__*/_jsxs(Select,{value:filterType,onChange:e=>setFilterType(e.target.value),label:\"Transaction Type\",children:[/*#__PURE__*/_jsx(MenuItem,{value:\"ALL\",children:\"All Types\"}),/*#__PURE__*/_jsx(MenuItem,{value:\"BUY\",children:\"Buy\"}),/*#__PURE__*/_jsx(MenuItem,{value:\"SELL\",children:\"Sell\"}),/*#__PURE__*/_jsx(MenuItem,{value:\"DIVIDEND\",children:\"Dividend\"}),/*#__PURE__*/_jsx(MenuItem,{value:\"DEPOSIT\",children:\"Deposit\"}),/*#__PURE__*/_jsx(MenuItem,{value:\"WITHDRAWAL\",children:\"Withdrawal\"})]})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:2,children:/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",color:\"text.secondary\",children:[filteredTransactions.length,\" transactions\"]})})]})}),/*#__PURE__*/_jsx(Paper,{children:/*#__PURE__*/_jsx(TableContainer,{children:/*#__PURE__*/_jsxs(Table,{children:[/*#__PURE__*/_jsx(TableHead,{children:/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:\"Date\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Type\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Asset\"}),/*#__PURE__*/_jsx(TableCell,{align:\"right\",children:\"Quantity\"}),/*#__PURE__*/_jsx(TableCell,{align:\"right\",children:\"Price\"}),/*#__PURE__*/_jsx(TableCell,{align:\"right\",children:\"Total Amount\"}),/*#__PURE__*/_jsx(TableCell,{align:\"center\",children:\"Actions\"})]})}),/*#__PURE__*/_jsx(TableBody,{children:isLoading?/*#__PURE__*/_jsx(TableRow,{children:/*#__PURE__*/_jsx(TableCell,{colSpan:7,align:\"center\",children:\"Loading transactions...\"})}):filteredTransactions.length===0?/*#__PURE__*/_jsx(TableRow,{children:/*#__PURE__*/_jsx(TableCell,{colSpan:7,align:\"center\",children:/*#__PURE__*/_jsxs(Box,{py:4,children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"text.secondary\",gutterBottom:true,children:\"No Transactions Found\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",gutterBottom:true,children:searchTerm||filterType!=='ALL'?'Try adjusting your filters':'Start by adding your first transaction'}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",startIcon:/*#__PURE__*/_jsx(Add,{}),onClick:()=>navigate(`/portfolios/${portfolioId}/add-transaction`),sx:{mt:2},children:\"Add First Transaction\"})]})})}):filteredTransactions.map(transaction=>/*#__PURE__*/_jsxs(TableRow,{hover:true,children:[/*#__PURE__*/_jsx(TableCell,{children:formatDate(transaction.transaction_date)}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(Chip,{icon:getTransactionIcon(transaction.transaction_type),label:transaction.transaction_type,color:getTransactionTypeColor(transaction.transaction_type),size:\"small\"})}),/*#__PURE__*/_jsx(TableCell,{children:transaction.asset?/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",fontWeight:\"medium\",children:transaction.asset.symbol}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",children:transaction.asset.name})]}):/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Cash Transaction\"})}),/*#__PURE__*/_jsx(TableCell,{align:\"right\",children:transaction.quantity?transaction.quantity.toFixed(4):'-'}),/*#__PURE__*/_jsx(TableCell,{align:\"right\",children:transaction.price?formatCurrency(transaction.price):'-'}),/*#__PURE__*/_jsx(TableCell,{align:\"right\",children:/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",color:['BUY','DEPOSIT'].includes(transaction.transaction_type)?'success.main':'error.main',fontWeight:\"medium\",children:[['BUY','DEPOSIT'].includes(transaction.transaction_type)?'+':'-',formatCurrency(transaction.total_amount)]})}),/*#__PURE__*/_jsx(TableCell,{align:\"center\",children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:e=>handleMenuOpen(e,transaction),children:/*#__PURE__*/_jsx(MoreVert,{})})})]},transaction.id))})]})})}),/*#__PURE__*/_jsxs(Menu,{anchorEl:anchorEl,open:Boolean(anchorEl),onClose:handleMenuClose,children:[/*#__PURE__*/_jsx(MenuItem,{onClick:handleMenuClose,children:\"View Details\"}),/*#__PURE__*/_jsx(MenuItem,{onClick:handleMenuClose,children:\"Edit Transaction\"}),/*#__PURE__*/_jsx(MenuItem,{onClick:handleMenuClose,sx:{color:'error.main'},children:\"Delete Transaction\"})]})]})]});};export default TransactionsPage;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}