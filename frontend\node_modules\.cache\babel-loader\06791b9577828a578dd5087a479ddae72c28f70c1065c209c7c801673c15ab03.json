{"ast": null, "code": "// TrustVault - Main App Component\nimport React,{useEffect}from'react';import{Routes,Route,Navigate}from'react-router-dom';import{Box,CircularProgress}from'@mui/material';import{Helmet}from'react-helmet-async';// Store\nimport{useAuthStore}from'./store/authStore';// Components\nimport Layout from'./components/Layout/Layout';import ProtectedRoute from'./components/Auth/ProtectedRoute';// Pages\nimport LoginPage from'./pages/Auth/LoginPage';import RegisterPage from'./pages/Auth/RegisterPage';import DashboardPage from'./pages/Dashboard/DashboardPage';import PortfoliosPage from'./pages/Portfolio/PortfoliosPage';import PortfolioDetailPage from'./pages/Portfolio/PortfolioDetailPage';import CreatePortfolioPage from'./pages/Portfolio/CreatePortfolioPage';import SecurityPage from'./pages/Security/SecurityPage';import ProfilePage from'./pages/Profile/ProfilePage';import NotFoundPage from'./pages/NotFoundPage';// Utils\nimport{validateEnvironment}from'./utils/security';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const App=()=>{const{isAuthenticated,isLoading,loadUser}=useAuthStore();useEffect(()=>{// Validate environment security\nvalidateEnvironment();// Load user if token exists\nloadUser();},[loadUser]);// Show loading spinner while checking authentication\nif(isLoading){return/*#__PURE__*/_jsx(Box,{display:\"flex\",justifyContent:\"center\",alignItems:\"center\",minHeight:\"100vh\",children:/*#__PURE__*/_jsx(CircularProgress,{size:40})});}return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(Helmet,{children:[/*#__PURE__*/_jsx(\"title\",{children:\"TrustVault - Secure Portfolio Management\"}),/*#__PURE__*/_jsx(\"meta\",{name:\"description\",content:\"Secure portfolio management platform with advanced cybersecurity features\"}),/*#__PURE__*/_jsx(\"meta\",{name:\"keywords\",content:\"portfolio, investment, security, cybersecurity, finance\"}),/*#__PURE__*/_jsx(\"meta\",{name:\"author\",content:\"TrustVault Team\"}),/*#__PURE__*/_jsx(\"meta\",{httpEquiv:\"X-Content-Type-Options\",content:\"nosniff\"}),/*#__PURE__*/_jsx(\"meta\",{httpEquiv:\"X-Frame-Options\",content:\"DENY\"}),/*#__PURE__*/_jsx(\"meta\",{httpEquiv:\"X-XSS-Protection\",content:\"1; mode=block\"}),/*#__PURE__*/_jsx(\"meta\",{httpEquiv:\"Referrer-Policy\",content:\"strict-origin-when-cross-origin\"}),/*#__PURE__*/_jsx(\"link\",{rel:\"preconnect\",href:process.env.REACT_APP_API_URL||'/api'})]}),/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/login\",element:isAuthenticated?/*#__PURE__*/_jsx(Navigate,{to:\"/dashboard\",replace:true}):/*#__PURE__*/_jsx(LoginPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/register\",element:isAuthenticated?/*#__PURE__*/_jsx(Navigate,{to:\"/dashboard\",replace:true}):/*#__PURE__*/_jsx(RegisterPage,{})}),/*#__PURE__*/_jsxs(Route,{path:\"/\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(Layout,{})}),children:[/*#__PURE__*/_jsx(Route,{index:true,element:/*#__PURE__*/_jsx(Navigate,{to:\"/dashboard\",replace:true})}),/*#__PURE__*/_jsx(Route,{path:\"dashboard\",element:/*#__PURE__*/_jsx(DashboardPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"portfolios\",element:/*#__PURE__*/_jsx(PortfoliosPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"portfolios/create\",element:/*#__PURE__*/_jsx(CreatePortfolioPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"portfolios/:id\",element:/*#__PURE__*/_jsx(PortfolioDetailPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"security\",element:/*#__PURE__*/_jsx(SecurityPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"profile\",element:/*#__PURE__*/_jsx(ProfilePage,{})})]}),/*#__PURE__*/_jsx(Route,{path:\"*\",element:/*#__PURE__*/_jsx(NotFoundPage,{})})]})]});};export default App;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}