{"ast": null, "code": "// TrustVault - Security Utilities\nimport CryptoJS from'crypto-js';/**\n * Validate environment security settings\n */export const validateEnvironment=()=>{// Check if running in development\nif(process.env.NODE_ENV==='development'){console.warn('🔒 Running in development mode - security features may be relaxed');}// Validate required environment variables\nconst requiredEnvVars=['REACT_APP_API_URL'];const missingVars=requiredEnvVars.filter(varName=>{const envValue=varName==='REACT_APP_API_URL'?process.env.REACT_APP_API_URL:undefined;return!envValue;});if(missingVars.length>0){console.error('❌ Missing required environment variables:',missingVars);}// Check for HTTPS in production\nif(process.env.NODE_ENV==='production'&&window.location.protocol!=='https:'){console.warn('⚠️ Application should be served over HTTPS in production');}};/**\n * Sanitize user input to prevent XSS\n */export const sanitizeInput=input=>{const div=document.createElement('div');div.textContent=input;return div.innerHTML;};/**\n * Validate email format\n */export const isValidEmail=email=>{const emailRegex=/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;return emailRegex.test(email);};/**\n * Validate password strength\n */export const validatePasswordStrength=password=>{const errors=[];let score=0;// Length check\nif(password.length<12){errors.push('Password must be at least 12 characters long');}else{score+=1;}// Uppercase check\nif(!/[A-Z]/.test(password)){errors.push('Password must contain at least one uppercase letter');}else{score+=1;}// Lowercase check\nif(!/[a-z]/.test(password)){errors.push('Password must contain at least one lowercase letter');}else{score+=1;}// Number check\nif(!/\\d/.test(password)){errors.push('Password must contain at least one number');}else{score+=1;}// Special character check\nif(!/[!@#$%^&*(),.?\":{}|<>]/.test(password)){errors.push('Password must contain at least one special character');}else{score+=1;}// Common patterns check\nconst commonPatterns=[/123456/,/password/i,/qwerty/i,/abc123/i,/admin/i,/letmein/i,/welcome/i];if(commonPatterns.some(pattern=>pattern.test(password))){errors.push('Password contains common patterns that are not allowed');score-=1;}// Sequential characters check\nif(hasSequentialChars(password)){errors.push('Password cannot contain sequential characters');score-=1;}return{isValid:errors.length===0,errors,score:Math.max(0,score)};};/**\n * Check for sequential characters in password\n */const hasSequentialChars=password=>{const sequences=['abcdefghijklmnopqrstuvwxyz','0123456789','qwertyuiopasdfghjklzxcvbnm'// keyboard layout\n];const passwordLower=password.toLowerCase();for(const sequence of sequences){for(let i=0;i<=sequence.length-3;i++){const subseq=sequence.substring(i,i+3);const reverseSubseq=subseq.split('').reverse().join('');if(passwordLower.includes(subseq)||passwordLower.includes(reverseSubseq)){return true;}}}return false;};/**\n * Generate secure random string\n */export const generateSecureRandom=function(){let length=arguments.length>0&&arguments[0]!==undefined?arguments[0]:32;const array=new Uint8Array(length);window.crypto.getRandomValues(array);return Array.from(array,byte=>byte.toString(16).padStart(2,'0')).join('');};/**\n * Hash sensitive data for client-side storage\n */export const hashData=(data,salt)=>{const saltToUse=salt||generateSecureRandom(16);return CryptoJS.SHA256(data+saltToUse).toString();};/**\n * Encrypt sensitive data for client-side storage\n */export const encryptData=(data,key)=>{return CryptoJS.AES.encrypt(data,key).toString();};/**\n * Decrypt sensitive data from client-side storage\n */export const decryptData=(encryptedData,key)=>{const bytes=CryptoJS.AES.decrypt(encryptedData,key);return bytes.toString(CryptoJS.enc.Utf8);};/**\n * Secure localStorage wrapper\n */export const secureStorage={setItem:function(key,value){let encrypt=arguments.length>2&&arguments[2]!==undefined?arguments[2]:false;try{const dataToStore=encrypt?encryptData(value,key):value;localStorage.setItem(key,dataToStore);}catch(error){console.error('Failed to store data securely:',error);}},getItem:function(key){let decrypt=arguments.length>1&&arguments[1]!==undefined?arguments[1]:false;try{const data=localStorage.getItem(key);if(!data)return null;return decrypt?decryptData(data,key):data;}catch(error){console.error('Failed to retrieve data securely:',error);return null;}},removeItem:key=>{localStorage.removeItem(key);},clear:()=>{localStorage.clear();}};/**\n * Detect potential security threats in user input\n */export const detectSecurityThreats=input=>{const threats=[];// XSS patterns\nconst xssPatterns=[/<script[^>]*>.*?<\\/script>/gi,/javascript:/gi,/vbscript:/gi,/on\\w+\\s*=/gi,/eval\\s*\\(/gi,/document\\.cookie/gi,/alert\\s*\\(/gi];// SQL injection patterns\nconst sqlPatterns=[/union\\s+select/gi,/or\\s+1\\s*=\\s*1/gi,/drop\\s+table/gi,/insert\\s+into/gi,/delete\\s+from/gi,/update\\s+.*set/gi];// Check for XSS\nif(xssPatterns.some(pattern=>pattern.test(input))){threats.push('Potential XSS attack detected');}// Check for SQL injection\nif(sqlPatterns.some(pattern=>pattern.test(input))){threats.push('Potential SQL injection detected');}return{isSafe:threats.length===0,threats};};/**\n * Rate limiting utility\n */export const createRateLimiter=(maxRequests,windowMs)=>{const requests=[];return{isAllowed:()=>{const now=Date.now();// Remove old requests outside the window\nwhile(requests.length>0&&requests[0]<=now-windowMs){requests.shift();}// Check if we're under the limit\nif(requests.length<maxRequests){requests.push(now);return true;}return false;},getRemainingRequests:()=>{const now=Date.now();// Remove old requests\nwhile(requests.length>0&&requests[0]<=now-windowMs){requests.shift();}return Math.max(0,maxRequests-requests.length);}};};/**\n * Content Security Policy violation handler\n */export const handleCSPViolation=event=>{console.error('CSP Violation:',{blockedURI:event.blockedURI,violatedDirective:event.violatedDirective,originalPolicy:event.originalPolicy,sourceFile:event.sourceFile,lineNumber:event.lineNumber});// In production, you might want to send this to your security monitoring system\nif(process.env.NODE_ENV==='production'){// Send to security monitoring endpoint\nfetch('/api/v1/security/csp-violation/',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({blockedURI:event.blockedURI,violatedDirective:event.violatedDirective,sourceFile:event.sourceFile,lineNumber:event.lineNumber,timestamp:new Date().toISOString()})}).catch(console.error);}};// Set up CSP violation listener\nif(typeof window!=='undefined'){document.addEventListener('securitypolicyviolation',handleCSPViolation);}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}