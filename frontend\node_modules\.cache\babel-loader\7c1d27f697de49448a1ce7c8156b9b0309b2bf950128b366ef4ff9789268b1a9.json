{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\TrustVault\\\\frontend\\\\src\\\\pages\\\\Profile\\\\ProfilePage.tsx\",\n  _s = $RefreshSig$();\n// TrustVault - Profile Page\n\nimport React from 'react';\nimport { Box, Typography, Card, CardContent, TextField, Button, Grid, Avatar, Divider, Alert } from '@mui/material';\nimport { Person, Security, Save, Lock } from '@mui/icons-material';\nimport { Helmet } from 'react-helmet-async';\nimport { useForm } from 'react-hook-form';\nimport { yupResolver } from '@hookform/resolvers/yup';\nimport * as yup from 'yup';\nimport { toast } from 'react-hot-toast';\n\n// Store\nimport { useAuthStore } from '../../store/authStore';\n\n// Types\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\n// Validation schema\nconst profileSchema = yup.object({\n  first_name: yup.string().min(2, 'First name must be at least 2 characters').required('First name is required'),\n  last_name: yup.string().min(2, 'Last name must be at least 2 characters').required('Last name is required'),\n  phone_number: yup.string().matches(/^\\+?[1-9]\\d{1,14}$/, 'Invalid phone number format').optional(),\n  timezone: yup.string().required('Timezone is required'),\n  language: yup.string().required('Language is required')\n});\nconst ProfilePage = () => {\n  _s();\n  var _errors$first_name, _errors$last_name, _errors$phone_number, _errors$timezone, _errors$language, _user$first_name, _user$last_name;\n  const {\n    user,\n    updateProfile,\n    isLoading\n  } = useAuthStore();\n  const {\n    register,\n    handleSubmit,\n    formState: {\n      errors,\n      isDirty\n    }\n  } = useForm({\n    resolver: yupResolver(profileSchema),\n    defaultValues: {\n      first_name: (user === null || user === void 0 ? void 0 : user.first_name) || '',\n      last_name: (user === null || user === void 0 ? void 0 : user.last_name) || '',\n      phone_number: (user === null || user === void 0 ? void 0 : user.phone_number) || '',\n      timezone: (user === null || user === void 0 ? void 0 : user.timezone) || 'UTC',\n      language: (user === null || user === void 0 ? void 0 : user.language) || 'en'\n    }\n  });\n  const onSubmit = async data => {\n    try {\n      await updateProfile(data);\n      toast.success('Profile updated successfully!');\n    } catch (error) {\n      toast.error('Failed to update profile');\n    }\n  };\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(Typography, {\n      children: \"Loading...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"title\", {\n        children: \"Profile - TrustVault\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"description\",\n        content: \"Manage your profile settings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        mb: 4,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          component: \"h1\",\n          gutterBottom: true,\n          children: \"Profile Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          color: \"text.secondary\",\n          children: \"Manage your personal information and account settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 2,\n                mb: 3,\n                children: [/*#__PURE__*/_jsxDEV(Person, {\n                  color: \"primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 115,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: \"Personal Information\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 116,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                component: \"form\",\n                onSubmit: handleSubmit(onSubmit),\n                children: [/*#__PURE__*/_jsxDEV(Grid, {\n                  container: true,\n                  spacing: 3,\n                  children: [/*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    sm: 6,\n                    children: /*#__PURE__*/_jsxDEV(TextField, {\n                      ...register('first_name'),\n                      fullWidth: true,\n                      label: \"First Name\",\n                      error: !!errors.first_name,\n                      helperText: (_errors$first_name = errors.first_name) === null || _errors$first_name === void 0 ? void 0 : _errors$first_name.message\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 124,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 123,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    sm: 6,\n                    children: /*#__PURE__*/_jsxDEV(TextField, {\n                      ...register('last_name'),\n                      fullWidth: true,\n                      label: \"Last Name\",\n                      error: !!errors.last_name,\n                      helperText: (_errors$last_name = errors.last_name) === null || _errors$last_name === void 0 ? void 0 : _errors$last_name.message\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 134,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 133,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    children: /*#__PURE__*/_jsxDEV(TextField, {\n                      value: user.email,\n                      fullWidth: true,\n                      label: \"Email Address\",\n                      disabled: true,\n                      helperText: \"Email cannot be changed\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 144,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 143,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    children: /*#__PURE__*/_jsxDEV(TextField, {\n                      value: user.username,\n                      fullWidth: true,\n                      label: \"Username\",\n                      disabled: true,\n                      helperText: \"Username cannot be changed\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 154,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 153,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    children: /*#__PURE__*/_jsxDEV(TextField, {\n                      ...register('phone_number'),\n                      fullWidth: true,\n                      label: \"Phone Number\",\n                      type: \"tel\",\n                      error: !!errors.phone_number,\n                      helperText: (_errors$phone_number = errors.phone_number) === null || _errors$phone_number === void 0 ? void 0 : _errors$phone_number.message\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 164,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 163,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    sm: 6,\n                    children: /*#__PURE__*/_jsxDEV(TextField, {\n                      ...register('timezone'),\n                      fullWidth: true,\n                      label: \"Timezone\",\n                      select: true,\n                      SelectProps: {\n                        native: true\n                      },\n                      error: !!errors.timezone,\n                      helperText: (_errors$timezone = errors.timezone) === null || _errors$timezone === void 0 ? void 0 : _errors$timezone.message,\n                      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"UTC\",\n                        children: \"UTC\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 184,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"America/New_York\",\n                        children: \"Eastern Time\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 185,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"America/Chicago\",\n                        children: \"Central Time\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 186,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"America/Denver\",\n                        children: \"Mountain Time\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 187,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"America/Los_Angeles\",\n                        children: \"Pacific Time\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 188,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"Europe/London\",\n                        children: \"London\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 189,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"Europe/Paris\",\n                        children: \"Paris\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 190,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"Asia/Tokyo\",\n                        children: \"Tokyo\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 191,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 175,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 174,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    sm: 6,\n                    children: /*#__PURE__*/_jsxDEV(TextField, {\n                      ...register('language'),\n                      fullWidth: true,\n                      label: \"Language\",\n                      select: true,\n                      SelectProps: {\n                        native: true\n                      },\n                      error: !!errors.language,\n                      helperText: (_errors$language = errors.language) === null || _errors$language === void 0 ? void 0 : _errors$language.message,\n                      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"en\",\n                        children: \"English\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 205,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"fr\",\n                        children: \"Fran\\xE7ais\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 206,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"es\",\n                        children: \"Espa\\xF1ol\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 207,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"de\",\n                        children: \"Deutsch\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 208,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 196,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 195,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 122,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  mt: 3,\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    type: \"submit\",\n                    variant: \"contained\",\n                    startIcon: /*#__PURE__*/_jsxDEV(Save, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 217,\n                      columnNumber: 34\n                    }, this),\n                    disabled: !isDirty || isLoading,\n                    children: isLoading ? 'Saving...' : 'Save Changes'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 214,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: [/*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              mb: 3\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                flexDirection: \"column\",\n                alignItems: \"center\",\n                textAlign: \"center\",\n                children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                  sx: {\n                    width: 80,\n                    height: 80,\n                    mb: 2,\n                    fontSize: '2rem'\n                  },\n                  children: [(_user$first_name = user.first_name) === null || _user$first_name === void 0 ? void 0 : _user$first_name[0], (_user$last_name = user.last_name) === null || _user$last_name === void 0 ? void 0 : _user$last_name[0]]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: [user.first_name, \" \", user.last_name]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  gutterBottom: true,\n                  children: user.email\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"text.secondary\",\n                  children: [\"Member since \", new Date(user.date_joined).toLocaleDateString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 2,\n                mb: 3,\n                children: [/*#__PURE__*/_jsxDEV(Security, {\n                  color: \"primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: \"Security\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                mb: 2,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  gutterBottom: true,\n                  children: \"Two-Factor Authentication\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Alert, {\n                  severity: user.is_mfa_enabled ? 'success' : 'warning',\n                  sx: {\n                    mb: 2\n                  },\n                  children: user.is_mfa_enabled ? 'Enabled' : 'Disabled'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 19\n                }, this), !user.is_mfa_enabled && /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  size: \"small\",\n                  fullWidth: true,\n                  sx: {\n                    mb: 2\n                  },\n                  children: \"Enable 2FA\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {\n                sx: {\n                  my: 2\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  gutterBottom: true,\n                  children: \"Password\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"text.secondary\",\n                  display: \"block\",\n                  mb: 2,\n                  children: [\"Last changed: \", new Date(user.date_joined).toLocaleDateString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  size: \"small\",\n                  fullWidth: true,\n                  startIcon: /*#__PURE__*/_jsxDEV(Lock, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 305,\n                    columnNumber: 32\n                  }, this),\n                  children: \"Change Password\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(ProfilePage, \"sOICWvAj5OTx2HcBTvRle6ps5Nc=\", false, function () {\n  return [useAuthStore, useForm];\n});\n_c = ProfilePage;\nexport default ProfilePage;\nvar _c;\n$RefreshReg$(_c, \"ProfilePage\");", "map": {"version": 3, "names": ["React", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "TextField", "<PERSON><PERSON>", "Grid", "Avatar", "Divider", "<PERSON><PERSON>", "Person", "Security", "Save", "Lock", "<PERSON><PERSON><PERSON>", "useForm", "yupResolver", "yup", "toast", "useAuthStore", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "profileSchema", "object", "first_name", "string", "min", "required", "last_name", "phone_number", "matches", "optional", "timezone", "language", "ProfilePage", "_s", "_errors$first_name", "_errors$last_name", "_errors$phone_number", "_errors$timezone", "_errors$language", "_user$first_name", "_user$last_name", "user", "updateProfile", "isLoading", "register", "handleSubmit", "formState", "errors", "isDirty", "resolver", "defaultValues", "onSubmit", "data", "success", "error", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "content", "mb", "variant", "component", "gutterBottom", "color", "container", "spacing", "item", "xs", "md", "display", "alignItems", "gap", "sm", "fullWidth", "label", "helperText", "message", "value", "email", "disabled", "username", "type", "select", "SelectProps", "native", "mt", "startIcon", "sx", "flexDirection", "textAlign", "width", "height", "fontSize", "Date", "date_joined", "toLocaleDateString", "severity", "is_mfa_enabled", "size", "my", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/TrustVault/frontend/src/pages/Profile/ProfilePage.tsx"], "sourcesContent": ["// TrustVault - Profile Page\n\nimport React from 'react';\nimport {\n  <PERSON>,\n  <PERSON><PERSON><PERSON>,\n  Card,\n  CardContent,\n  TextField,\n  Button,\n  Grid,\n  Avatar,\n  Divider,\n  Alert,\n} from '@mui/material';\nimport {\n  Person,\n  Security,\n  Save,\n  Lock,\n} from '@mui/icons-material';\nimport { Helmet } from 'react-helmet-async';\nimport { useForm } from 'react-hook-form';\nimport { yupResolver } from '@hookform/resolvers/yup';\nimport * as yup from 'yup';\nimport { toast } from 'react-hot-toast';\n\n// Store\nimport { useAuthStore } from '../../store/authStore';\n\n// Types\nimport { User } from '../../types';\n\ninterface ProfileFormData {\n  first_name: string;\n  last_name: string;\n  phone_number?: string;\n  timezone: string;\n  language: string;\n}\n\n// Validation schema\nconst profileSchema = yup.object({\n  first_name: yup\n    .string()\n    .min(2, 'First name must be at least 2 characters')\n    .required('First name is required'),\n  last_name: yup\n    .string()\n    .min(2, 'Last name must be at least 2 characters')\n    .required('Last name is required'),\n  phone_number: yup\n    .string()\n    .matches(/^\\+?[1-9]\\d{1,14}$/, 'Invalid phone number format')\n    .optional(),\n  timezone: yup.string().required('Timezone is required'),\n  language: yup.string().required('Language is required'),\n});\n\nconst ProfilePage: React.FC = () => {\n  const { user, updateProfile, isLoading } = useAuthStore();\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors, isDirty },\n  } = useForm<ProfileFormData>({\n    resolver: yupResolver(profileSchema),\n    defaultValues: {\n      first_name: user?.first_name || '',\n      last_name: user?.last_name || '',\n      phone_number: user?.phone_number || '',\n      timezone: user?.timezone || 'UTC',\n      language: user?.language || 'en',\n    },\n  });\n\n  const onSubmit = async (data: ProfileFormData) => {\n    try {\n      await updateProfile(data);\n      toast.success('Profile updated successfully!');\n    } catch (error) {\n      toast.error('Failed to update profile');\n    }\n  };\n\n  if (!user) {\n    return <Typography>Loading...</Typography>;\n  }\n\n  return (\n    <>\n      <Helmet>\n        <title>Profile - TrustVault</title>\n        <meta name=\"description\" content=\"Manage your profile settings\" />\n      </Helmet>\n\n      <Box>\n        {/* Header */}\n        <Box mb={4}>\n          <Typography variant=\"h4\" component=\"h1\" gutterBottom>\n            Profile Settings\n          </Typography>\n          <Typography variant=\"body1\" color=\"text.secondary\">\n            Manage your personal information and account settings\n          </Typography>\n        </Box>\n\n        <Grid container spacing={3}>\n          {/* Profile Information */}\n          <Grid item xs={12} md={8}>\n            <Card>\n              <CardContent>\n                <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n                  <Person color=\"primary\" />\n                  <Typography variant=\"h6\">\n                    Personal Information\n                  </Typography>\n                </Box>\n\n                <Box component=\"form\" onSubmit={handleSubmit(onSubmit)}>\n                  <Grid container spacing={3}>\n                    <Grid item xs={12} sm={6}>\n                      <TextField\n                        {...register('first_name')}\n                        fullWidth\n                        label=\"First Name\"\n                        error={!!errors.first_name}\n                        helperText={errors.first_name?.message}\n                      />\n                    </Grid>\n\n                    <Grid item xs={12} sm={6}>\n                      <TextField\n                        {...register('last_name')}\n                        fullWidth\n                        label=\"Last Name\"\n                        error={!!errors.last_name}\n                        helperText={errors.last_name?.message}\n                      />\n                    </Grid>\n\n                    <Grid item xs={12}>\n                      <TextField\n                        value={user.email}\n                        fullWidth\n                        label=\"Email Address\"\n                        disabled\n                        helperText=\"Email cannot be changed\"\n                      />\n                    </Grid>\n\n                    <Grid item xs={12}>\n                      <TextField\n                        value={user.username}\n                        fullWidth\n                        label=\"Username\"\n                        disabled\n                        helperText=\"Username cannot be changed\"\n                      />\n                    </Grid>\n\n                    <Grid item xs={12}>\n                      <TextField\n                        {...register('phone_number')}\n                        fullWidth\n                        label=\"Phone Number\"\n                        type=\"tel\"\n                        error={!!errors.phone_number}\n                        helperText={errors.phone_number?.message}\n                      />\n                    </Grid>\n\n                    <Grid item xs={12} sm={6}>\n                      <TextField\n                        {...register('timezone')}\n                        fullWidth\n                        label=\"Timezone\"\n                        select\n                        SelectProps={{ native: true }}\n                        error={!!errors.timezone}\n                        helperText={errors.timezone?.message}\n                      >\n                        <option value=\"UTC\">UTC</option>\n                        <option value=\"America/New_York\">Eastern Time</option>\n                        <option value=\"America/Chicago\">Central Time</option>\n                        <option value=\"America/Denver\">Mountain Time</option>\n                        <option value=\"America/Los_Angeles\">Pacific Time</option>\n                        <option value=\"Europe/London\">London</option>\n                        <option value=\"Europe/Paris\">Paris</option>\n                        <option value=\"Asia/Tokyo\">Tokyo</option>\n                      </TextField>\n                    </Grid>\n\n                    <Grid item xs={12} sm={6}>\n                      <TextField\n                        {...register('language')}\n                        fullWidth\n                        label=\"Language\"\n                        select\n                        SelectProps={{ native: true }}\n                        error={!!errors.language}\n                        helperText={errors.language?.message}\n                      >\n                        <option value=\"en\">English</option>\n                        <option value=\"fr\">Français</option>\n                        <option value=\"es\">Español</option>\n                        <option value=\"de\">Deutsch</option>\n                      </TextField>\n                    </Grid>\n                  </Grid>\n\n                  <Box mt={3}>\n                    <Button\n                      type=\"submit\"\n                      variant=\"contained\"\n                      startIcon={<Save />}\n                      disabled={!isDirty || isLoading}\n                    >\n                      {isLoading ? 'Saving...' : 'Save Changes'}\n                    </Button>\n                  </Box>\n                </Box>\n              </CardContent>\n            </Card>\n          </Grid>\n\n          {/* Profile Summary & Security */}\n          <Grid item xs={12} md={4}>\n            {/* Profile Summary */}\n            <Card sx={{ mb: 3 }}>\n              <CardContent>\n                <Box display=\"flex\" flexDirection=\"column\" alignItems=\"center\" textAlign=\"center\">\n                  <Avatar\n                    sx={{\n                      width: 80,\n                      height: 80,\n                      mb: 2,\n                      fontSize: '2rem',\n                    }}\n                  >\n                    {user.first_name?.[0]}{user.last_name?.[0]}\n                  </Avatar>\n                  \n                  <Typography variant=\"h6\" gutterBottom>\n                    {user.first_name} {user.last_name}\n                  </Typography>\n                  \n                  <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\n                    {user.email}\n                  </Typography>\n                  \n                  <Typography variant=\"caption\" color=\"text.secondary\">\n                    Member since {new Date(user.date_joined).toLocaleDateString()}\n                  </Typography>\n                </Box>\n              </CardContent>\n            </Card>\n\n            {/* Security Settings */}\n            <Card>\n              <CardContent>\n                <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n                  <Security color=\"primary\" />\n                  <Typography variant=\"h6\">\n                    Security\n                  </Typography>\n                </Box>\n\n                <Box mb={2}>\n                  <Typography variant=\"body2\" gutterBottom>\n                    Two-Factor Authentication\n                  </Typography>\n                  <Alert\n                    severity={user.is_mfa_enabled ? 'success' : 'warning'}\n                    sx={{ mb: 2 }}\n                  >\n                    {user.is_mfa_enabled ? 'Enabled' : 'Disabled'}\n                  </Alert>\n                  {!user.is_mfa_enabled && (\n                    <Button\n                      variant=\"outlined\"\n                      size=\"small\"\n                      fullWidth\n                      sx={{ mb: 2 }}\n                    >\n                      Enable 2FA\n                    </Button>\n                  )}\n                </Box>\n\n                <Divider sx={{ my: 2 }} />\n\n                <Box>\n                  <Typography variant=\"body2\" gutterBottom>\n                    Password\n                  </Typography>\n                  <Typography variant=\"caption\" color=\"text.secondary\" display=\"block\" mb={2}>\n                    Last changed: {new Date(user.date_joined).toLocaleDateString()}\n                  </Typography>\n                  <Button\n                    variant=\"outlined\"\n                    size=\"small\"\n                    fullWidth\n                    startIcon={<Lock />}\n                  >\n                    Change Password\n                  </Button>\n                </Box>\n              </CardContent>\n            </Card>\n          </Grid>\n        </Grid>\n      </Box>\n    </>\n  );\n};\n\nexport default ProfilePage;\n"], "mappings": ";;AAAA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,SAAS,EACTC,MAAM,EACNC,IAAI,EACJC,MAAM,EACNC,OAAO,EACPC,KAAK,QACA,eAAe;AACtB,SACEC,MAAM,EACNC,QAAQ,EACRC,IAAI,EACJC,IAAI,QACC,qBAAqB;AAC5B,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,WAAW,QAAQ,yBAAyB;AACrD,OAAO,KAAKC,GAAG,MAAM,KAAK;AAC1B,SAASC,KAAK,QAAQ,iBAAiB;;AAEvC;AACA,SAASC,YAAY,QAAQ,uBAAuB;;AAEpD;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAWA;AACA,MAAMC,aAAa,GAAGP,GAAG,CAACQ,MAAM,CAAC;EAC/BC,UAAU,EAAET,GAAG,CACZU,MAAM,CAAC,CAAC,CACRC,GAAG,CAAC,CAAC,EAAE,0CAA0C,CAAC,CAClDC,QAAQ,CAAC,wBAAwB,CAAC;EACrCC,SAAS,EAAEb,GAAG,CACXU,MAAM,CAAC,CAAC,CACRC,GAAG,CAAC,CAAC,EAAE,yCAAyC,CAAC,CACjDC,QAAQ,CAAC,uBAAuB,CAAC;EACpCE,YAAY,EAAEd,GAAG,CACdU,MAAM,CAAC,CAAC,CACRK,OAAO,CAAC,oBAAoB,EAAE,6BAA6B,CAAC,CAC5DC,QAAQ,CAAC,CAAC;EACbC,QAAQ,EAAEjB,GAAG,CAACU,MAAM,CAAC,CAAC,CAACE,QAAQ,CAAC,sBAAsB,CAAC;EACvDM,QAAQ,EAAElB,GAAG,CAACU,MAAM,CAAC,CAAC,CAACE,QAAQ,CAAC,sBAAsB;AACxD,CAAC,CAAC;AAEF,MAAMO,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,kBAAA,EAAAC,iBAAA,EAAAC,oBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,eAAA;EAClC,MAAM;IAAEC,IAAI;IAAEC,aAAa;IAAEC;EAAU,CAAC,GAAG5B,YAAY,CAAC,CAAC;EAEzD,MAAM;IACJ6B,QAAQ;IACRC,YAAY;IACZC,SAAS,EAAE;MAAEC,MAAM;MAAEC;IAAQ;EAC/B,CAAC,GAAGrC,OAAO,CAAkB;IAC3BsC,QAAQ,EAAErC,WAAW,CAACQ,aAAa,CAAC;IACpC8B,aAAa,EAAE;MACb5B,UAAU,EAAE,CAAAmB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEnB,UAAU,KAAI,EAAE;MAClCI,SAAS,EAAE,CAAAe,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEf,SAAS,KAAI,EAAE;MAChCC,YAAY,EAAE,CAAAc,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEd,YAAY,KAAI,EAAE;MACtCG,QAAQ,EAAE,CAAAW,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEX,QAAQ,KAAI,KAAK;MACjCC,QAAQ,EAAE,CAAAU,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEV,QAAQ,KAAI;IAC9B;EACF,CAAC,CAAC;EAEF,MAAMoB,QAAQ,GAAG,MAAOC,IAAqB,IAAK;IAChD,IAAI;MACF,MAAMV,aAAa,CAACU,IAAI,CAAC;MACzBtC,KAAK,CAACuC,OAAO,CAAC,+BAA+B,CAAC;IAChD,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdxC,KAAK,CAACwC,KAAK,CAAC,0BAA0B,CAAC;IACzC;EACF,CAAC;EAED,IAAI,CAACb,IAAI,EAAE;IACT,oBAAOxB,OAAA,CAACpB,UAAU;MAAA0D,QAAA,EAAC;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC;EAC5C;EAEA,oBACE1C,OAAA,CAAAE,SAAA;IAAAoC,QAAA,gBACEtC,OAAA,CAACP,MAAM;MAAA6C,QAAA,gBACLtC,OAAA;QAAAsC,QAAA,EAAO;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACnC1C,OAAA;QAAM2C,IAAI,EAAC,aAAa;QAACC,OAAO,EAAC;MAA8B;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5D,CAAC,eAET1C,OAAA,CAACrB,GAAG;MAAA2D,QAAA,gBAEFtC,OAAA,CAACrB,GAAG;QAACkE,EAAE,EAAE,CAAE;QAAAP,QAAA,gBACTtC,OAAA,CAACpB,UAAU;UAACkE,OAAO,EAAC,IAAI;UAACC,SAAS,EAAC,IAAI;UAACC,YAAY;UAAAV,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb1C,OAAA,CAACpB,UAAU;UAACkE,OAAO,EAAC,OAAO;UAACG,KAAK,EAAC,gBAAgB;UAAAX,QAAA,EAAC;QAEnD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAEN1C,OAAA,CAACf,IAAI;QAACiE,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAb,QAAA,gBAEzBtC,OAAA,CAACf,IAAI;UAACmE,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAhB,QAAA,eACvBtC,OAAA,CAACnB,IAAI;YAAAyD,QAAA,eACHtC,OAAA,CAAClB,WAAW;cAAAwD,QAAA,gBACVtC,OAAA,CAACrB,GAAG;gBAAC4E,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,GAAG,EAAE,CAAE;gBAACZ,EAAE,EAAE,CAAE;gBAAAP,QAAA,gBACpDtC,OAAA,CAACX,MAAM;kBAAC4D,KAAK,EAAC;gBAAS;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC1B1C,OAAA,CAACpB,UAAU;kBAACkE,OAAO,EAAC,IAAI;kBAAAR,QAAA,EAAC;gBAEzB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eAEN1C,OAAA,CAACrB,GAAG;gBAACoE,SAAS,EAAC,MAAM;gBAACb,QAAQ,EAAEN,YAAY,CAACM,QAAQ,CAAE;gBAAAI,QAAA,gBACrDtC,OAAA,CAACf,IAAI;kBAACiE,SAAS;kBAACC,OAAO,EAAE,CAAE;kBAAAb,QAAA,gBACzBtC,OAAA,CAACf,IAAI;oBAACmE,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAACK,EAAE,EAAE,CAAE;oBAAApB,QAAA,eACvBtC,OAAA,CAACjB,SAAS;sBAAA,GACJ4C,QAAQ,CAAC,YAAY,CAAC;sBAC1BgC,SAAS;sBACTC,KAAK,EAAC,YAAY;sBAClBvB,KAAK,EAAE,CAAC,CAACP,MAAM,CAACzB,UAAW;sBAC3BwD,UAAU,GAAA5C,kBAAA,GAAEa,MAAM,CAACzB,UAAU,cAAAY,kBAAA,uBAAjBA,kBAAA,CAAmB6C;oBAAQ;sBAAAvB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eAEP1C,OAAA,CAACf,IAAI;oBAACmE,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAACK,EAAE,EAAE,CAAE;oBAAApB,QAAA,eACvBtC,OAAA,CAACjB,SAAS;sBAAA,GACJ4C,QAAQ,CAAC,WAAW,CAAC;sBACzBgC,SAAS;sBACTC,KAAK,EAAC,WAAW;sBACjBvB,KAAK,EAAE,CAAC,CAACP,MAAM,CAACrB,SAAU;sBAC1BoD,UAAU,GAAA3C,iBAAA,GAAEY,MAAM,CAACrB,SAAS,cAAAS,iBAAA,uBAAhBA,iBAAA,CAAkB4C;oBAAQ;sBAAAvB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eAEP1C,OAAA,CAACf,IAAI;oBAACmE,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAAAf,QAAA,eAChBtC,OAAA,CAACjB,SAAS;sBACRgF,KAAK,EAAEvC,IAAI,CAACwC,KAAM;sBAClBL,SAAS;sBACTC,KAAK,EAAC,eAAe;sBACrBK,QAAQ;sBACRJ,UAAU,EAAC;oBAAyB;sBAAAtB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eAEP1C,OAAA,CAACf,IAAI;oBAACmE,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAAAf,QAAA,eAChBtC,OAAA,CAACjB,SAAS;sBACRgF,KAAK,EAAEvC,IAAI,CAAC0C,QAAS;sBACrBP,SAAS;sBACTC,KAAK,EAAC,UAAU;sBAChBK,QAAQ;sBACRJ,UAAU,EAAC;oBAA4B;sBAAAtB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eAEP1C,OAAA,CAACf,IAAI;oBAACmE,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAAAf,QAAA,eAChBtC,OAAA,CAACjB,SAAS;sBAAA,GACJ4C,QAAQ,CAAC,cAAc,CAAC;sBAC5BgC,SAAS;sBACTC,KAAK,EAAC,cAAc;sBACpBO,IAAI,EAAC,KAAK;sBACV9B,KAAK,EAAE,CAAC,CAACP,MAAM,CAACpB,YAAa;sBAC7BmD,UAAU,GAAA1C,oBAAA,GAAEW,MAAM,CAACpB,YAAY,cAAAS,oBAAA,uBAAnBA,oBAAA,CAAqB2C;oBAAQ;sBAAAvB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1C;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eAEP1C,OAAA,CAACf,IAAI;oBAACmE,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAACK,EAAE,EAAE,CAAE;oBAAApB,QAAA,eACvBtC,OAAA,CAACjB,SAAS;sBAAA,GACJ4C,QAAQ,CAAC,UAAU,CAAC;sBACxBgC,SAAS;sBACTC,KAAK,EAAC,UAAU;sBAChBQ,MAAM;sBACNC,WAAW,EAAE;wBAAEC,MAAM,EAAE;sBAAK,CAAE;sBAC9BjC,KAAK,EAAE,CAAC,CAACP,MAAM,CAACjB,QAAS;sBACzBgD,UAAU,GAAAzC,gBAAA,GAAEU,MAAM,CAACjB,QAAQ,cAAAO,gBAAA,uBAAfA,gBAAA,CAAiB0C,OAAQ;sBAAAxB,QAAA,gBAErCtC,OAAA;wBAAQ+D,KAAK,EAAC,KAAK;wBAAAzB,QAAA,EAAC;sBAAG;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAChC1C,OAAA;wBAAQ+D,KAAK,EAAC,kBAAkB;wBAAAzB,QAAA,EAAC;sBAAY;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACtD1C,OAAA;wBAAQ+D,KAAK,EAAC,iBAAiB;wBAAAzB,QAAA,EAAC;sBAAY;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACrD1C,OAAA;wBAAQ+D,KAAK,EAAC,gBAAgB;wBAAAzB,QAAA,EAAC;sBAAa;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACrD1C,OAAA;wBAAQ+D,KAAK,EAAC,qBAAqB;wBAAAzB,QAAA,EAAC;sBAAY;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACzD1C,OAAA;wBAAQ+D,KAAK,EAAC,eAAe;wBAAAzB,QAAA,EAAC;sBAAM;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC7C1C,OAAA;wBAAQ+D,KAAK,EAAC,cAAc;wBAAAzB,QAAA,EAAC;sBAAK;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC3C1C,OAAA;wBAAQ+D,KAAK,EAAC,YAAY;wBAAAzB,QAAA,EAAC;sBAAK;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CAAC,eAEP1C,OAAA,CAACf,IAAI;oBAACmE,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAACK,EAAE,EAAE,CAAE;oBAAApB,QAAA,eACvBtC,OAAA,CAACjB,SAAS;sBAAA,GACJ4C,QAAQ,CAAC,UAAU,CAAC;sBACxBgC,SAAS;sBACTC,KAAK,EAAC,UAAU;sBAChBQ,MAAM;sBACNC,WAAW,EAAE;wBAAEC,MAAM,EAAE;sBAAK,CAAE;sBAC9BjC,KAAK,EAAE,CAAC,CAACP,MAAM,CAAChB,QAAS;sBACzB+C,UAAU,GAAAxC,gBAAA,GAAES,MAAM,CAAChB,QAAQ,cAAAO,gBAAA,uBAAfA,gBAAA,CAAiByC,OAAQ;sBAAAxB,QAAA,gBAErCtC,OAAA;wBAAQ+D,KAAK,EAAC,IAAI;wBAAAzB,QAAA,EAAC;sBAAO;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACnC1C,OAAA;wBAAQ+D,KAAK,EAAC,IAAI;wBAAAzB,QAAA,EAAC;sBAAQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACpC1C,OAAA;wBAAQ+D,KAAK,EAAC,IAAI;wBAAAzB,QAAA,EAAC;sBAAO;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACnC1C,OAAA;wBAAQ+D,KAAK,EAAC,IAAI;wBAAAzB,QAAA,EAAC;sBAAO;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEP1C,OAAA,CAACrB,GAAG;kBAAC4F,EAAE,EAAE,CAAE;kBAAAjC,QAAA,eACTtC,OAAA,CAAChB,MAAM;oBACLmF,IAAI,EAAC,QAAQ;oBACbrB,OAAO,EAAC,WAAW;oBACnB0B,SAAS,eAAExE,OAAA,CAACT,IAAI;sBAAAgD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACpBuB,QAAQ,EAAE,CAAClC,OAAO,IAAIL,SAAU;oBAAAY,QAAA,EAE/BZ,SAAS,GAAG,WAAW,GAAG;kBAAc;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGP1C,OAAA,CAACf,IAAI;UAACmE,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAhB,QAAA,gBAEvBtC,OAAA,CAACnB,IAAI;YAAC4F,EAAE,EAAE;cAAE5B,EAAE,EAAE;YAAE,CAAE;YAAAP,QAAA,eAClBtC,OAAA,CAAClB,WAAW;cAAAwD,QAAA,eACVtC,OAAA,CAACrB,GAAG;gBAAC4E,OAAO,EAAC,MAAM;gBAACmB,aAAa,EAAC,QAAQ;gBAAClB,UAAU,EAAC,QAAQ;gBAACmB,SAAS,EAAC,QAAQ;gBAAArC,QAAA,gBAC/EtC,OAAA,CAACd,MAAM;kBACLuF,EAAE,EAAE;oBACFG,KAAK,EAAE,EAAE;oBACTC,MAAM,EAAE,EAAE;oBACVhC,EAAE,EAAE,CAAC;oBACLiC,QAAQ,EAAE;kBACZ,CAAE;kBAAAxC,QAAA,IAAAhB,gBAAA,GAEDE,IAAI,CAACnB,UAAU,cAAAiB,gBAAA,uBAAfA,gBAAA,CAAkB,CAAC,CAAC,GAAAC,eAAA,GAAEC,IAAI,CAACf,SAAS,cAAAc,eAAA,uBAAdA,eAAA,CAAiB,CAAC,CAAC;gBAAA;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC,eAET1C,OAAA,CAACpB,UAAU;kBAACkE,OAAO,EAAC,IAAI;kBAACE,YAAY;kBAAAV,QAAA,GAClCd,IAAI,CAACnB,UAAU,EAAC,GAAC,EAACmB,IAAI,CAACf,SAAS;gBAAA;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eAEb1C,OAAA,CAACpB,UAAU;kBAACkE,OAAO,EAAC,OAAO;kBAACG,KAAK,EAAC,gBAAgB;kBAACD,YAAY;kBAAAV,QAAA,EAC5Dd,IAAI,CAACwC;gBAAK;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eAEb1C,OAAA,CAACpB,UAAU;kBAACkE,OAAO,EAAC,SAAS;kBAACG,KAAK,EAAC,gBAAgB;kBAAAX,QAAA,GAAC,eACtC,EAAC,IAAIyC,IAAI,CAACvD,IAAI,CAACwD,WAAW,CAAC,CAACC,kBAAkB,CAAC,CAAC;gBAAA;kBAAA1C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAGP1C,OAAA,CAACnB,IAAI;YAAAyD,QAAA,eACHtC,OAAA,CAAClB,WAAW;cAAAwD,QAAA,gBACVtC,OAAA,CAACrB,GAAG;gBAAC4E,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,GAAG,EAAE,CAAE;gBAACZ,EAAE,EAAE,CAAE;gBAAAP,QAAA,gBACpDtC,OAAA,CAACV,QAAQ;kBAAC2D,KAAK,EAAC;gBAAS;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5B1C,OAAA,CAACpB,UAAU;kBAACkE,OAAO,EAAC,IAAI;kBAAAR,QAAA,EAAC;gBAEzB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eAEN1C,OAAA,CAACrB,GAAG;gBAACkE,EAAE,EAAE,CAAE;gBAAAP,QAAA,gBACTtC,OAAA,CAACpB,UAAU;kBAACkE,OAAO,EAAC,OAAO;kBAACE,YAAY;kBAAAV,QAAA,EAAC;gBAEzC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb1C,OAAA,CAACZ,KAAK;kBACJ8F,QAAQ,EAAE1D,IAAI,CAAC2D,cAAc,GAAG,SAAS,GAAG,SAAU;kBACtDV,EAAE,EAAE;oBAAE5B,EAAE,EAAE;kBAAE,CAAE;kBAAAP,QAAA,EAEbd,IAAI,CAAC2D,cAAc,GAAG,SAAS,GAAG;gBAAU;kBAAA5C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,EACP,CAAClB,IAAI,CAAC2D,cAAc,iBACnBnF,OAAA,CAAChB,MAAM;kBACL8D,OAAO,EAAC,UAAU;kBAClBsC,IAAI,EAAC,OAAO;kBACZzB,SAAS;kBACTc,EAAE,EAAE;oBAAE5B,EAAE,EAAE;kBAAE,CAAE;kBAAAP,QAAA,EACf;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN1C,OAAA,CAACb,OAAO;gBAACsF,EAAE,EAAE;kBAAEY,EAAE,EAAE;gBAAE;cAAE;gBAAA9C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAE1B1C,OAAA,CAACrB,GAAG;gBAAA2D,QAAA,gBACFtC,OAAA,CAACpB,UAAU;kBAACkE,OAAO,EAAC,OAAO;kBAACE,YAAY;kBAAAV,QAAA,EAAC;gBAEzC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb1C,OAAA,CAACpB,UAAU;kBAACkE,OAAO,EAAC,SAAS;kBAACG,KAAK,EAAC,gBAAgB;kBAACM,OAAO,EAAC,OAAO;kBAACV,EAAE,EAAE,CAAE;kBAAAP,QAAA,GAAC,gBAC5D,EAAC,IAAIyC,IAAI,CAACvD,IAAI,CAACwD,WAAW,CAAC,CAACC,kBAAkB,CAAC,CAAC;gBAAA;kBAAA1C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC,eACb1C,OAAA,CAAChB,MAAM;kBACL8D,OAAO,EAAC,UAAU;kBAClBsC,IAAI,EAAC,OAAO;kBACZzB,SAAS;kBACTa,SAAS,eAAExE,OAAA,CAACR,IAAI;oBAAA+C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAJ,QAAA,EACrB;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAAC1B,EAAA,CAjQID,WAAqB;EAAA,QACkBjB,YAAY,EAMnDJ,OAAO;AAAA;AAAA4F,EAAA,GAPPvE,WAAqB;AAmQ3B,eAAeA,WAAW;AAAC,IAAAuE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}