// TrustVault - Portfolio Transactions Page

import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  TextField,
  FormControl,
  InputLabel,
  Select,
  Grid,
} from '@mui/material';
import {
  ArrowBack,
  Add,
  MoreVert,
  TrendingUp,
  TrendingDown,
  FilterList,
} from '@mui/icons-material';
import { Helmet } from 'react-helmet-async';
import { useNavigate, useParams } from 'react-router-dom';
import { useQuery } from 'react-query';

// Services
import apiService from '../../services/api';

const TransactionsPage: React.FC = () => {
  const navigate = useNavigate();
  const { id: portfolioId } = useParams<{ id: string }>();
  
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedTransaction, setSelectedTransaction] = useState<any>(null);
  const [filterType, setFilterType] = useState<string>('ALL');
  const [searchTerm, setSearchTerm] = useState<string>('');

  // Fetch portfolio details
  const { data: portfolio } = useQuery(
    ['portfolio', portfolioId],
    () => apiService.getPortfolio(portfolioId!),
    {
      enabled: !!portfolioId,
    }
  );

  // Fetch transactions
  const { data: transactions, isLoading } = useQuery(
    ['transactions', portfolioId],
    () => apiService.getTransactions(portfolioId!),
    {
      enabled: !!portfolioId,
    }
  );

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, transaction: any) => {
    setAnchorEl(event.currentTarget);
    setSelectedTransaction(transaction);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedTransaction(null);
  };

  const getTransactionTypeColor = (type: string) => {
    switch (type) {
      case 'BUY':
        return 'success';
      case 'SELL':
        return 'error';
      case 'DIVIDEND':
        return 'info';
      case 'DEPOSIT':
        return 'primary';
      case 'WITHDRAWAL':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'BUY':
      case 'DEPOSIT':
        return <TrendingUp />;
      case 'SELL':
      case 'WITHDRAWAL':
        return <TrendingDown />;
      default:
        return null;
    }
  };

  const formatCurrency = (value: string | number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(typeof value === 'string' ? parseFloat(value) : value);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const filteredTransactions = transactions?.filter((transaction: any) => {
    const matchesType = filterType === 'ALL' || transaction.transaction_type === filterType;
    const matchesSearch = !searchTerm || 
      transaction.asset?.symbol.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.asset?.name.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesType && matchesSearch;
  }) || [];

  if (!portfolioId) {
    return (
      <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
        <Typography variant="h6" color="error">
          Portfolio ID is required
        </Typography>
      </Box>
    );
  }

  return (
    <>
      <Helmet>
        <title>Transactions - TrustVault</title>
        <meta name="description" content="View portfolio transaction history" />
      </Helmet>

      <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
        {/* Header */}
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={3}>
          <Box display="flex" alignItems="center">
            <Button
              startIcon={<ArrowBack />}
              onClick={() => navigate(`/portfolios/${portfolioId}`)}
              sx={{ mr: 2 }}
            >
              Back to Portfolio
            </Button>
            <Box>
              <Typography variant="h4" component="h1">
                Transaction History
              </Typography>
              {portfolio && (
                <Typography variant="body2" color="text.secondary">
                  {portfolio.name}
                </Typography>
              )}
            </Box>
          </Box>
          
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => navigate(`/portfolios/${portfolioId}/add-transaction`)}
          >
            Add Transaction
          </Button>
        </Box>

        {/* Filters */}
        <Paper sx={{ p: 3, mb: 3 }}>
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} sm={4}>
              <TextField
                label="Search transactions"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                fullWidth
                size="small"
              />
            </Grid>
            
            <Grid item xs={12} sm={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Transaction Type</InputLabel>
                <Select
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value)}
                  label="Transaction Type"
                >
                  <MenuItem value="ALL">All Types</MenuItem>
                  <MenuItem value="BUY">Buy</MenuItem>
                  <MenuItem value="SELL">Sell</MenuItem>
                  <MenuItem value="DIVIDEND">Dividend</MenuItem>
                  <MenuItem value="DEPOSIT">Deposit</MenuItem>
                  <MenuItem value="WITHDRAWAL">Withdrawal</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={2}>
              <Typography variant="body2" color="text.secondary">
                {filteredTransactions.length} transactions
              </Typography>
            </Grid>
          </Grid>
        </Paper>

        {/* Transactions Table */}
        <Paper>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Date</TableCell>
                  <TableCell>Type</TableCell>
                  <TableCell>Asset</TableCell>
                  <TableCell align="right">Quantity</TableCell>
                  <TableCell align="right">Price</TableCell>
                  <TableCell align="right">Total Amount</TableCell>
                  <TableCell align="center">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={7} align="center">
                      Loading transactions...
                    </TableCell>
                  </TableRow>
                ) : filteredTransactions.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} align="center">
                      <Box py={4}>
                        <Typography variant="h6" color="text.secondary" gutterBottom>
                          No Transactions Found
                        </Typography>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          {searchTerm || filterType !== 'ALL' 
                            ? 'Try adjusting your filters'
                            : 'Start by adding your first transaction'
                          }
                        </Typography>
                        <Button
                          variant="contained"
                          startIcon={<Add />}
                          onClick={() => navigate(`/portfolios/${portfolioId}/add-transaction`)}
                          sx={{ mt: 2 }}
                        >
                          Add First Transaction
                        </Button>
                      </Box>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredTransactions.map((transaction: any) => (
                    <TableRow key={transaction.id} hover>
                      <TableCell>
                        {formatDate(transaction.transaction_date)}
                      </TableCell>
                      <TableCell>
                        <Chip
                          icon={getTransactionIcon(transaction.transaction_type)}
                          label={transaction.transaction_type}
                          color={getTransactionTypeColor(transaction.transaction_type) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        {transaction.asset ? (
                          <Box>
                            <Typography variant="body2" fontWeight="medium">
                              {transaction.asset.symbol}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {transaction.asset.name}
                            </Typography>
                          </Box>
                        ) : (
                          <Typography variant="body2" color="text.secondary">
                            Cash Transaction
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell align="right">
                        {transaction.quantity ? transaction.quantity.toFixed(4) : '-'}
                      </TableCell>
                      <TableCell align="right">
                        {transaction.price ? formatCurrency(transaction.price) : '-'}
                      </TableCell>
                      <TableCell align="right">
                        <Typography
                          variant="body2"
                          color={
                            ['BUY', 'DEPOSIT'].includes(transaction.transaction_type)
                              ? 'success.main'
                              : 'error.main'
                          }
                          fontWeight="medium"
                        >
                          {['BUY', 'DEPOSIT'].includes(transaction.transaction_type) ? '+' : '-'}
                          {formatCurrency(transaction.total_amount)}
                        </Typography>
                      </TableCell>
                      <TableCell align="center">
                        <IconButton
                          size="small"
                          onClick={(e) => handleMenuOpen(e, transaction)}
                        >
                          <MoreVert />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>

        {/* Action Menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
        >
          <MenuItem onClick={handleMenuClose}>
            View Details
          </MenuItem>
          <MenuItem onClick={handleMenuClose}>
            Edit Transaction
          </MenuItem>
          <MenuItem onClick={handleMenuClose} sx={{ color: 'error.main' }}>
            Delete Transaction
          </MenuItem>
        </Menu>
      </Box>
    </>
  );
};

export default TransactionsPage;
