{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"active\", \"children\", \"className\", \"component\", \"completed\", \"disabled\", \"expanded\", \"index\", \"last\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport integerPropType from '@mui/utils/integerPropType';\nimport composeClasses from '@mui/utils/composeClasses';\nimport StepperContext from '../Stepper/StepperContext';\nimport StepContext from './StepContext';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport styled from '../styles/styled';\nimport { getStepUtilityClass } from './stepClasses';\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    orientation,\n    alternativeLabel,\n    completed\n  } = ownerState;\n  const slots = {\n    root: ['root', orientation, alternativeLabel && 'alternativeLabel', completed && 'completed']\n  };\n  return composeClasses(slots, getStepUtilityClass, classes);\n};\nconst StepRoot = styled('div', {\n  name: 'MuiStep',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[ownerState.orientation], ownerState.alternativeLabel && styles.alternativeLabel, ownerState.completed && styles.completed];\n  }\n})(_ref => {\n  let {\n    ownerState\n  } = _ref;\n  return _extends({}, ownerState.orientation === 'horizontal' && {\n    paddingLeft: 8,\n    paddingRight: 8\n  }, ownerState.alternativeLabel && {\n    flex: 1,\n    position: 'relative'\n  });\n});\nconst Step = /*#__PURE__*/React.forwardRef(function Step(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiStep'\n  });\n  const {\n      active: activeProp,\n      children,\n      className,\n      component = 'div',\n      completed: completedProp,\n      disabled: disabledProp,\n      expanded = false,\n      index,\n      last\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    activeStep,\n    connector,\n    alternativeLabel,\n    orientation,\n    nonLinear\n  } = React.useContext(StepperContext);\n  let [active = false, completed = false, disabled = false] = [activeProp, completedProp, disabledProp];\n  if (activeStep === index) {\n    active = activeProp !== undefined ? activeProp : true;\n  } else if (!nonLinear && activeStep > index) {\n    completed = completedProp !== undefined ? completedProp : true;\n  } else if (!nonLinear && activeStep < index) {\n    disabled = disabledProp !== undefined ? disabledProp : true;\n  }\n  const contextValue = React.useMemo(() => ({\n    index,\n    last,\n    expanded,\n    icon: index + 1,\n    active,\n    completed,\n    disabled\n  }), [index, last, expanded, active, completed, disabled]);\n  const ownerState = _extends({}, props, {\n    active,\n    orientation,\n    alternativeLabel,\n    completed,\n    disabled,\n    expanded,\n    component\n  });\n  const classes = useUtilityClasses(ownerState);\n  const newChildren = /*#__PURE__*/_jsxs(StepRoot, _extends({\n    as: component,\n    className: clsx(classes.root, className),\n    ref: ref,\n    ownerState: ownerState\n  }, other, {\n    children: [connector && alternativeLabel && index !== 0 ? connector : null, children]\n  }));\n  return /*#__PURE__*/_jsx(StepContext.Provider, {\n    value: contextValue,\n    children: connector && !alternativeLabel && index !== 0 ? /*#__PURE__*/_jsxs(React.Fragment, {\n      children: [connector, newChildren]\n    }) : newChildren\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Step.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Sets the step as active. Is passed to child components.\n   */\n  active: PropTypes.bool,\n  /**\n   * Should be `Step` sub-components such as `StepLabel`, `StepContent`.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Mark the step as completed. Is passed to child components.\n   */\n  completed: PropTypes.bool,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, the step is disabled, will also disable the button if\n   * `StepButton` is a child of `Step`. Is passed to child components.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * Expand the step.\n   * @default false\n   */\n  expanded: PropTypes.bool,\n  /**\n   * The position of the step.\n   * The prop defaults to the value inherited from the parent Stepper component.\n   */\n  index: integerPropType,\n  /**\n   * If `true`, the Step is displayed as rendered last.\n   * The prop defaults to the value inherited from the parent Stepper component.\n   */\n  last: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Step;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}