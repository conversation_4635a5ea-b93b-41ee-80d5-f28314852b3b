{"ast": null, "code": "function areArraysEqual(array1, array2) {\n  let itemComparer = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : (a, b) => a === b;\n  return array1.length === array2.length && array1.every((value, index) => itemComparer(value, array2[index]));\n}\nexport default areArraysEqual;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}