{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\TrustVault\\\\frontend\\\\src\\\\pages\\\\Portfolio\\\\CreatePortfolioPage.tsx\",\n  _s = $RefreshSig$();\n// TrustVault - Create Portfolio Page\n\nimport React, { useState } from 'react';\nimport { Box, Typography, Paper, TextField, Button, FormControl, InputLabel, Select, MenuItem, FormControlLabel, Switch, Alert, CircularProgress } from '@mui/material';\nimport { ArrowBack, Save } from '@mui/icons-material';\nimport { Helmet } from 'react-helmet-async';\nimport { useNavigate } from 'react-router-dom';\nimport { useMutation, useQueryClient } from 'react-query';\nimport { toast } from 'react-hot-toast';\n\n// Services\nimport apiService from '../../services/api';\n\n// Types\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CreatePortfolioPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const queryClient = useQueryClient();\n\n  // Add console log to verify component is loading\n  console.log('CreatePortfolioPage: Component is loading');\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    portfolio_type: 'MODERATE',\n    is_public: false\n  });\n  const [errors, setErrors] = useState({});\n\n  // Move hooks before any conditional returns\n  const createPortfolioMutation = useMutation(data => apiService.createPortfolio(data), {\n    onSuccess: portfolio => {\n      toast.success('Portfolio created successfully!');\n      queryClient.invalidateQueries('portfolios');\n      navigate(`/portfolios/${portfolio.id}`);\n    },\n    onError: error => {\n      var _error$response;\n      console.error('Create portfolio error:', error);\n      if ((_error$response = error.response) !== null && _error$response !== void 0 && _error$response.data) {\n        setErrors(error.response.data);\n      } else {\n        toast.error('Failed to create portfolio. Please try again.');\n      }\n    }\n  });\n  const handleInputChange = field => event => {\n    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n\n    // Clear error when user starts typing\n    if (Object.prototype.hasOwnProperty.call(errors, field)) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: ''\n      }));\n    }\n  };\n  const handleSubmit = event => {\n    event.preventDefault();\n\n    // Basic validation\n    const newErrors = {};\n    if (!formData.name.trim()) {\n      newErrors.name = 'Portfolio name is required';\n    }\n    if (Object.keys(newErrors).length > 0) {\n      setErrors(newErrors);\n      return;\n    }\n    createPortfolioMutation.mutate(formData);\n  };\n  const portfolioTypes = [{\n    value: 'CONSERVATIVE',\n    label: 'Conservative',\n    description: 'Low risk, stable returns'\n  }, {\n    value: 'MODERATE',\n    label: 'Moderate',\n    description: 'Balanced risk and return'\n  }, {\n    value: 'AGGRESSIVE',\n    label: 'Aggressive',\n    description: 'High risk, high potential returns'\n  }, {\n    value: 'CUSTOM',\n    label: 'Custom',\n    description: 'Custom allocation strategy'\n  }];\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"title\", {\n        children: \"Create Portfolio - TrustVault\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"description\",\n        content: \"Create a new investment portfolio\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        maxWidth: 800,\n        mx: 'auto',\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'none'\n        },\n        children: \"CreatePortfolioPage-Loaded\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        alignItems: \"center\",\n        mb: 3,\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          startIcon: /*#__PURE__*/_jsxDEV(ArrowBack, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 24\n          }, this),\n          onClick: () => navigate('/portfolios'),\n          sx: {\n            mr: 2\n          },\n          children: \"Back to Portfolios\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          component: \"h1\",\n          children: \"Create New Portfolio\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            flexDirection: \"column\",\n            gap: 3,\n            children: [/*#__PURE__*/_jsxDEV(TextField, {\n              label: \"Portfolio Name\",\n              value: formData.name,\n              onChange: handleInputChange('name'),\n              error: !!errors.name,\n              helperText: errors.name,\n              required: true,\n              fullWidth: true,\n              placeholder: \"e.g., My Investment Portfolio\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              label: \"Description\",\n              value: formData.description,\n              onChange: handleInputChange('description'),\n              error: !!errors.description,\n              helperText: errors.description,\n              multiline: true,\n              rows: 3,\n              fullWidth: true,\n              placeholder: \"Describe your investment strategy and goals...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Portfolio Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: formData.portfolio_type,\n                onChange: handleInputChange('portfolio_type'),\n                label: \"Portfolio Type\",\n                children: portfolioTypes.map(type => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: type.value,\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body1\",\n                      children: type.label\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 181,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"text.secondary\",\n                      children: type.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 182,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 180,\n                    columnNumber: 23\n                  }, this)\n                }, type.value, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n              control: /*#__PURE__*/_jsxDEV(Switch, {\n                checked: formData.is_public,\n                onChange: handleInputChange('is_public')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 19\n              }, this),\n              label: /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: \"Make Portfolio Public\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"text.secondary\",\n                  children: \"Allow others to view your portfolio performance (holdings remain private)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this), Object.keys(errors).length > 0 && /*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"error\",\n              children: \"Please fix the errors above and try again.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              justifyContent: \"flex-end\",\n              gap: 2,\n              mt: 2,\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                onClick: () => navigate('/portfolios'),\n                disabled: createPortfolioMutation.isLoading,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                type: \"submit\",\n                variant: \"contained\",\n                startIcon: createPortfolioMutation.isLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 228,\n                  columnNumber: 66\n                }, this) : /*#__PURE__*/_jsxDEV(Save, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 228,\n                  columnNumber: 99\n                }, this),\n                disabled: createPortfolioMutation.isLoading,\n                children: createPortfolioMutation.isLoading ? 'Creating...' : 'Create Portfolio'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mt: 3,\n          bgcolor: 'background.default'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Getting Started\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: \"After creating your portfolio, you can:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          component: \"ul\",\n          sx: {\n            mt: 1,\n            pl: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            component: \"li\",\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Add assets and track your investments\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            component: \"li\",\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Record transactions and monitor performance\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            component: \"li\",\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"View detailed analytics and reports\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            component: \"li\",\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Set up alerts and notifications\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(CreatePortfolioPage, \"LVBbV6xPhmxwLcoYxEz/sjAItyM=\", false, function () {\n  return [useNavigate, useQueryClient, useMutation];\n});\n_c = CreatePortfolioPage;\nexport default CreatePortfolioPage;\nvar _c;\n$RefreshReg$(_c, \"CreatePortfolioPage\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Paper", "TextField", "<PERSON><PERSON>", "FormControl", "InputLabel", "Select", "MenuItem", "FormControlLabel", "Switch", "<PERSON><PERSON>", "CircularProgress", "ArrowBack", "Save", "<PERSON><PERSON><PERSON>", "useNavigate", "useMutation", "useQueryClient", "toast", "apiService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CreatePortfolioPage", "_s", "navigate", "queryClient", "console", "log", "formData", "setFormData", "name", "description", "portfolio_type", "is_public", "errors", "setErrors", "createPortfolioMutation", "data", "createPortfolio", "onSuccess", "portfolio", "success", "invalidateQueries", "id", "onError", "error", "_error$response", "response", "handleInputChange", "field", "event", "value", "target", "type", "checked", "prev", "Object", "prototype", "hasOwnProperty", "call", "handleSubmit", "preventDefault", "newErrors", "trim", "keys", "length", "mutate", "portfolioTypes", "label", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "content", "sx", "max<PERSON><PERSON><PERSON>", "mx", "p", "style", "display", "alignItems", "mb", "startIcon", "onClick", "mr", "variant", "component", "onSubmit", "flexDirection", "gap", "onChange", "helperText", "required", "fullWidth", "placeholder", "multiline", "rows", "map", "color", "control", "severity", "justifyContent", "mt", "disabled", "isLoading", "size", "bgcolor", "gutterBottom", "pl", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/TrustVault/frontend/src/pages/Portfolio/CreatePortfolioPage.tsx"], "sourcesContent": ["// TrustVault - Create Portfolio Page\n\nimport React, { useState } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  TextField,\n  Button,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  FormControlLabel,\n  Switch,\n  Alert,\n  CircularProgress,\n} from '@mui/material';\nimport { ArrowBack, Save } from '@mui/icons-material';\nimport { Helmet } from 'react-helmet-async';\nimport { useNavigate } from 'react-router-dom';\nimport { useMutation, useQueryClient } from 'react-query';\nimport { toast } from 'react-hot-toast';\n\n// Services\nimport apiService from '../../services/api';\n\n// Types\ninterface CreatePortfolioForm {\n  name: string;\n  description: string;\n  portfolio_type: 'CONSERVATIVE' | 'MODERATE' | 'AGGRESSIVE' | 'CUSTOM';\n  is_public: boolean;\n}\n\nconst CreatePortfolioPage: React.FC = () => {\n  const navigate = useNavigate();\n  const queryClient = useQueryClient();\n\n  // Add console log to verify component is loading\n  console.log('CreatePortfolioPage: Component is loading');\n\n  const [formData, setFormData] = useState<CreatePortfolioForm>({\n    name: '',\n    description: '',\n    portfolio_type: 'MODERATE',\n    is_public: false,\n  });\n\n  const [errors, setErrors] = useState<Record<string, string>>({});\n\n  // Move hooks before any conditional returns\n  const createPortfolioMutation = useMutation(\n    (data: CreatePortfolioForm) => apiService.createPortfolio(data),\n    {\n      onSuccess: (portfolio) => {\n        toast.success('Portfolio created successfully!');\n        queryClient.invalidateQueries('portfolios');\n        navigate(`/portfolios/${portfolio.id}`);\n      },\n      onError: (error: any) => {\n        console.error('Create portfolio error:', error);\n        if (error.response?.data) {\n          setErrors(error.response.data);\n        } else {\n          toast.error('Failed to create portfolio. Please try again.');\n        }\n      },\n    }\n  );\n\n\n\n  const handleInputChange = (field: keyof CreatePortfolioForm) => (\n    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | any\n  ) => {\n    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;\n    setFormData(prev => ({\n      ...prev,\n      [field]: value,\n    }));\n    \n    // Clear error when user starts typing\n    if (Object.prototype.hasOwnProperty.call(errors, field)) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: '',\n      }));\n    }\n  };\n\n  const handleSubmit = (event: React.FormEvent) => {\n    event.preventDefault();\n    \n    // Basic validation\n    const newErrors: Record<string, string> = {};\n    \n    if (!formData.name.trim()) {\n      newErrors.name = 'Portfolio name is required';\n    }\n    \n    if (Object.keys(newErrors).length > 0) {\n      setErrors(newErrors);\n      return;\n    }\n    \n    createPortfolioMutation.mutate(formData);\n  };\n\n  const portfolioTypes = [\n    { value: 'CONSERVATIVE', label: 'Conservative', description: 'Low risk, stable returns' },\n    { value: 'MODERATE', label: 'Moderate', description: 'Balanced risk and return' },\n    { value: 'AGGRESSIVE', label: 'Aggressive', description: 'High risk, high potential returns' },\n    { value: 'CUSTOM', label: 'Custom', description: 'Custom allocation strategy' },\n  ];\n\n  return (\n    <>\n      <Helmet>\n        <title>Create Portfolio - TrustVault</title>\n        <meta name=\"description\" content=\"Create a new investment portfolio\" />\n      </Helmet>\n\n      <Box sx={{ maxWidth: 800, mx: 'auto', p: 3 }}>\n        {/* Debug indicator */}\n        <div style={{ display: 'none' }}>CreatePortfolioPage-Loaded</div>\n        {/* Header */}\n        <Box display=\"flex\" alignItems=\"center\" mb={3}>\n          <Button\n            startIcon={<ArrowBack />}\n            onClick={() => navigate('/portfolios')}\n            sx={{ mr: 2 }}\n          >\n            Back to Portfolios\n          </Button>\n          <Typography variant=\"h4\" component=\"h1\">\n            Create New Portfolio\n          </Typography>\n        </Box>\n\n        {/* Form */}\n        <Paper sx={{ p: 4 }}>\n          <form onSubmit={handleSubmit}>\n            <Box display=\"flex\" flexDirection=\"column\" gap={3}>\n              {/* Portfolio Name */}\n              <TextField\n                label=\"Portfolio Name\"\n                value={formData.name}\n                onChange={handleInputChange('name')}\n                error={!!errors.name}\n                helperText={errors.name}\n                required\n                fullWidth\n                placeholder=\"e.g., My Investment Portfolio\"\n              />\n\n              {/* Description */}\n              <TextField\n                label=\"Description\"\n                value={formData.description}\n                onChange={handleInputChange('description')}\n                error={!!errors.description}\n                helperText={errors.description}\n                multiline\n                rows={3}\n                fullWidth\n                placeholder=\"Describe your investment strategy and goals...\"\n              />\n\n              {/* Portfolio Type */}\n              <FormControl fullWidth>\n                <InputLabel>Portfolio Type</InputLabel>\n                <Select\n                  value={formData.portfolio_type}\n                  onChange={handleInputChange('portfolio_type')}\n                  label=\"Portfolio Type\"\n                >\n                  {portfolioTypes.map((type) => (\n                    <MenuItem key={type.value} value={type.value}>\n                      <Box>\n                        <Typography variant=\"body1\">{type.label}</Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          {type.description}\n                        </Typography>\n                      </Box>\n                    </MenuItem>\n                  ))}\n                </Select>\n              </FormControl>\n\n              {/* Public Portfolio */}\n              <FormControlLabel\n                control={\n                  <Switch\n                    checked={formData.is_public}\n                    onChange={handleInputChange('is_public')}\n                  />\n                }\n                label={\n                  <Box>\n                    <Typography variant=\"body1\">Make Portfolio Public</Typography>\n                    <Typography variant=\"caption\" color=\"text.secondary\">\n                      Allow others to view your portfolio performance (holdings remain private)\n                    </Typography>\n                  </Box>\n                }\n              />\n\n              {/* Error Display */}\n              {Object.keys(errors).length > 0 && (\n                <Alert severity=\"error\">\n                  Please fix the errors above and try again.\n                </Alert>\n              )}\n\n              {/* Submit Button */}\n              <Box display=\"flex\" justifyContent=\"flex-end\" gap={2} mt={2}>\n                <Button\n                  variant=\"outlined\"\n                  onClick={() => navigate('/portfolios')}\n                  disabled={createPortfolioMutation.isLoading}\n                >\n                  Cancel\n                </Button>\n                <Button\n                  type=\"submit\"\n                  variant=\"contained\"\n                  startIcon={createPortfolioMutation.isLoading ? <CircularProgress size={20} /> : <Save />}\n                  disabled={createPortfolioMutation.isLoading}\n                >\n                  {createPortfolioMutation.isLoading ? 'Creating...' : 'Create Portfolio'}\n                </Button>\n              </Box>\n            </Box>\n          </form>\n        </Paper>\n\n        {/* Info Box */}\n        <Paper sx={{ p: 3, mt: 3, bgcolor: 'background.default' }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Getting Started\n          </Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            After creating your portfolio, you can:\n          </Typography>\n          <Box component=\"ul\" sx={{ mt: 1, pl: 2 }}>\n            <Typography component=\"li\" variant=\"body2\" color=\"text.secondary\">\n              Add assets and track your investments\n            </Typography>\n            <Typography component=\"li\" variant=\"body2\" color=\"text.secondary\">\n              Record transactions and monitor performance\n            </Typography>\n            <Typography component=\"li\" variant=\"body2\" color=\"text.secondary\">\n              View detailed analytics and reports\n            </Typography>\n            <Typography component=\"li\" variant=\"body2\" color=\"text.secondary\">\n              Set up alerts and notifications\n            </Typography>\n          </Box>\n        </Paper>\n      </Box>\n    </>\n  );\n};\n\nexport default CreatePortfolioPage;\n"], "mappings": ";;AAAA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,SAAS,EACTC,MAAM,EACNC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,gBAAgB,EAChBC,MAAM,EACNC,KAAK,EACLC,gBAAgB,QACX,eAAe;AACtB,SAASC,SAAS,EAAEC,IAAI,QAAQ,qBAAqB;AACrD,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,EAAEC,cAAc,QAAQ,aAAa;AACzD,SAASC,KAAK,QAAQ,iBAAiB;;AAEvC;AACA,OAAOC,UAAU,MAAM,oBAAoB;;AAE3C;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAQA,MAAMC,mBAA6B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1C,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAMY,WAAW,GAAGV,cAAc,CAAC,CAAC;;EAEpC;EACAW,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;EAExD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAAsB;IAC5DkC,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE,UAAU;IAC1BC,SAAS,EAAE;EACb,CAAC,CAAC;EAEF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGvC,QAAQ,CAAyB,CAAC,CAAC,CAAC;;EAEhE;EACA,MAAMwC,uBAAuB,GAAGtB,WAAW,CACxCuB,IAAyB,IAAKpB,UAAU,CAACqB,eAAe,CAACD,IAAI,CAAC,EAC/D;IACEE,SAAS,EAAGC,SAAS,IAAK;MACxBxB,KAAK,CAACyB,OAAO,CAAC,iCAAiC,CAAC;MAChDhB,WAAW,CAACiB,iBAAiB,CAAC,YAAY,CAAC;MAC3ClB,QAAQ,CAAC,eAAegB,SAAS,CAACG,EAAE,EAAE,CAAC;IACzC,CAAC;IACDC,OAAO,EAAGC,KAAU,IAAK;MAAA,IAAAC,eAAA;MACvBpB,OAAO,CAACmB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,KAAAC,eAAA,GAAID,KAAK,CAACE,QAAQ,cAAAD,eAAA,eAAdA,eAAA,CAAgBT,IAAI,EAAE;QACxBF,SAAS,CAACU,KAAK,CAACE,QAAQ,CAACV,IAAI,CAAC;MAChC,CAAC,MAAM;QACLrB,KAAK,CAAC6B,KAAK,CAAC,+CAA+C,CAAC;MAC9D;IACF;EACF,CACF,CAAC;EAID,MAAMG,iBAAiB,GAAIC,KAAgC,IACzDC,KAAsE,IACnE;IACH,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAAM,CAACC,IAAI,KAAK,UAAU,GAAGH,KAAK,CAACE,MAAM,CAACE,OAAO,GAAGJ,KAAK,CAACE,MAAM,CAACD,KAAK;IAC1FtB,WAAW,CAAC0B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACN,KAAK,GAAGE;IACX,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIK,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACzB,MAAM,EAAEe,KAAK,CAAC,EAAE;MACvDd,SAAS,CAACoB,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACN,KAAK,GAAG;MACX,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMW,YAAY,GAAIV,KAAsB,IAAK;IAC/CA,KAAK,CAACW,cAAc,CAAC,CAAC;;IAEtB;IACA,MAAMC,SAAiC,GAAG,CAAC,CAAC;IAE5C,IAAI,CAAClC,QAAQ,CAACE,IAAI,CAACiC,IAAI,CAAC,CAAC,EAAE;MACzBD,SAAS,CAAChC,IAAI,GAAG,4BAA4B;IAC/C;IAEA,IAAI0B,MAAM,CAACQ,IAAI,CAACF,SAAS,CAAC,CAACG,MAAM,GAAG,CAAC,EAAE;MACrC9B,SAAS,CAAC2B,SAAS,CAAC;MACpB;IACF;IAEA1B,uBAAuB,CAAC8B,MAAM,CAACtC,QAAQ,CAAC;EAC1C,CAAC;EAED,MAAMuC,cAAc,GAAG,CACrB;IAAEhB,KAAK,EAAE,cAAc;IAAEiB,KAAK,EAAE,cAAc;IAAErC,WAAW,EAAE;EAA2B,CAAC,EACzF;IAAEoB,KAAK,EAAE,UAAU;IAAEiB,KAAK,EAAE,UAAU;IAAErC,WAAW,EAAE;EAA2B,CAAC,EACjF;IAAEoB,KAAK,EAAE,YAAY;IAAEiB,KAAK,EAAE,YAAY;IAAErC,WAAW,EAAE;EAAoC,CAAC,EAC9F;IAAEoB,KAAK,EAAE,QAAQ;IAAEiB,KAAK,EAAE,QAAQ;IAAErC,WAAW,EAAE;EAA6B,CAAC,CAChF;EAED,oBACEZ,OAAA,CAAAE,SAAA;IAAAgD,QAAA,gBACElD,OAAA,CAACP,MAAM;MAAAyD,QAAA,gBACLlD,OAAA;QAAAkD,QAAA,EAAO;MAA6B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC5CtD,OAAA;QAAMW,IAAI,EAAC,aAAa;QAAC4C,OAAO,EAAC;MAAmC;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjE,CAAC,eAETtD,OAAA,CAACtB,GAAG;MAAC8E,EAAE,EAAE;QAAEC,QAAQ,EAAE,GAAG;QAAEC,EAAE,EAAE,MAAM;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAT,QAAA,gBAE3ClD,OAAA;QAAK4D,KAAK,EAAE;UAAEC,OAAO,EAAE;QAAO,CAAE;QAAAX,QAAA,EAAC;MAA0B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAEjEtD,OAAA,CAACtB,GAAG;QAACmF,OAAO,EAAC,MAAM;QAACC,UAAU,EAAC,QAAQ;QAACC,EAAE,EAAE,CAAE;QAAAb,QAAA,gBAC5ClD,OAAA,CAAClB,MAAM;UACLkF,SAAS,eAAEhE,OAAA,CAACT,SAAS;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBW,OAAO,EAAEA,CAAA,KAAM5D,QAAQ,CAAC,aAAa,CAAE;UACvCmD,EAAE,EAAE;YAAEU,EAAE,EAAE;UAAE,CAAE;UAAAhB,QAAA,EACf;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTtD,OAAA,CAACrB,UAAU;UAACwF,OAAO,EAAC,IAAI;UAACC,SAAS,EAAC,IAAI;UAAAlB,QAAA,EAAC;QAExC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNtD,OAAA,CAACpB,KAAK;QAAC4E,EAAE,EAAE;UAAEG,CAAC,EAAE;QAAE,CAAE;QAAAT,QAAA,eAClBlD,OAAA;UAAMqE,QAAQ,EAAE5B,YAAa;UAAAS,QAAA,eAC3BlD,OAAA,CAACtB,GAAG;YAACmF,OAAO,EAAC,MAAM;YAACS,aAAa,EAAC,QAAQ;YAACC,GAAG,EAAE,CAAE;YAAArB,QAAA,gBAEhDlD,OAAA,CAACnB,SAAS;cACRoE,KAAK,EAAC,gBAAgB;cACtBjB,KAAK,EAAEvB,QAAQ,CAACE,IAAK;cACrB6D,QAAQ,EAAE3C,iBAAiB,CAAC,MAAM,CAAE;cACpCH,KAAK,EAAE,CAAC,CAACX,MAAM,CAACJ,IAAK;cACrB8D,UAAU,EAAE1D,MAAM,CAACJ,IAAK;cACxB+D,QAAQ;cACRC,SAAS;cACTC,WAAW,EAAC;YAA+B;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC,eAGFtD,OAAA,CAACnB,SAAS;cACRoE,KAAK,EAAC,aAAa;cACnBjB,KAAK,EAAEvB,QAAQ,CAACG,WAAY;cAC5B4D,QAAQ,EAAE3C,iBAAiB,CAAC,aAAa,CAAE;cAC3CH,KAAK,EAAE,CAAC,CAACX,MAAM,CAACH,WAAY;cAC5B6D,UAAU,EAAE1D,MAAM,CAACH,WAAY;cAC/BiE,SAAS;cACTC,IAAI,EAAE,CAAE;cACRH,SAAS;cACTC,WAAW,EAAC;YAAgD;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC,eAGFtD,OAAA,CAACjB,WAAW;cAAC4F,SAAS;cAAAzB,QAAA,gBACpBlD,OAAA,CAAChB,UAAU;gBAAAkE,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACvCtD,OAAA,CAACf,MAAM;gBACL+C,KAAK,EAAEvB,QAAQ,CAACI,cAAe;gBAC/B2D,QAAQ,EAAE3C,iBAAiB,CAAC,gBAAgB,CAAE;gBAC9CoB,KAAK,EAAC,gBAAgB;gBAAAC,QAAA,EAErBF,cAAc,CAAC+B,GAAG,CAAE7C,IAAI,iBACvBlC,OAAA,CAACd,QAAQ;kBAAkB8C,KAAK,EAAEE,IAAI,CAACF,KAAM;kBAAAkB,QAAA,eAC3ClD,OAAA,CAACtB,GAAG;oBAAAwE,QAAA,gBACFlD,OAAA,CAACrB,UAAU;sBAACwF,OAAO,EAAC,OAAO;sBAAAjB,QAAA,EAAEhB,IAAI,CAACe;oBAAK;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eACrDtD,OAAA,CAACrB,UAAU;sBAACwF,OAAO,EAAC,SAAS;sBAACa,KAAK,EAAC,gBAAgB;sBAAA9B,QAAA,EACjDhB,IAAI,CAACtB;oBAAW;sBAAAuC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACP,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC,GANOpB,IAAI,CAACF,KAAK;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAOf,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGdtD,OAAA,CAACb,gBAAgB;cACf8F,OAAO,eACLjF,OAAA,CAACZ,MAAM;gBACL+C,OAAO,EAAE1B,QAAQ,CAACK,SAAU;gBAC5B0D,QAAQ,EAAE3C,iBAAiB,CAAC,WAAW;cAAE;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CACF;cACDL,KAAK,eACHjD,OAAA,CAACtB,GAAG;gBAAAwE,QAAA,gBACFlD,OAAA,CAACrB,UAAU;kBAACwF,OAAO,EAAC,OAAO;kBAAAjB,QAAA,EAAC;gBAAqB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC9DtD,OAAA,CAACrB,UAAU;kBAACwF,OAAO,EAAC,SAAS;kBAACa,KAAK,EAAC,gBAAgB;kBAAA9B,QAAA,EAAC;gBAErD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,EAGDjB,MAAM,CAACQ,IAAI,CAAC9B,MAAM,CAAC,CAAC+B,MAAM,GAAG,CAAC,iBAC7B9C,OAAA,CAACX,KAAK;cAAC6F,QAAQ,EAAC,OAAO;cAAAhC,QAAA,EAAC;YAExB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CACR,eAGDtD,OAAA,CAACtB,GAAG;cAACmF,OAAO,EAAC,MAAM;cAACsB,cAAc,EAAC,UAAU;cAACZ,GAAG,EAAE,CAAE;cAACa,EAAE,EAAE,CAAE;cAAAlC,QAAA,gBAC1DlD,OAAA,CAAClB,MAAM;gBACLqF,OAAO,EAAC,UAAU;gBAClBF,OAAO,EAAEA,CAAA,KAAM5D,QAAQ,CAAC,aAAa,CAAE;gBACvCgF,QAAQ,EAAEpE,uBAAuB,CAACqE,SAAU;gBAAApC,QAAA,EAC7C;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTtD,OAAA,CAAClB,MAAM;gBACLoD,IAAI,EAAC,QAAQ;gBACbiC,OAAO,EAAC,WAAW;gBACnBH,SAAS,EAAE/C,uBAAuB,CAACqE,SAAS,gBAAGtF,OAAA,CAACV,gBAAgB;kBAACiG,IAAI,EAAE;gBAAG;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAGtD,OAAA,CAACR,IAAI;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzF+B,QAAQ,EAAEpE,uBAAuB,CAACqE,SAAU;gBAAApC,QAAA,EAE3CjC,uBAAuB,CAACqE,SAAS,GAAG,aAAa,GAAG;cAAkB;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGRtD,OAAA,CAACpB,KAAK;QAAC4E,EAAE,EAAE;UAAEG,CAAC,EAAE,CAAC;UAAEyB,EAAE,EAAE,CAAC;UAAEI,OAAO,EAAE;QAAqB,CAAE;QAAAtC,QAAA,gBACxDlD,OAAA,CAACrB,UAAU;UAACwF,OAAO,EAAC,IAAI;UAACsB,YAAY;UAAAvC,QAAA,EAAC;QAEtC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbtD,OAAA,CAACrB,UAAU;UAACwF,OAAO,EAAC,OAAO;UAACa,KAAK,EAAC,gBAAgB;UAAA9B,QAAA,EAAC;QAEnD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbtD,OAAA,CAACtB,GAAG;UAAC0F,SAAS,EAAC,IAAI;UAACZ,EAAE,EAAE;YAAE4B,EAAE,EAAE,CAAC;YAAEM,EAAE,EAAE;UAAE,CAAE;UAAAxC,QAAA,gBACvClD,OAAA,CAACrB,UAAU;YAACyF,SAAS,EAAC,IAAI;YAACD,OAAO,EAAC,OAAO;YAACa,KAAK,EAAC,gBAAgB;YAAA9B,QAAA,EAAC;UAElE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbtD,OAAA,CAACrB,UAAU;YAACyF,SAAS,EAAC,IAAI;YAACD,OAAO,EAAC,OAAO;YAACa,KAAK,EAAC,gBAAgB;YAAA9B,QAAA,EAAC;UAElE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbtD,OAAA,CAACrB,UAAU;YAACyF,SAAS,EAAC,IAAI;YAACD,OAAO,EAAC,OAAO;YAACa,KAAK,EAAC,gBAAgB;YAAA9B,QAAA,EAAC;UAElE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbtD,OAAA,CAACrB,UAAU;YAACyF,SAAS,EAAC,IAAI;YAACD,OAAO,EAAC,OAAO;YAACa,KAAK,EAAC,gBAAgB;YAAA9B,QAAA,EAAC;UAElE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAAClD,EAAA,CApOID,mBAA6B;EAAA,QAChBT,WAAW,EACRE,cAAc,EAeFD,WAAW;AAAA;AAAAgG,EAAA,GAjBvCxF,mBAA6B;AAsOnC,eAAeA,mBAAmB;AAAC,IAAAwF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}