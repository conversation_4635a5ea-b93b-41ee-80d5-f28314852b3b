# TrustVault - Security Models

from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from apps.core.models import BaseModel, SecurityEvent

User = get_user_model()


class ThreatIntelligence(BaseModel):
    """Model for storing threat intelligence data."""
    
    THREAT_TYPES = [
        ('IP_BLACKLIST', 'IP Blacklist'),
        ('DOMAIN_BLACKLIST', 'Domain Blacklist'),
        ('MALWARE_HASH', 'Malware Hash'),
        ('IOC', 'Indicator of Compromise'),
        ('CVE', 'Common Vulnerabilities and Exposures'),
        ('SIGNATURE', 'Attack Signature'),
    ]
    
    threat_type = models.CharField(max_length=20, choices=THREAT_TYPES)
    value = models.TextField()  # IP, domain, hash, etc.
    description = models.TextField()
    severity = models.CharField(max_length=10, choices=SecurityEvent.RISK_LEVELS)
    
    # Source information
    source = models.Char<PERSON>ield(max_length=100)  # MISP, AlienVault, etc.
    confidence = models.IntegerField(default=50)  # 0-100
    
    # Validity
    first_seen = models.DateTimeField(default=timezone.now)
    last_seen = models.DateTimeField(default=timezone.now)
    expires_at = models.DateTimeField(null=True, blank=True)
    
    # Additional context
    tags = models.JSONField(default=list, blank=True)
    references = models.JSONField(default=list, blank=True)
    
    class Meta:
        db_table = 'security_threat_intelligence'
        ordering = ['-last_seen']
        indexes = [
            models.Index(fields=['threat_type', 'value']),
            models.Index(fields=['severity', 'last_seen']),
            models.Index(fields=['expires_at']),
        ]
    
    def __str__(self):
        return f"{self.threat_type} - {self.value[:50]}"
    
    @property
    def is_expired(self):
        """Check if this threat intelligence has expired."""
        if self.expires_at:
            return timezone.now() > self.expires_at
        return False
    
    @property
    def is_active(self):
        """Check if this threat intelligence is currently active."""
        return self.is_active and not self.is_expired


class SecurityMetric(BaseModel):
    """Model for storing security metrics and KPIs."""
    
    METRIC_TYPES = [
        ('EVENT_COUNT', 'Event Count'),
        ('THREAT_LEVEL', 'Threat Level'),
        ('RESPONSE_TIME', 'Response Time'),
        ('UPTIME', 'System Uptime'),
        ('FAILED_LOGINS', 'Failed Login Attempts'),
        ('BLOCKED_IPS', 'Blocked IP Addresses'),
        ('VULNERABILITY_COUNT', 'Vulnerability Count'),
        ('COMPLIANCE_SCORE', 'Compliance Score'),
    ]
    
    metric_type = models.CharField(max_length=20, choices=METRIC_TYPES)
    value = models.DecimalField(max_digits=15, decimal_places=4)
    unit = models.CharField(max_length=20, blank=True)  # count, percentage, seconds, etc.
    
    # Time period
    period_start = models.DateTimeField()
    period_end = models.DateTimeField()
    
    # Context
    context = models.JSONField(default=dict, blank=True)
    
    class Meta:
        db_table = 'security_metric'
        ordering = ['-period_end']
        indexes = [
            models.Index(fields=['metric_type', 'period_end']),
            models.Index(fields=['period_start', 'period_end']),
        ]
    
    def __str__(self):
        return f"{self.metric_type} - {self.value} {self.unit}"


class SecurityAlert(BaseModel):
    """Model for security alerts and notifications."""
    
    ALERT_TYPES = [
        ('THRESHOLD_EXCEEDED', 'Threshold Exceeded'),
        ('ANOMALY_DETECTED', 'Anomaly Detected'),
        ('CRITICAL_EVENT', 'Critical Event'),
        ('SYSTEM_DOWN', 'System Down'),
        ('COMPLIANCE_VIOLATION', 'Compliance Violation'),
        ('MAINTENANCE_REQUIRED', 'Maintenance Required'),
    ]
    
    PRIORITY_LEVELS = [
        ('LOW', 'Low'),
        ('MEDIUM', 'Medium'),
        ('HIGH', 'High'),
        ('URGENT', 'Urgent'),
    ]
    
    alert_type = models.CharField(max_length=25, choices=ALERT_TYPES)
    priority = models.CharField(max_length=10, choices=PRIORITY_LEVELS)
    title = models.CharField(max_length=200)
    message = models.TextField()
    
    # Related objects
    security_event = models.ForeignKey(
        SecurityEvent,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='security_alerts'
    )
    
    # Alert status
    is_acknowledged = models.BooleanField(default=False)
    acknowledged_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='acknowledged_alerts'
    )
    acknowledged_at = models.DateTimeField(null=True, blank=True)
    
    # Notification tracking
    notification_sent = models.BooleanField(default=False)
    notification_channels = models.JSONField(default=list, blank=True)
    
    class Meta:
        db_table = 'security_alert'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['alert_type', 'created_at']),
            models.Index(fields=['priority', 'created_at']),
            models.Index(fields=['is_acknowledged', 'created_at']),
        ]
    
    def __str__(self):
        return f"{self.alert_type} - {self.priority} - {self.title}"
    
    def acknowledge(self, user):
        """Acknowledge this alert."""
        self.is_acknowledged = True
        self.acknowledged_by = user
        self.acknowledged_at = timezone.now()
        self.save(update_fields=['is_acknowledged', 'acknowledged_by', 'acknowledged_at'])
