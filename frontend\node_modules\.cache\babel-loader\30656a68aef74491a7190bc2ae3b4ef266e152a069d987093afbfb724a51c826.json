{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\TrustVault\\\\frontend\\\\src\\\\pages\\\\Portfolio\\\\PortfolioDetailPage.tsx\",\n  _s = $RefreshSig$();\n// TrustVault - Portfolio Detail Page\n\nimport React from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { Box, Typography, Button, Grid, Card, CardContent, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Chip, IconButton } from '@mui/material';\nimport { ArrowBack, Edit, Add, TrendingUp, TrendingDown } from '@mui/icons-material';\nimport { Helmet } from 'react-helmet-async';\nimport { useQuery } from 'react-query';\n\n// Services\nimport apiService from '../../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PortfolioDetailPage = () => {\n  _s();\n  var _portfolio$holdings;\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n\n  // Move hooks before any conditional returns\n  const {\n    data: portfolio,\n    isLoading\n  } = useQuery(['portfolio', id], () => apiService.getPortfolio(id), {\n    enabled: !!id && id !== 'create'\n  });\n\n  // Redirect if someone tries to access /portfolios/create through this component\n  React.useEffect(() => {\n    if (id === 'create') {\n      navigate('/portfolios/create', {\n        replace: true\n      });\n    }\n  }, [id, navigate]);\n\n  // Don't render anything if ID is 'create'\n  if (id === 'create') {\n    return null;\n  }\n  const formatCurrency = value => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(parseFloat(value));\n  };\n  const formatPercentage = value => {\n    const num = parseFloat(value);\n    return `${num >= 0 ? '+' : ''}${num.toFixed(2)}%`;\n  };\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(Typography, {\n      children: \"Loading portfolio...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 12\n    }, this);\n  }\n  if (!portfolio) {\n    return /*#__PURE__*/_jsxDEV(Typography, {\n      children: \"Portfolio not found\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"title\", {\n        children: [portfolio.name, \" - TrustVault\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"description\",\n        content: `Portfolio details for ${portfolio.name}`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        alignItems: \"center\",\n        gap: 2,\n        mb: 4,\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => navigate('/portfolios'),\n          children: /*#__PURE__*/_jsxDEV(ArrowBack, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          flexGrow: 1,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            component: \"h1\",\n            gutterBottom: true,\n            children: portfolio.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            color: \"text.secondary\",\n            children: portfolio.description || 'No description'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(Edit, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 24\n          }, this),\n          onClick: () => navigate(`/portfolios/${id}/edit`),\n          children: \"Edit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        mb: 4,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: \"Portfolio Overview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                container: true,\n                spacing: 3,\n                children: [/*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  md: 3,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: \"Total Value\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 122,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    color: \"primary\",\n                    children: formatCurrency(portfolio.total_value)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 125,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 121,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  md: 3,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: \"Holdings\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 131,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    children: ((_portfolio$holdings = portfolio.holdings) === null || _portfolio$holdings === void 0 ? void 0 : _portfolio$holdings.length) || 0\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 134,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  md: 3,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: \"Type\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 140,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                    label: portfolio.portfolio_type,\n                    color: \"primary\",\n                    variant: \"outlined\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 143,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 139,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  md: 3,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: \"Currency\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 151,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    children: portfolio.currency\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 154,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 150,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: \"Quick Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                flexDirection: \"column\",\n                gap: 2,\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"contained\",\n                  startIcon: /*#__PURE__*/_jsxDEV(Add, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 173,\n                    columnNumber: 32\n                  }, this),\n                  onClick: () => navigate(`/portfolios/${id}/add-holding`),\n                  children: \"Add Holding\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  onClick: () => navigate(`/portfolios/${id}/transactions`),\n                  children: \"View Transactions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  onClick: () => navigate(`/portfolios/${id}/analytics`),\n                  children: \"Analytics\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            justifyContent: \"space-between\",\n            alignItems: \"center\",\n            mb: 3,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Holdings\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(Add, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 28\n              }, this),\n              onClick: () => navigate(`/portfolios/${id}/add-holding`),\n              children: \"Add Holding\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this), portfolio.holdings && portfolio.holdings.length > 0 ? /*#__PURE__*/_jsxDEV(TableContainer, {\n            component: Paper,\n            variant: \"outlined\",\n            children: /*#__PURE__*/_jsxDEV(Table, {\n              children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                children: /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Asset\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 220,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Symbol\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 221,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: \"Quantity\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 222,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: \"Avg Cost\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 223,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: \"Current Value\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 224,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: \"P&L\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 225,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: \"P&L %\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 226,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                children: portfolio.holdings.map(holding => /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        fontWeight: \"medium\",\n                        children: holding.asset.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 234,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: holding.asset.asset_type\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 237,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 233,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 232,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"medium\",\n                      children: holding.asset.symbol\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 243,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: parseFloat(holding.quantity).toLocaleString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 247,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: formatCurrency(holding.average_cost)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 250,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: formatCurrency(holding.current_value)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 253,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      justifyContent: \"flex-end\",\n                      gap: 1,\n                      children: [parseFloat(holding.profit_loss) >= 0 ? /*#__PURE__*/_jsxDEV(TrendingUp, {\n                        color: \"success\",\n                        fontSize: \"small\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 259,\n                        columnNumber: 31\n                      }, this) : /*#__PURE__*/_jsxDEV(TrendingDown, {\n                        color: \"error\",\n                        fontSize: \"small\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 261,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        color: parseFloat(holding.profit_loss) >= 0 ? 'success.main' : 'error.main',\n                        children: formatCurrency(holding.profit_loss)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 263,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 257,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 256,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: parseFloat(holding.profit_loss_percentage) >= 0 ? 'success.main' : 'error.main',\n                      children: formatPercentage(holding.profit_loss_percentage)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 276,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 275,\n                    columnNumber: 25\n                  }, this)]\n                }, holding.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            textAlign: \"center\",\n            py: 4,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              color: \"text.secondary\",\n              gutterBottom: true,\n              children: \"No Holdings Yet\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              mb: 3,\n              children: \"Add your first holding to start tracking your investments\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              startIcon: /*#__PURE__*/_jsxDEV(Add, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 30\n              }, this),\n              onClick: () => navigate(`/portfolios/${id}/add-holding`),\n              children: \"Add First Holding\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(PortfolioDetailPage, \"VwmjvAPISvvXXeDqLsHq4p89Al0=\", false, function () {\n  return [useParams, useNavigate, useQuery];\n});\n_c = PortfolioDetailPage;\nexport default PortfolioDetailPage;\nvar _c;\n$RefreshReg$(_c, \"PortfolioDetailPage\");", "map": {"version": 3, "names": ["React", "useParams", "useNavigate", "Box", "Typography", "<PERSON><PERSON>", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "Chip", "IconButton", "ArrowBack", "Edit", "Add", "TrendingUp", "TrendingDown", "<PERSON><PERSON><PERSON>", "useQuery", "apiService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PortfolioDetailPage", "_s", "_portfolio$holdings", "id", "navigate", "data", "portfolio", "isLoading", "getPortfolio", "enabled", "useEffect", "replace", "formatCurrency", "value", "Intl", "NumberFormat", "style", "currency", "format", "parseFloat", "formatPercentage", "num", "toFixed", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "content", "display", "alignItems", "gap", "mb", "onClick", "flexGrow", "variant", "component", "gutterBottom", "color", "description", "startIcon", "container", "spacing", "item", "xs", "md", "total_value", "holdings", "length", "label", "portfolio_type", "flexDirection", "justifyContent", "size", "align", "map", "holding", "fontWeight", "asset", "asset_type", "symbol", "quantity", "toLocaleString", "average_cost", "current_value", "profit_loss", "fontSize", "profit_loss_percentage", "textAlign", "py", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/TrustVault/frontend/src/pages/Portfolio/PortfolioDetailPage.tsx"], "sourcesContent": ["// TrustVault - Portfolio Detail Page\n\nimport React from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport {\n  Box,\n  Typography,\n  Button,\n  Grid,\n  Card,\n  CardContent,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  Chip,\n  IconButton,\n  Tooltip,\n} from '@mui/material';\nimport {\n  ArrowBack,\n  Edit,\n  Add,\n  TrendingUp,\n  TrendingDown,\n} from '@mui/icons-material';\nimport { Helmet } from 'react-helmet-async';\nimport { useQuery } from 'react-query';\n\n// Services\nimport apiService from '../../services/api';\n\nconst PortfolioDetailPage: React.FC = () => {\n  const { id } = useParams<{ id: string }>();\n  const navigate = useNavigate();\n\n  // Move hooks before any conditional returns\n  const { data: portfolio, isLoading } = useQuery(\n    ['portfolio', id],\n    () => apiService.getPortfolio(id!),\n    {\n      enabled: !!id && id !== 'create',\n    }\n  );\n\n  // Redirect if someone tries to access /portfolios/create through this component\n  React.useEffect(() => {\n    if (id === 'create') {\n      navigate('/portfolios/create', { replace: true });\n    }\n  }, [id, navigate]);\n\n  // Don't render anything if ID is 'create'\n  if (id === 'create') {\n    return null;\n  }\n\n  const formatCurrency = (value: string) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD',\n    }).format(parseFloat(value));\n  };\n\n  const formatPercentage = (value: string) => {\n    const num = parseFloat(value);\n    return `${num >= 0 ? '+' : ''}${num.toFixed(2)}%`;\n  };\n\n  if (isLoading) {\n    return <Typography>Loading portfolio...</Typography>;\n  }\n\n  if (!portfolio) {\n    return <Typography>Portfolio not found</Typography>;\n  }\n\n  return (\n    <>\n      <Helmet>\n        <title>{portfolio.name} - TrustVault</title>\n        <meta name=\"description\" content={`Portfolio details for ${portfolio.name}`} />\n      </Helmet>\n\n      <Box>\n        {/* Header */}\n        <Box display=\"flex\" alignItems=\"center\" gap={2} mb={4}>\n          <IconButton onClick={() => navigate('/portfolios')}>\n            <ArrowBack />\n          </IconButton>\n          <Box flexGrow={1}>\n            <Typography variant=\"h4\" component=\"h1\" gutterBottom>\n              {portfolio.name}\n            </Typography>\n            <Typography variant=\"body1\" color=\"text.secondary\">\n              {portfolio.description || 'No description'}\n            </Typography>\n          </Box>\n          <Button\n            variant=\"outlined\"\n            startIcon={<Edit />}\n            onClick={() => navigate(`/portfolios/${id}/edit`)}\n          >\n            Edit\n          </Button>\n        </Box>\n\n        {/* Portfolio Summary */}\n        <Grid container spacing={3} mb={4}>\n          <Grid item xs={12} md={8}>\n            <Card>\n              <CardContent>\n                <Typography variant=\"h6\" gutterBottom>\n                  Portfolio Overview\n                </Typography>\n                \n                <Grid container spacing={3}>\n                  <Grid item xs={6} md={3}>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      Total Value\n                    </Typography>\n                    <Typography variant=\"h4\" color=\"primary\">\n                      {formatCurrency(portfolio.total_value)}\n                    </Typography>\n                  </Grid>\n                  \n                  <Grid item xs={6} md={3}>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      Holdings\n                    </Typography>\n                    <Typography variant=\"h4\">\n                      {portfolio.holdings?.length || 0}\n                    </Typography>\n                  </Grid>\n                  \n                  <Grid item xs={6} md={3}>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      Type\n                    </Typography>\n                    <Chip\n                      label={portfolio.portfolio_type}\n                      color=\"primary\"\n                      variant=\"outlined\"\n                    />\n                  </Grid>\n                  \n                  <Grid item xs={6} md={3}>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      Currency\n                    </Typography>\n                    <Typography variant=\"h4\">\n                      {portfolio.currency}\n                    </Typography>\n                  </Grid>\n                </Grid>\n              </CardContent>\n            </Card>\n          </Grid>\n\n          <Grid item xs={12} md={4}>\n            <Card>\n              <CardContent>\n                <Typography variant=\"h6\" gutterBottom>\n                  Quick Actions\n                </Typography>\n                \n                <Box display=\"flex\" flexDirection=\"column\" gap={2}>\n                  <Button\n                    variant=\"contained\"\n                    startIcon={<Add />}\n                    onClick={() => navigate(`/portfolios/${id}/add-holding`)}\n                  >\n                    Add Holding\n                  </Button>\n                  \n                  <Button\n                    variant=\"outlined\"\n                    onClick={() => navigate(`/portfolios/${id}/transactions`)}\n                  >\n                    View Transactions\n                  </Button>\n                  \n                  <Button\n                    variant=\"outlined\"\n                    onClick={() => navigate(`/portfolios/${id}/analytics`)}\n                  >\n                    Analytics\n                  </Button>\n                </Box>\n              </CardContent>\n            </Card>\n          </Grid>\n        </Grid>\n\n        {/* Holdings Table */}\n        <Card>\n          <CardContent>\n            <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={3}>\n              <Typography variant=\"h6\">\n                Holdings\n              </Typography>\n              <Button\n                variant=\"contained\"\n                size=\"small\"\n                startIcon={<Add />}\n                onClick={() => navigate(`/portfolios/${id}/add-holding`)}\n              >\n                Add Holding\n              </Button>\n            </Box>\n\n            {portfolio.holdings && portfolio.holdings.length > 0 ? (\n              <TableContainer component={Paper} variant=\"outlined\">\n                <Table>\n                  <TableHead>\n                    <TableRow>\n                      <TableCell>Asset</TableCell>\n                      <TableCell>Symbol</TableCell>\n                      <TableCell align=\"right\">Quantity</TableCell>\n                      <TableCell align=\"right\">Avg Cost</TableCell>\n                      <TableCell align=\"right\">Current Value</TableCell>\n                      <TableCell align=\"right\">P&L</TableCell>\n                      <TableCell align=\"right\">P&L %</TableCell>\n                    </TableRow>\n                  </TableHead>\n                  <TableBody>\n                    {portfolio.holdings.map((holding) => (\n                      <TableRow key={holding.id}>\n                        <TableCell>\n                          <Box>\n                            <Typography variant=\"body2\" fontWeight=\"medium\">\n                              {holding.asset.name}\n                            </Typography>\n                            <Typography variant=\"caption\" color=\"text.secondary\">\n                              {holding.asset.asset_type}\n                            </Typography>\n                          </Box>\n                        </TableCell>\n                        <TableCell>\n                          <Typography variant=\"body2\" fontWeight=\"medium\">\n                            {holding.asset.symbol}\n                          </Typography>\n                        </TableCell>\n                        <TableCell align=\"right\">\n                          {parseFloat(holding.quantity).toLocaleString()}\n                        </TableCell>\n                        <TableCell align=\"right\">\n                          {formatCurrency(holding.average_cost)}\n                        </TableCell>\n                        <TableCell align=\"right\">\n                          {formatCurrency(holding.current_value)}\n                        </TableCell>\n                        <TableCell align=\"right\">\n                          <Box display=\"flex\" alignItems=\"center\" justifyContent=\"flex-end\" gap={1}>\n                            {parseFloat(holding.profit_loss) >= 0 ? (\n                              <TrendingUp color=\"success\" fontSize=\"small\" />\n                            ) : (\n                              <TrendingDown color=\"error\" fontSize=\"small\" />\n                            )}\n                            <Typography\n                              variant=\"body2\"\n                              color={\n                                parseFloat(holding.profit_loss) >= 0\n                                  ? 'success.main'\n                                  : 'error.main'\n                              }\n                            >\n                              {formatCurrency(holding.profit_loss)}\n                            </Typography>\n                          </Box>\n                        </TableCell>\n                        <TableCell align=\"right\">\n                          <Typography\n                            variant=\"body2\"\n                            color={\n                              parseFloat(holding.profit_loss_percentage) >= 0\n                                ? 'success.main'\n                                : 'error.main'\n                            }\n                          >\n                            {formatPercentage(holding.profit_loss_percentage)}\n                          </Typography>\n                        </TableCell>\n                      </TableRow>\n                    ))}\n                  </TableBody>\n                </Table>\n              </TableContainer>\n            ) : (\n              <Box textAlign=\"center\" py={4}>\n                <Typography variant=\"h6\" color=\"text.secondary\" gutterBottom>\n                  No Holdings Yet\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\" mb={3}>\n                  Add your first holding to start tracking your investments\n                </Typography>\n                <Button\n                  variant=\"contained\"\n                  startIcon={<Add />}\n                  onClick={() => navigate(`/portfolios/${id}/add-holding`)}\n                >\n                  Add First Holding\n                </Button>\n              </Box>\n            )}\n          </CardContent>\n        </Card>\n      </Box>\n    </>\n  );\n};\n\nexport default PortfolioDetailPage;\n"], "mappings": ";;AAAA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,IAAI,EACJC,UAAU,QAEL,eAAe;AACtB,SACEC,SAAS,EACTC,IAAI,EACJC,GAAG,EACHC,UAAU,EACVC,YAAY,QACP,qBAAqB;AAC5B,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,QAAQ,QAAQ,aAAa;;AAEtC;AACA,OAAOC,UAAU,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5C,MAAMC,mBAA6B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,mBAAA;EAC1C,MAAM;IAAEC;EAAG,CAAC,GAAGhC,SAAS,CAAiB,CAAC;EAC1C,MAAMiC,QAAQ,GAAGhC,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM;IAAEiC,IAAI,EAAEC,SAAS;IAAEC;EAAU,CAAC,GAAGb,QAAQ,CAC7C,CAAC,WAAW,EAAES,EAAE,CAAC,EACjB,MAAMR,UAAU,CAACa,YAAY,CAACL,EAAG,CAAC,EAClC;IACEM,OAAO,EAAE,CAAC,CAACN,EAAE,IAAIA,EAAE,KAAK;EAC1B,CACF,CAAC;;EAED;EACAjC,KAAK,CAACwC,SAAS,CAAC,MAAM;IACpB,IAAIP,EAAE,KAAK,QAAQ,EAAE;MACnBC,QAAQ,CAAC,oBAAoB,EAAE;QAAEO,OAAO,EAAE;MAAK,CAAC,CAAC;IACnD;EACF,CAAC,EAAE,CAACR,EAAE,EAAEC,QAAQ,CAAC,CAAC;;EAElB;EACA,IAAID,EAAE,KAAK,QAAQ,EAAE;IACnB,OAAO,IAAI;EACb;EAEA,MAAMS,cAAc,GAAIC,KAAa,IAAK;IACxC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACC,UAAU,CAACN,KAAK,CAAC,CAAC;EAC9B,CAAC;EAED,MAAMO,gBAAgB,GAAIP,KAAa,IAAK;IAC1C,MAAMQ,GAAG,GAAGF,UAAU,CAACN,KAAK,CAAC;IAC7B,OAAO,GAAGQ,GAAG,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,GAAGA,GAAG,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG;EACnD,CAAC;EAED,IAAIf,SAAS,EAAE;IACb,oBAAOV,OAAA,CAACvB,UAAU;MAAAiD,QAAA,EAAC;IAAoB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC;EACtD;EAEA,IAAI,CAACrB,SAAS,EAAE;IACd,oBAAOT,OAAA,CAACvB,UAAU;MAAAiD,QAAA,EAAC;IAAmB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC;EACrD;EAEA,oBACE9B,OAAA,CAAAE,SAAA;IAAAwB,QAAA,gBACE1B,OAAA,CAACJ,MAAM;MAAA8B,QAAA,gBACL1B,OAAA;QAAA0B,QAAA,GAAQjB,SAAS,CAACsB,IAAI,EAAC,eAAa;MAAA;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC5C9B,OAAA;QAAM+B,IAAI,EAAC,aAAa;QAACC,OAAO,EAAE,yBAAyBvB,SAAS,CAACsB,IAAI;MAAG;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzE,CAAC,eAET9B,OAAA,CAACxB,GAAG;MAAAkD,QAAA,gBAEF1B,OAAA,CAACxB,GAAG;QAACyD,OAAO,EAAC,MAAM;QAACC,UAAU,EAAC,QAAQ;QAACC,GAAG,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAV,QAAA,gBACpD1B,OAAA,CAACV,UAAU;UAAC+C,OAAO,EAAEA,CAAA,KAAM9B,QAAQ,CAAC,aAAa,CAAE;UAAAmB,QAAA,eACjD1B,OAAA,CAACT,SAAS;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACb9B,OAAA,CAACxB,GAAG;UAAC8D,QAAQ,EAAE,CAAE;UAAAZ,QAAA,gBACf1B,OAAA,CAACvB,UAAU;YAAC8D,OAAO,EAAC,IAAI;YAACC,SAAS,EAAC,IAAI;YAACC,YAAY;YAAAf,QAAA,EACjDjB,SAAS,CAACsB;UAAI;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACb9B,OAAA,CAACvB,UAAU;YAAC8D,OAAO,EAAC,OAAO;YAACG,KAAK,EAAC,gBAAgB;YAAAhB,QAAA,EAC/CjB,SAAS,CAACkC,WAAW,IAAI;UAAgB;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACN9B,OAAA,CAACtB,MAAM;UACL6D,OAAO,EAAC,UAAU;UAClBK,SAAS,eAAE5C,OAAA,CAACR,IAAI;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACpBO,OAAO,EAAEA,CAAA,KAAM9B,QAAQ,CAAC,eAAeD,EAAE,OAAO,CAAE;UAAAoB,QAAA,EACnD;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGN9B,OAAA,CAACrB,IAAI;QAACkE,SAAS;QAACC,OAAO,EAAE,CAAE;QAACV,EAAE,EAAE,CAAE;QAAAV,QAAA,gBAChC1B,OAAA,CAACrB,IAAI;UAACoE,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAvB,QAAA,eACvB1B,OAAA,CAACpB,IAAI;YAAA8C,QAAA,eACH1B,OAAA,CAACnB,WAAW;cAAA6C,QAAA,gBACV1B,OAAA,CAACvB,UAAU;gBAAC8D,OAAO,EAAC,IAAI;gBAACE,YAAY;gBAAAf,QAAA,EAAC;cAEtC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAEb9B,OAAA,CAACrB,IAAI;gBAACkE,SAAS;gBAACC,OAAO,EAAE,CAAE;gBAAApB,QAAA,gBACzB1B,OAAA,CAACrB,IAAI;kBAACoE,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAACC,EAAE,EAAE,CAAE;kBAAAvB,QAAA,gBACtB1B,OAAA,CAACvB,UAAU;oBAAC8D,OAAO,EAAC,OAAO;oBAACG,KAAK,EAAC,gBAAgB;oBAAAhB,QAAA,EAAC;kBAEnD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACb9B,OAAA,CAACvB,UAAU;oBAAC8D,OAAO,EAAC,IAAI;oBAACG,KAAK,EAAC,SAAS;oBAAAhB,QAAA,EACrCX,cAAc,CAACN,SAAS,CAACyC,WAAW;kBAAC;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eAEP9B,OAAA,CAACrB,IAAI;kBAACoE,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAACC,EAAE,EAAE,CAAE;kBAAAvB,QAAA,gBACtB1B,OAAA,CAACvB,UAAU;oBAAC8D,OAAO,EAAC,OAAO;oBAACG,KAAK,EAAC,gBAAgB;oBAAAhB,QAAA,EAAC;kBAEnD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACb9B,OAAA,CAACvB,UAAU;oBAAC8D,OAAO,EAAC,IAAI;oBAAAb,QAAA,EACrB,EAAArB,mBAAA,GAAAI,SAAS,CAAC0C,QAAQ,cAAA9C,mBAAA,uBAAlBA,mBAAA,CAAoB+C,MAAM,KAAI;kBAAC;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eAEP9B,OAAA,CAACrB,IAAI;kBAACoE,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAACC,EAAE,EAAE,CAAE;kBAAAvB,QAAA,gBACtB1B,OAAA,CAACvB,UAAU;oBAAC8D,OAAO,EAAC,OAAO;oBAACG,KAAK,EAAC,gBAAgB;oBAAAhB,QAAA,EAAC;kBAEnD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACb9B,OAAA,CAACX,IAAI;oBACHgE,KAAK,EAAE5C,SAAS,CAAC6C,cAAe;oBAChCZ,KAAK,EAAC,SAAS;oBACfH,OAAO,EAAC;kBAAU;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAEP9B,OAAA,CAACrB,IAAI;kBAACoE,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAACC,EAAE,EAAE,CAAE;kBAAAvB,QAAA,gBACtB1B,OAAA,CAACvB,UAAU;oBAAC8D,OAAO,EAAC,OAAO;oBAACG,KAAK,EAAC,gBAAgB;oBAAAhB,QAAA,EAAC;kBAEnD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACb9B,OAAA,CAACvB,UAAU;oBAAC8D,OAAO,EAAC,IAAI;oBAAAb,QAAA,EACrBjB,SAAS,CAACW;kBAAQ;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEP9B,OAAA,CAACrB,IAAI;UAACoE,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAvB,QAAA,eACvB1B,OAAA,CAACpB,IAAI;YAAA8C,QAAA,eACH1B,OAAA,CAACnB,WAAW;cAAA6C,QAAA,gBACV1B,OAAA,CAACvB,UAAU;gBAAC8D,OAAO,EAAC,IAAI;gBAACE,YAAY;gBAAAf,QAAA,EAAC;cAEtC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAEb9B,OAAA,CAACxB,GAAG;gBAACyD,OAAO,EAAC,MAAM;gBAACsB,aAAa,EAAC,QAAQ;gBAACpB,GAAG,EAAE,CAAE;gBAAAT,QAAA,gBAChD1B,OAAA,CAACtB,MAAM;kBACL6D,OAAO,EAAC,WAAW;kBACnBK,SAAS,eAAE5C,OAAA,CAACP,GAAG;oBAAAkC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACnBO,OAAO,EAAEA,CAAA,KAAM9B,QAAQ,CAAC,eAAeD,EAAE,cAAc,CAAE;kBAAAoB,QAAA,EAC1D;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAET9B,OAAA,CAACtB,MAAM;kBACL6D,OAAO,EAAC,UAAU;kBAClBF,OAAO,EAAEA,CAAA,KAAM9B,QAAQ,CAAC,eAAeD,EAAE,eAAe,CAAE;kBAAAoB,QAAA,EAC3D;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAET9B,OAAA,CAACtB,MAAM;kBACL6D,OAAO,EAAC,UAAU;kBAClBF,OAAO,EAAEA,CAAA,KAAM9B,QAAQ,CAAC,eAAeD,EAAE,YAAY,CAAE;kBAAAoB,QAAA,EACxD;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGP9B,OAAA,CAACpB,IAAI;QAAA8C,QAAA,eACH1B,OAAA,CAACnB,WAAW;UAAA6C,QAAA,gBACV1B,OAAA,CAACxB,GAAG;YAACyD,OAAO,EAAC,MAAM;YAACuB,cAAc,EAAC,eAAe;YAACtB,UAAU,EAAC,QAAQ;YAACE,EAAE,EAAE,CAAE;YAAAV,QAAA,gBAC3E1B,OAAA,CAACvB,UAAU;cAAC8D,OAAO,EAAC,IAAI;cAAAb,QAAA,EAAC;YAEzB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb9B,OAAA,CAACtB,MAAM;cACL6D,OAAO,EAAC,WAAW;cACnBkB,IAAI,EAAC,OAAO;cACZb,SAAS,eAAE5C,OAAA,CAACP,GAAG;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACnBO,OAAO,EAAEA,CAAA,KAAM9B,QAAQ,CAAC,eAAeD,EAAE,cAAc,CAAE;cAAAoB,QAAA,EAC1D;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAELrB,SAAS,CAAC0C,QAAQ,IAAI1C,SAAS,CAAC0C,QAAQ,CAACC,MAAM,GAAG,CAAC,gBAClDpD,OAAA,CAACf,cAAc;YAACuD,SAAS,EAAEpD,KAAM;YAACmD,OAAO,EAAC,UAAU;YAAAb,QAAA,eAClD1B,OAAA,CAAClB,KAAK;cAAA4C,QAAA,gBACJ1B,OAAA,CAACd,SAAS;gBAAAwC,QAAA,eACR1B,OAAA,CAACb,QAAQ;kBAAAuC,QAAA,gBACP1B,OAAA,CAAChB,SAAS;oBAAA0C,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC5B9B,OAAA,CAAChB,SAAS;oBAAA0C,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC7B9B,OAAA,CAAChB,SAAS;oBAAC0E,KAAK,EAAC,OAAO;oBAAAhC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC7C9B,OAAA,CAAChB,SAAS;oBAAC0E,KAAK,EAAC,OAAO;oBAAAhC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC7C9B,OAAA,CAAChB,SAAS;oBAAC0E,KAAK,EAAC,OAAO;oBAAAhC,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAClD9B,OAAA,CAAChB,SAAS;oBAAC0E,KAAK,EAAC,OAAO;oBAAAhC,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACxC9B,OAAA,CAAChB,SAAS;oBAAC0E,KAAK,EAAC,OAAO;oBAAAhC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACZ9B,OAAA,CAACjB,SAAS;gBAAA2C,QAAA,EACPjB,SAAS,CAAC0C,QAAQ,CAACQ,GAAG,CAAEC,OAAO,iBAC9B5D,OAAA,CAACb,QAAQ;kBAAAuC,QAAA,gBACP1B,OAAA,CAAChB,SAAS;oBAAA0C,QAAA,eACR1B,OAAA,CAACxB,GAAG;sBAAAkD,QAAA,gBACF1B,OAAA,CAACvB,UAAU;wBAAC8D,OAAO,EAAC,OAAO;wBAACsB,UAAU,EAAC,QAAQ;wBAAAnC,QAAA,EAC5CkC,OAAO,CAACE,KAAK,CAAC/B;sBAAI;wBAAAJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACT,CAAC,eACb9B,OAAA,CAACvB,UAAU;wBAAC8D,OAAO,EAAC,SAAS;wBAACG,KAAK,EAAC,gBAAgB;wBAAAhB,QAAA,EACjDkC,OAAO,CAACE,KAAK,CAACC;sBAAU;wBAAApC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACf,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC,eACZ9B,OAAA,CAAChB,SAAS;oBAAA0C,QAAA,eACR1B,OAAA,CAACvB,UAAU;sBAAC8D,OAAO,EAAC,OAAO;sBAACsB,UAAU,EAAC,QAAQ;sBAAAnC,QAAA,EAC5CkC,OAAO,CAACE,KAAK,CAACE;oBAAM;sBAAArC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACZ9B,OAAA,CAAChB,SAAS;oBAAC0E,KAAK,EAAC,OAAO;oBAAAhC,QAAA,EACrBJ,UAAU,CAACsC,OAAO,CAACK,QAAQ,CAAC,CAACC,cAAc,CAAC;kBAAC;oBAAAvC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrC,CAAC,eACZ9B,OAAA,CAAChB,SAAS;oBAAC0E,KAAK,EAAC,OAAO;oBAAAhC,QAAA,EACrBX,cAAc,CAAC6C,OAAO,CAACO,YAAY;kBAAC;oBAAAxC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACZ9B,OAAA,CAAChB,SAAS;oBAAC0E,KAAK,EAAC,OAAO;oBAAAhC,QAAA,EACrBX,cAAc,CAAC6C,OAAO,CAACQ,aAAa;kBAAC;oBAAAzC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC,eACZ9B,OAAA,CAAChB,SAAS;oBAAC0E,KAAK,EAAC,OAAO;oBAAAhC,QAAA,eACtB1B,OAAA,CAACxB,GAAG;sBAACyD,OAAO,EAAC,MAAM;sBAACC,UAAU,EAAC,QAAQ;sBAACsB,cAAc,EAAC,UAAU;sBAACrB,GAAG,EAAE,CAAE;sBAAAT,QAAA,GACtEJ,UAAU,CAACsC,OAAO,CAACS,WAAW,CAAC,IAAI,CAAC,gBACnCrE,OAAA,CAACN,UAAU;wBAACgD,KAAK,EAAC,SAAS;wBAAC4B,QAAQ,EAAC;sBAAO;wBAAA3C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,gBAE/C9B,OAAA,CAACL,YAAY;wBAAC+C,KAAK,EAAC,OAAO;wBAAC4B,QAAQ,EAAC;sBAAO;wBAAA3C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAC/C,eACD9B,OAAA,CAACvB,UAAU;wBACT8D,OAAO,EAAC,OAAO;wBACfG,KAAK,EACHpB,UAAU,CAACsC,OAAO,CAACS,WAAW,CAAC,IAAI,CAAC,GAChC,cAAc,GACd,YACL;wBAAA3C,QAAA,EAEAX,cAAc,CAAC6C,OAAO,CAACS,WAAW;sBAAC;wBAAA1C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC,eACZ9B,OAAA,CAAChB,SAAS;oBAAC0E,KAAK,EAAC,OAAO;oBAAAhC,QAAA,eACtB1B,OAAA,CAACvB,UAAU;sBACT8D,OAAO,EAAC,OAAO;sBACfG,KAAK,EACHpB,UAAU,CAACsC,OAAO,CAACW,sBAAsB,CAAC,IAAI,CAAC,GAC3C,cAAc,GACd,YACL;sBAAA7C,QAAA,EAEAH,gBAAgB,CAACqC,OAAO,CAACW,sBAAsB;oBAAC;sBAAA5C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA,GAvDC8B,OAAO,CAACtD,EAAE;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAwDf,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,gBAEjB9B,OAAA,CAACxB,GAAG;YAACgG,SAAS,EAAC,QAAQ;YAACC,EAAE,EAAE,CAAE;YAAA/C,QAAA,gBAC5B1B,OAAA,CAACvB,UAAU;cAAC8D,OAAO,EAAC,IAAI;cAACG,KAAK,EAAC,gBAAgB;cAACD,YAAY;cAAAf,QAAA,EAAC;YAE7D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb9B,OAAA,CAACvB,UAAU;cAAC8D,OAAO,EAAC,OAAO;cAACG,KAAK,EAAC,gBAAgB;cAACN,EAAE,EAAE,CAAE;cAAAV,QAAA,EAAC;YAE1D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb9B,OAAA,CAACtB,MAAM;cACL6D,OAAO,EAAC,WAAW;cACnBK,SAAS,eAAE5C,OAAA,CAACP,GAAG;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACnBO,OAAO,EAAEA,CAAA,KAAM9B,QAAQ,CAAC,eAAeD,EAAE,cAAc,CAAE;cAAAoB,QAAA,EAC1D;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAAC1B,EAAA,CAtRID,mBAA6B;EAAA,QAClB7B,SAAS,EACPC,WAAW,EAGWsB,QAAQ;AAAA;AAAA6E,EAAA,GAL3CvE,mBAA6B;AAwRnC,eAAeA,mBAAmB;AAAC,IAAAuE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}