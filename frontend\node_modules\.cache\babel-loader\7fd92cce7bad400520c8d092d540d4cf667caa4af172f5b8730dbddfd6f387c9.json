{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"disableAnimation\", \"margin\", \"shrink\", \"variant\", \"className\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport composeClasses from '@mui/utils/composeClasses';\nimport clsx from 'clsx';\nimport formControlState from '../FormControl/formControlState';\nimport useFormControl from '../FormControl/useFormControl';\nimport FormLabel, { formLabelClasses } from '../FormLabel';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport capitalize from '../utils/capitalize';\nimport styled, { rootShouldForwardProp } from '../styles/styled';\nimport { getInputLabelUtilityClasses } from './inputLabelClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    formControl,\n    size,\n    shrink,\n    disableAnimation,\n    variant,\n    required\n  } = ownerState;\n  const slots = {\n    root: ['root', formControl && 'formControl', !disableAnimation && 'animated', shrink && 'shrink', size && size !== 'normal' && `size${capitalize(size)}`, variant],\n    asterisk: [required && 'asterisk']\n  };\n  const composedClasses = composeClasses(slots, getInputLabelUtilityClasses, classes);\n  return _extends({}, classes, composedClasses);\n};\nconst InputLabelRoot = styled(FormLabel, {\n  shouldForwardProp: prop => rootShouldForwardProp(prop) || prop === 'classes',\n  name: 'MuiInputLabel',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [{\n      [`& .${formLabelClasses.asterisk}`]: styles.asterisk\n    }, styles.root, ownerState.formControl && styles.formControl, ownerState.size === 'small' && styles.sizeSmall, ownerState.shrink && styles.shrink, !ownerState.disableAnimation && styles.animated, ownerState.focused && styles.focused, styles[ownerState.variant]];\n  }\n})(_ref => {\n  let {\n    theme,\n    ownerState\n  } = _ref;\n  return _extends({\n    display: 'block',\n    transformOrigin: 'top left',\n    whiteSpace: 'nowrap',\n    overflow: 'hidden',\n    textOverflow: 'ellipsis',\n    maxWidth: '100%'\n  }, ownerState.formControl && {\n    position: 'absolute',\n    left: 0,\n    top: 0,\n    // slight alteration to spec spacing to match visual spec result\n    transform: 'translate(0, 20px) scale(1)'\n  }, ownerState.size === 'small' && {\n    // Compensation for the `Input.inputSizeSmall` style.\n    transform: 'translate(0, 17px) scale(1)'\n  }, ownerState.shrink && {\n    transform: 'translate(0, -1.5px) scale(0.75)',\n    transformOrigin: 'top left',\n    maxWidth: '133%'\n  }, !ownerState.disableAnimation && {\n    transition: theme.transitions.create(['color', 'transform', 'max-width'], {\n      duration: theme.transitions.duration.shorter,\n      easing: theme.transitions.easing.easeOut\n    })\n  }, ownerState.variant === 'filled' && _extends({\n    // Chrome's autofill feature gives the input field a yellow background.\n    // Since the input field is behind the label in the HTML tree,\n    // the input field is drawn last and hides the label with an opaque background color.\n    // zIndex: 1 will raise the label above opaque background-colors of input.\n    zIndex: 1,\n    pointerEvents: 'none',\n    transform: 'translate(12px, 16px) scale(1)',\n    maxWidth: 'calc(100% - 24px)'\n  }, ownerState.size === 'small' && {\n    transform: 'translate(12px, 13px) scale(1)'\n  }, ownerState.shrink && _extends({\n    userSelect: 'none',\n    pointerEvents: 'auto',\n    transform: 'translate(12px, 7px) scale(0.75)',\n    maxWidth: 'calc(133% - 24px)'\n  }, ownerState.size === 'small' && {\n    transform: 'translate(12px, 4px) scale(0.75)'\n  })), ownerState.variant === 'outlined' && _extends({\n    // see comment above on filled.zIndex\n    zIndex: 1,\n    pointerEvents: 'none',\n    transform: 'translate(14px, 16px) scale(1)',\n    maxWidth: 'calc(100% - 24px)'\n  }, ownerState.size === 'small' && {\n    transform: 'translate(14px, 9px) scale(1)'\n  }, ownerState.shrink && {\n    userSelect: 'none',\n    pointerEvents: 'auto',\n    // Theoretically, we should have (8+5)*2/0.75 = 34px\n    // but it feels a better when it bleeds a bit on the left, so 32px.\n    maxWidth: 'calc(133% - 32px)',\n    transform: 'translate(14px, -9px) scale(0.75)'\n  }));\n});\nconst InputLabel = /*#__PURE__*/React.forwardRef(function InputLabel(inProps, ref) {\n  const props = useDefaultProps({\n    name: 'MuiInputLabel',\n    props: inProps\n  });\n  const {\n      disableAnimation = false,\n      shrink: shrinkProp,\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const muiFormControl = useFormControl();\n  let shrink = shrinkProp;\n  if (typeof shrink === 'undefined' && muiFormControl) {\n    shrink = muiFormControl.filled || muiFormControl.focused || muiFormControl.adornedStart;\n  }\n  const fcs = formControlState({\n    props,\n    muiFormControl,\n    states: ['size', 'variant', 'required', 'focused']\n  });\n  const ownerState = _extends({}, props, {\n    disableAnimation,\n    formControl: muiFormControl,\n    shrink,\n    size: fcs.size,\n    variant: fcs.variant,\n    required: fcs.required,\n    focused: fcs.focused\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(InputLabelRoot, _extends({\n    \"data-shrink\": shrink,\n    ownerState: ownerState,\n    ref: ref,\n    className: clsx(classes.root, className)\n  }, other, {\n    classes: classes\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? InputLabel.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['error', 'info', 'primary', 'secondary', 'success', 'warning']), PropTypes.string]),\n  /**\n   * If `true`, the transition animation is disabled.\n   * @default false\n   */\n  disableAnimation: PropTypes.bool,\n  /**\n   * If `true`, the component is disabled.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the label is displayed in an error state.\n   */\n  error: PropTypes.bool,\n  /**\n   * If `true`, the `input` of this label is focused.\n   */\n  focused: PropTypes.bool,\n  /**\n   * If `dense`, will adjust vertical spacing. This is normally obtained via context from\n   * FormControl.\n   */\n  margin: PropTypes.oneOf(['dense']),\n  /**\n   * if `true`, the label will indicate that the `input` is required.\n   */\n  required: PropTypes.bool,\n  /**\n   * If `true`, the label is shrunk.\n   */\n  shrink: PropTypes.bool,\n  /**\n   * The size of the component.\n   * @default 'normal'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['normal', 'small']), PropTypes.string]),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The variant to use.\n   */\n  variant: PropTypes.oneOf(['filled', 'outlined', 'standard'])\n} : void 0;\nexport default InputLabel;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}