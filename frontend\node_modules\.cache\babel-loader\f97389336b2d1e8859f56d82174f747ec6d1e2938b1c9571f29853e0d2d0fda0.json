{"ast": null, "code": "// TrustVault - Security Page\nimport React from'react';import{<PERSON>,Typography,Grid,Card,CardContent,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Paper,Chip,Alert}from'@mui/material';import{Security,Warning,CheckCircle,Error}from'@mui/icons-material';import{Helmet}from'react-helmet-async';import{useQuery}from'react-query';// Services\nimport apiService from'../../services/api';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const SecurityPage=()=>{var _securityDashboard$lo,_securityDashboard$lo2,_securityDashboard$lo3;const{data:securityDashboard,isLoading}=useQuery('security-dashboard',apiService.getSecurityDashboard);const{data:securityEvents}=useQuery('security-events',()=>apiService.getSecurityEvents({page:1}));const getRiskLevelColor=level=>{switch(level){case'CRITICAL':return'error';case'HIGH':return'warning';case'MEDIUM':return'info';case'LOW':return'success';default:return'default';}};const getRiskLevelIcon=level=>{switch(level){case'CRITICAL':return/*#__PURE__*/_jsx(Error,{color:\"error\"});case'HIGH':return/*#__PURE__*/_jsx(Warning,{color:\"warning\"});case'MEDIUM':return/*#__PURE__*/_jsx(Warning,{color:\"info\"});case'LOW':return/*#__PURE__*/_jsx(CheckCircle,{color:\"success\"});default:return/*#__PURE__*/_jsx(Security,{});}};return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(Helmet,{children:[/*#__PURE__*/_jsx(\"title\",{children:\"Security - TrustVault\"}),/*#__PURE__*/_jsx(\"meta\",{name:\"description\",content:\"Security monitoring and events\"})]}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Box,{mb:4,children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",component:\"h1\",gutterBottom:true,children:\"Security Center\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",color:\"text.secondary\",children:\"Monitor security events and system status\"})]}),securityDashboard&&securityDashboard.security_events.unresolved>0&&/*#__PURE__*/_jsxs(Alert,{severity:\"warning\",sx:{mb:3},children:[\"You have \",securityDashboard.security_events.unresolved,\" unresolved security events that require attention.\"]}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,mb:4,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(CardContent,{children:/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",justifyContent:\"space-between\",children:[/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{color:\"text.secondary\",gutterBottom:true,children:\"Total Events\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",children:(securityDashboard===null||securityDashboard===void 0?void 0:securityDashboard.security_events.total_events)||0})]}),/*#__PURE__*/_jsx(Security,{color:\"primary\",sx:{fontSize:40}})]})})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(CardContent,{children:/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",justifyContent:\"space-between\",children:[/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{color:\"text.secondary\",gutterBottom:true,children:\"Last 24h\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",children:(securityDashboard===null||securityDashboard===void 0?void 0:securityDashboard.security_events.last_24h)||0})]}),/*#__PURE__*/_jsx(Warning,{color:\"warning\",sx:{fontSize:40}})]})})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(CardContent,{children:/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",justifyContent:\"space-between\",children:[/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{color:\"text.secondary\",gutterBottom:true,children:\"Critical Events\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",color:\"error\",children:(securityDashboard===null||securityDashboard===void 0?void 0:securityDashboard.security_events.critical)||0})]}),/*#__PURE__*/_jsx(Error,{color:\"error\",sx:{fontSize:40}})]})})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(CardContent,{children:/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",justifyContent:\"space-between\",children:[/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{color:\"text.secondary\",gutterBottom:true,children:\"Unresolved\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",color:\"warning\",children:(securityDashboard===null||securityDashboard===void 0?void 0:securityDashboard.security_events.unresolved)||0})]}),/*#__PURE__*/_jsx(Warning,{color:\"warning\",sx:{fontSize:40}})]})})})})]}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,mb:4,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Login Activity (24h)\"}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:2,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:6,children:/*#__PURE__*/_jsxs(Box,{textAlign:\"center\",children:[/*#__PURE__*/_jsx(Typography,{variant:\"h3\",color:\"success.main\",children:(securityDashboard===null||securityDashboard===void 0?void 0:securityDashboard.login_attempts.successful_last_24h)||0}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Successful\"})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:6,children:/*#__PURE__*/_jsxs(Box,{textAlign:\"center\",children:[/*#__PURE__*/_jsx(Typography,{variant:\"h3\",color:\"error.main\",children:(securityDashboard===null||securityDashboard===void 0?void 0:securityDashboard.login_attempts.failed_last_24h)||0}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Failed\"})]})})]}),((_securityDashboard$lo=securityDashboard===null||securityDashboard===void 0?void 0:(_securityDashboard$lo2=securityDashboard.login_attempts)===null||_securityDashboard$lo2===void 0?void 0:_securityDashboard$lo2.suspicious_last_24h)!==null&&_securityDashboard$lo!==void 0?_securityDashboard$lo:0)>0&&/*#__PURE__*/_jsxs(Alert,{severity:\"warning\",sx:{mt:2},children:[securityDashboard===null||securityDashboard===void 0?void 0:(_securityDashboard$lo3=securityDashboard.login_attempts)===null||_securityDashboard$lo3===void 0?void 0:_securityDashboard$lo3.suspicious_last_24h,\" suspicious login attempts detected\"]})]})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Top Threat Sources\"}),securityDashboard!==null&&securityDashboard!==void 0&&securityDashboard.threat_sources&&securityDashboard.threat_sources.length>0?/*#__PURE__*/_jsx(Box,{children:securityDashboard.threat_sources.slice(0,5).map((source,index)=>/*#__PURE__*/_jsxs(Box,{display:\"flex\",justifyContent:\"space-between\",alignItems:\"center\",py:1,borderBottom:index<4?1:0,borderColor:\"divider\",children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",fontFamily:\"monospace\",children:source.source_ip}),/*#__PURE__*/_jsx(Chip,{label:`${source.count} events`,size:\"small\",color:\"warning\"})]},source.source_ip))}):/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"No threat sources detected\"})]})})})]}),/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Recent Security Events\"}),isLoading?/*#__PURE__*/_jsx(Typography,{children:\"Loading security events...\"}):securityEvents&&securityEvents.results.length>0?/*#__PURE__*/_jsx(TableContainer,{component:Paper,variant:\"outlined\",children:/*#__PURE__*/_jsxs(Table,{children:[/*#__PURE__*/_jsx(TableHead,{children:/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:\"Event Type\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Risk Level\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Source IP\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Description\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Status\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Date\"})]})}),/*#__PURE__*/_jsx(TableBody,{children:securityEvents.results.slice(0,10).map(event=>/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",gap:1,children:[getRiskLevelIcon(event.risk_level),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:event.event_type.replace('_',' ')})]})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(Chip,{label:event.risk_level,color:getRiskLevelColor(event.risk_level),size:\"small\"})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(Typography,{variant:\"body2\",fontFamily:\"monospace\",children:event.source_ip})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(Typography,{variant:\"body2\",noWrap:true,children:event.description})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(Chip,{label:event.is_resolved?'Resolved':'Open',color:event.is_resolved?'success':'warning',size:\"small\"})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:new Date(event.created_at).toLocaleString()})})]},event.id))})]})}):/*#__PURE__*/_jsxs(Box,{textAlign:\"center\",py:4,children:[/*#__PURE__*/_jsx(CheckCircle,{sx:{fontSize:64,color:'success.main',mb:2}}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"text.secondary\",gutterBottom:true,children:\"No Security Events\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Your system is secure with no recent security events\"})]})]})})]})]});};export default SecurityPage;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}