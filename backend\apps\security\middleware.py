# TrustVault - Security Middleware

import logging
import time
import json
from django.http import JsonResponse
from django.utils import timezone
from django.core.cache import cache
from django.conf import settings
from apps.core.models import SecurityEvent, AuditLog

logger = logging.getLogger('apps.security')


class SecurityMiddleware:
    """Security middleware for request monitoring and protection."""
    
    def __init__(self, get_response):
        self.get_response = get_response
        
        # Suspicious patterns to detect
        self.suspicious_patterns = [
            # SQL Injection patterns
            r'union\s+select',
            r'or\s+1\s*=\s*1',
            r'drop\s+table',
            r'insert\s+into',
            r'delete\s+from',
            r'update\s+.*set',
            r'information_schema',
            r'exec\s*\(',
            r'xp_cmdshell',
            
            # XSS patterns
            r'<script[^>]*>',
            r'javascript:',
            r'vbscript:',
            r'onload\s*=',
            r'onerror\s*=',
            r'onclick\s*=',
            r'onmouseover\s*=',
            r'eval\s*\(',
            r'document\.cookie',
            r'alert\s*\(',
            
            # Directory traversal
            r'\.\./.*\.\.',
            r'\.\.\\.*\.\.',
            r'/etc/passwd',
            r'/etc/shadow',
            r'boot\.ini',
            r'win\.ini',
            
            # Command injection
            r'system\s*\(',
            r'exec\s*\(',
            r'shell_exec',
            r'passthru\s*\(',
            r'`.*`',
            r'\|\s*nc\s',
            r'\|\s*netcat',
            
            # File inclusion
            r'php://filter',
            r'php://input',
            r'data://text',
            r'expect://',
            r'file://',
        ]
    
    def __call__(self, request):
        """Process request through security middleware."""
        start_time = time.time()
        
        # Get client information
        client_ip = self._get_client_ip(request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        
        # Check rate limiting
        if self._is_rate_limited(client_ip):
            return JsonResponse({
                'error': 'Rate limit exceeded',
                'message': 'Too many requests. Please try again later.'
            }, status=429)
        
        # Check for suspicious patterns
        threat_detected = self._detect_threats(request)
        if threat_detected:
            self._log_security_event(
                'INTRUSION_ATTEMPT',
                'HIGH',
                client_ip,
                user_agent,
                f"Suspicious pattern detected: {threat_detected}",
                {
                    'path': request.path,
                    'method': request.method,
                    'pattern': threat_detected,
                    'query_params': dict(request.GET),
                    'user': str(request.user) if hasattr(request, 'user') and request.user.is_authenticated else 'Anonymous'
                }
            )
            
            return JsonResponse({
                'error': 'Security violation detected',
                'message': 'Request blocked for security reasons.'
            }, status=403)
        
        # Process request
        response = self.get_response(request)
        
        # Calculate response time
        response_time = time.time() - start_time
        
        # Log request if needed
        if self._should_log_request(request, response):
            self._log_request(request, response, response_time)
        
        # Add security headers
        self._add_security_headers(response)
        
        return response
    
    def _get_client_ip(self, request):
        """Get client IP address."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR', '127.0.0.1')
        return ip
    
    def _is_rate_limited(self, client_ip):
        """Check if client IP is rate limited."""
        cache_key = f"rate_limit:{client_ip}"
        current_requests = cache.get(cache_key, 0)
        
        # Allow 100 requests per minute per IP
        if current_requests >= 100:
            return True
        
        # Increment counter
        cache.set(cache_key, current_requests + 1, 60)
        return False
    
    def _detect_threats(self, request):
        """Detect security threats in request."""
        import re
        
        # Check URL path
        path = request.path.lower()
        for pattern in self.suspicious_patterns:
            if re.search(pattern, path, re.IGNORECASE):
                return pattern
        
        # Check query parameters
        for key, value in request.GET.items():
            combined = f"{key}={value}".lower()
            for pattern in self.suspicious_patterns:
                if re.search(pattern, combined, re.IGNORECASE):
                    return pattern
        
        # Check POST data if available
        if request.method == 'POST':
            try:
                if hasattr(request, 'body') and request.body:
                    body = request.body.decode('utf-8', errors='ignore').lower()
                    for pattern in self.suspicious_patterns:
                        if re.search(pattern, body, re.IGNORECASE):
                            return pattern
            except:
                pass
        
        # Check headers for suspicious content
        suspicious_headers = ['User-Agent', 'Referer', 'X-Forwarded-For']
        for header in suspicious_headers:
            value = request.META.get(f'HTTP_{header.upper().replace("-", "_")}', '').lower()
            for pattern in self.suspicious_patterns:
                if re.search(pattern, value, re.IGNORECASE):
                    return pattern
        
        return None
    
    def _should_log_request(self, request, response):
        """Determine if request should be logged."""
        # Log all authentication requests
        if '/api/v1/auth/' in request.path:
            return True
        
        # Log all admin requests
        if '/admin/' in request.path:
            return True
        
        # Log error responses
        if response.status_code >= 400:
            return True
        
        # Log sensitive endpoints
        sensitive_paths = ['/api/v1/portfolio/', '/api/v1/security/']
        for path in sensitive_paths:
            if path in request.path:
                return True
        
        return False
    
    def _log_request(self, request, response, response_time):
        """Log request details."""
        if hasattr(request, 'user') and request.user.is_authenticated:
            user = request.user
        else:
            user = None
        
        # Determine severity based on response status
        if response.status_code >= 500:
            severity = 'CRITICAL'
        elif response.status_code >= 400:
            severity = 'HIGH'
        else:
            severity = 'LOW'
        
        AuditLog.objects.create(
            user=user,
            action='ACCESS',
            resource_type='HTTP_REQUEST',
            resource_id=request.path,
            ip_address=self._get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            severity=severity,
            details={
                'method': request.method,
                'path': request.path,
                'status_code': response.status_code,
                'response_time': response_time,
                'query_params': dict(request.GET),
                'content_type': request.content_type,
                'content_length': len(request.body) if hasattr(request, 'body') else 0
            }
        )
    
    def _log_security_event(self, event_type, risk_level, source_ip, user_agent, description, details):
        """Log security event."""
        SecurityEvent.objects.create(
            event_type=event_type,
            risk_level=risk_level,
            source_ip=source_ip,
            user_agent=user_agent,
            description=description,
            details=details
        )
        
        logger.warning(f"Security event: {event_type} from {source_ip} - {description}")
    
    def _add_security_headers(self, response):
        """Add security headers to response."""
        # Content Security Policy
        response['Content-Security-Policy'] = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline'; "
            "style-src 'self' 'unsafe-inline'; "
            "img-src 'self' data: https:; "
            "font-src 'self' data:; "
            "connect-src 'self'; "
            "frame-ancestors 'none'; "
            "base-uri 'self'; "
            "form-action 'self'"
        )
        
        # Security headers
        response['X-Content-Type-Options'] = 'nosniff'
        response['X-Frame-Options'] = 'DENY'
        response['X-XSS-Protection'] = '1; mode=block'
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        response['Permissions-Policy'] = 'geolocation=(), microphone=(), camera=()'
        
        # HSTS (only for HTTPS)
        if not settings.DEBUG:
            response['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains; preload'
        
        # Remove server information
        if 'Server' in response:
            del response['Server']
