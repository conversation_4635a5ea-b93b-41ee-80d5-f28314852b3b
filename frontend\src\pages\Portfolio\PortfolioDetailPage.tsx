// TrustVault - Portfolio Detail Page

import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  ArrowBack,
  Edit,
  Add,
  TrendingUp,
  TrendingDown,
} from '@mui/icons-material';
import { Helmet } from 'react-helmet-async';
import { useQuery } from 'react-query';

// Services
import apiService from '../../services/api';

const PortfolioDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const { data: portfolio, isLoading } = useQuery(
    ['portfolio', id],
    () => apiService.getPortfolio(id!),
    {
      enabled: !!id,
    }
  );

  const formatCurrency = (value: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(parseFloat(value));
  };

  const formatPercentage = (value: string) => {
    const num = parseFloat(value);
    return `${num >= 0 ? '+' : ''}${num.toFixed(2)}%`;
  };

  if (isLoading) {
    return <Typography>Loading portfolio...</Typography>;
  }

  if (!portfolio) {
    return <Typography>Portfolio not found</Typography>;
  }

  return (
    <>
      <Helmet>
        <title>{portfolio.name} - TrustVault</title>
        <meta name="description" content={`Portfolio details for ${portfolio.name}`} />
      </Helmet>

      <Box>
        {/* Header */}
        <Box display="flex" alignItems="center" gap={2} mb={4}>
          <IconButton onClick={() => navigate('/portfolios')}>
            <ArrowBack />
          </IconButton>
          <Box flexGrow={1}>
            <Typography variant="h4" component="h1" gutterBottom>
              {portfolio.name}
            </Typography>
            <Typography variant="body1" color="text.secondary">
              {portfolio.description || 'No description'}
            </Typography>
          </Box>
          <Button
            variant="outlined"
            startIcon={<Edit />}
            onClick={() => navigate(`/portfolios/${id}/edit`)}
          >
            Edit
          </Button>
        </Box>

        {/* Portfolio Summary */}
        <Grid container spacing={3} mb={4}>
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Portfolio Overview
                </Typography>
                
                <Grid container spacing={3}>
                  <Grid item xs={6} md={3}>
                    <Typography variant="body2" color="text.secondary">
                      Total Value
                    </Typography>
                    <Typography variant="h4" color="primary">
                      {formatCurrency(portfolio.total_value)}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={6} md={3}>
                    <Typography variant="body2" color="text.secondary">
                      Holdings
                    </Typography>
                    <Typography variant="h4">
                      {portfolio.holdings?.length || 0}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={6} md={3}>
                    <Typography variant="body2" color="text.secondary">
                      Type
                    </Typography>
                    <Chip
                      label={portfolio.portfolio_type}
                      color="primary"
                      variant="outlined"
                    />
                  </Grid>
                  
                  <Grid item xs={6} md={3}>
                    <Typography variant="body2" color="text.secondary">
                      Currency
                    </Typography>
                    <Typography variant="h4">
                      {portfolio.currency}
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Quick Actions
                </Typography>
                
                <Box display="flex" flexDirection="column" gap={2}>
                  <Button
                    variant="contained"
                    startIcon={<Add />}
                    onClick={() => navigate(`/portfolios/${id}/add-holding`)}
                  >
                    Add Holding
                  </Button>
                  
                  <Button
                    variant="outlined"
                    onClick={() => navigate(`/portfolios/${id}/transactions`)}
                  >
                    View Transactions
                  </Button>
                  
                  <Button
                    variant="outlined"
                    onClick={() => navigate(`/portfolios/${id}/analytics`)}
                  >
                    Analytics
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Holdings Table */}
        <Card>
          <CardContent>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
              <Typography variant="h6">
                Holdings
              </Typography>
              <Button
                variant="contained"
                size="small"
                startIcon={<Add />}
                onClick={() => navigate(`/portfolios/${id}/add-holding`)}
              >
                Add Holding
              </Button>
            </Box>

            {portfolio.holdings && portfolio.holdings.length > 0 ? (
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Asset</TableCell>
                      <TableCell>Symbol</TableCell>
                      <TableCell align="right">Quantity</TableCell>
                      <TableCell align="right">Avg Cost</TableCell>
                      <TableCell align="right">Current Value</TableCell>
                      <TableCell align="right">P&L</TableCell>
                      <TableCell align="right">P&L %</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {portfolio.holdings.map((holding) => (
                      <TableRow key={holding.id}>
                        <TableCell>
                          <Box>
                            <Typography variant="body2" fontWeight="medium">
                              {holding.asset.name}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {holding.asset.asset_type}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" fontWeight="medium">
                            {holding.asset.symbol}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          {parseFloat(holding.quantity).toLocaleString()}
                        </TableCell>
                        <TableCell align="right">
                          {formatCurrency(holding.average_cost)}
                        </TableCell>
                        <TableCell align="right">
                          {formatCurrency(holding.current_value)}
                        </TableCell>
                        <TableCell align="right">
                          <Box display="flex" alignItems="center" justifyContent="flex-end" gap={1}>
                            {parseFloat(holding.profit_loss) >= 0 ? (
                              <TrendingUp color="success" fontSize="small" />
                            ) : (
                              <TrendingDown color="error" fontSize="small" />
                            )}
                            <Typography
                              variant="body2"
                              color={
                                parseFloat(holding.profit_loss) >= 0
                                  ? 'success.main'
                                  : 'error.main'
                              }
                            >
                              {formatCurrency(holding.profit_loss)}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell align="right">
                          <Typography
                            variant="body2"
                            color={
                              parseFloat(holding.profit_loss_percentage) >= 0
                                ? 'success.main'
                                : 'error.main'
                            }
                          >
                            {formatPercentage(holding.profit_loss_percentage)}
                          </Typography>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            ) : (
              <Box textAlign="center" py={4}>
                <Typography variant="h6" color="text.secondary" gutterBottom>
                  No Holdings Yet
                </Typography>
                <Typography variant="body2" color="text.secondary" mb={3}>
                  Add your first holding to start tracking your investments
                </Typography>
                <Button
                  variant="contained"
                  startIcon={<Add />}
                  onClick={() => navigate(`/portfolios/${id}/add-holding`)}
                >
                  Add First Holding
                </Button>
              </Box>
            )}
          </CardContent>
        </Card>
      </Box>
    </>
  );
};

export default PortfolioDetailPage;
