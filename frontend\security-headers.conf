# TrustVault - Frontend Security Headers

# Security headers
add_header X-Frame-Options "DENY" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
add_header Permissions-Policy "geolocation=(), microphone=(), camera=()" always;

# HSTS (uncomment for production with HTTPS)
# add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;

# Remove server information
server_tokens off;
