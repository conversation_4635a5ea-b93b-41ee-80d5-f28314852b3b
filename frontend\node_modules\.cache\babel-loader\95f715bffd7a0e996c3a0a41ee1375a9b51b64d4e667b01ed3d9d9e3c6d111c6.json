{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\TrustVault\\\\frontend\\\\src\\\\pages\\\\Portfolio\\\\CreatePortfolioPage.tsx\",\n  _s = $RefreshSig$();\n// TrustVault - Create Portfolio Page\n\nimport React, { useState } from 'react';\nimport { Box, Typography, Paper, TextField, Button, FormControl, InputLabel, Select, MenuItem, FormControlLabel, Switch, Alert, CircularProgress } from '@mui/material';\nimport { ArrowBack, Save } from '@mui/icons-material';\nimport { Helmet } from 'react-helmet-async';\nimport { useNavigate } from 'react-router-dom';\nimport { useMutation, useQueryClient } from 'react-query';\nimport { toast } from 'react-hot-toast';\n\n// Services\nimport apiService from '../../services/api';\n\n// Types\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CreatePortfolioPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const queryClient = useQueryClient();\n\n  // Add console log to verify component is loading\n  console.log('CreatePortfolioPage: Component is loading');\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    portfolio_type: 'MODERATE',\n    is_public: false\n  });\n  const [errors, setErrors] = useState({});\n\n  // Move hooks before any conditional returns\n  const createPortfolioMutation = useMutation(data => apiService.createPortfolio(data), {\n    onSuccess: portfolio => {\n      toast.success('Portfolio created successfully!');\n      queryClient.invalidateQueries('portfolios');\n      navigate(`/portfolios/${portfolio.id}`);\n    },\n    onError: error => {\n      var _error$response;\n      console.error('Create portfolio error:', error);\n      if ((_error$response = error.response) !== null && _error$response !== void 0 && _error$response.data) {\n        setErrors(error.response.data);\n      } else {\n        toast.error('Failed to create portfolio. Please try again.');\n      }\n    }\n  });\n  const handleInputChange = field => event => {\n    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n\n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: ''\n      }));\n    }\n  };\n  const handleSubmit = event => {\n    event.preventDefault();\n\n    // Basic validation\n    const newErrors = {};\n    if (!formData.name.trim()) {\n      newErrors.name = 'Portfolio name is required';\n    }\n    if (Object.keys(newErrors).length > 0) {\n      setErrors(newErrors);\n      return;\n    }\n    createPortfolioMutation.mutate(formData);\n  };\n  const portfolioTypes = [{\n    value: 'CONSERVATIVE',\n    label: 'Conservative',\n    description: 'Low risk, stable returns'\n  }, {\n    value: 'MODERATE',\n    label: 'Moderate',\n    description: 'Balanced risk and return'\n  }, {\n    value: 'AGGRESSIVE',\n    label: 'Aggressive',\n    description: 'High risk, high potential returns'\n  }, {\n    value: 'CUSTOM',\n    label: 'Custom',\n    description: 'Custom allocation strategy'\n  }];\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"title\", {\n        children: \"Create Portfolio - TrustVault\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"description\",\n        content: \"Create a new investment portfolio\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        maxWidth: 800,\n        mx: 'auto',\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'none'\n        },\n        children: \"CreatePortfolioPage-Loaded\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        alignItems: \"center\",\n        mb: 3,\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          startIcon: /*#__PURE__*/_jsxDEV(ArrowBack, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 24\n          }, this),\n          onClick: () => navigate('/portfolios'),\n          sx: {\n            mr: 2\n          },\n          children: \"Back to Portfolios\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          component: \"h1\",\n          children: \"Create New Portfolio\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            flexDirection: \"column\",\n            gap: 3,\n            children: [/*#__PURE__*/_jsxDEV(TextField, {\n              label: \"Portfolio Name\",\n              value: formData.name,\n              onChange: handleInputChange('name'),\n              error: !!errors.name,\n              helperText: errors.name,\n              required: true,\n              fullWidth: true,\n              placeholder: \"e.g., My Investment Portfolio\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              label: \"Description\",\n              value: formData.description,\n              onChange: handleInputChange('description'),\n              error: !!errors.description,\n              helperText: errors.description,\n              multiline: true,\n              rows: 3,\n              fullWidth: true,\n              placeholder: \"Describe your investment strategy and goals...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Portfolio Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: formData.portfolio_type,\n                onChange: handleInputChange('portfolio_type'),\n                label: \"Portfolio Type\",\n                children: portfolioTypes.map(type => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: type.value,\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body1\",\n                      children: type.label\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 179,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"text.secondary\",\n                      children: type.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 180,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 178,\n                    columnNumber: 23\n                  }, this)\n                }, type.value, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n              control: /*#__PURE__*/_jsxDEV(Switch, {\n                checked: formData.is_public,\n                onChange: handleInputChange('is_public')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 19\n              }, this),\n              label: /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: \"Make Portfolio Public\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"text.secondary\",\n                  children: \"Allow others to view your portfolio performance (holdings remain private)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this), Object.keys(errors).length > 0 && /*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"error\",\n              children: \"Please fix the errors above and try again.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              justifyContent: \"flex-end\",\n              gap: 2,\n              mt: 2,\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                onClick: () => navigate('/portfolios'),\n                disabled: createPortfolioMutation.isLoading,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                type: \"submit\",\n                variant: \"contained\",\n                startIcon: createPortfolioMutation.isLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 66\n                }, this) : /*#__PURE__*/_jsxDEV(Save, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 99\n                }, this),\n                disabled: createPortfolioMutation.isLoading,\n                children: createPortfolioMutation.isLoading ? 'Creating...' : 'Create Portfolio'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mt: 3,\n          bgcolor: 'background.default'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Getting Started\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: \"After creating your portfolio, you can:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          component: \"ul\",\n          sx: {\n            mt: 1,\n            pl: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            component: \"li\",\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Add assets and track your investments\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            component: \"li\",\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Record transactions and monitor performance\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            component: \"li\",\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"View detailed analytics and reports\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            component: \"li\",\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Set up alerts and notifications\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(CreatePortfolioPage, \"LVBbV6xPhmxwLcoYxEz/sjAItyM=\", false, function () {\n  return [useNavigate, useQueryClient, useMutation];\n});\n_c = CreatePortfolioPage;\nexport default CreatePortfolioPage;\nvar _c;\n$RefreshReg$(_c, \"CreatePortfolioPage\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Paper", "TextField", "<PERSON><PERSON>", "FormControl", "InputLabel", "Select", "MenuItem", "FormControlLabel", "Switch", "<PERSON><PERSON>", "CircularProgress", "ArrowBack", "Save", "<PERSON><PERSON><PERSON>", "useNavigate", "useMutation", "useQueryClient", "toast", "apiService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CreatePortfolioPage", "_s", "navigate", "queryClient", "console", "log", "formData", "setFormData", "name", "description", "portfolio_type", "is_public", "errors", "setErrors", "createPortfolioMutation", "data", "createPortfolio", "onSuccess", "portfolio", "success", "invalidateQueries", "id", "onError", "error", "_error$response", "response", "handleInputChange", "field", "event", "value", "target", "type", "checked", "prev", "handleSubmit", "preventDefault", "newErrors", "trim", "Object", "keys", "length", "mutate", "portfolioTypes", "label", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "content", "sx", "max<PERSON><PERSON><PERSON>", "mx", "p", "style", "display", "alignItems", "mb", "startIcon", "onClick", "mr", "variant", "component", "onSubmit", "flexDirection", "gap", "onChange", "helperText", "required", "fullWidth", "placeholder", "multiline", "rows", "map", "color", "control", "severity", "justifyContent", "mt", "disabled", "isLoading", "size", "bgcolor", "gutterBottom", "pl", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/TrustVault/frontend/src/pages/Portfolio/CreatePortfolioPage.tsx"], "sourcesContent": ["// TrustVault - Create Portfolio Page\n\nimport React, { useState } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  TextField,\n  Button,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  FormControlLabel,\n  Switch,\n  Alert,\n  CircularProgress,\n} from '@mui/material';\nimport { ArrowBack, Save } from '@mui/icons-material';\nimport { Helmet } from 'react-helmet-async';\nimport { useNavigate } from 'react-router-dom';\nimport { useMutation, useQueryClient } from 'react-query';\nimport { toast } from 'react-hot-toast';\n\n// Services\nimport apiService from '../../services/api';\n\n// Types\ninterface CreatePortfolioForm {\n  name: string;\n  description: string;\n  portfolio_type: 'CONSERVATIVE' | 'MODERATE' | 'AGGRESSIVE' | 'CUSTOM';\n  is_public: boolean;\n}\n\nconst CreatePortfolioPage: React.FC = () => {\n  const navigate = useNavigate();\n  const queryClient = useQueryClient();\n\n  // Add console log to verify component is loading\n  console.log('CreatePortfolioPage: Component is loading');\n\n  const [formData, setFormData] = useState<CreatePortfolioForm>({\n    name: '',\n    description: '',\n    portfolio_type: 'MODERATE',\n    is_public: false,\n  });\n\n  const [errors, setErrors] = useState<Record<string, string>>({});\n\n  // Move hooks before any conditional returns\n  const createPortfolioMutation = useMutation(\n    (data: CreatePortfolioForm) => apiService.createPortfolio(data),\n    {\n      onSuccess: (portfolio) => {\n        toast.success('Portfolio created successfully!');\n        queryClient.invalidateQueries('portfolios');\n        navigate(`/portfolios/${portfolio.id}`);\n      },\n      onError: (error: any) => {\n        console.error('Create portfolio error:', error);\n        if (error.response?.data) {\n          setErrors(error.response.data);\n        } else {\n          toast.error('Failed to create portfolio. Please try again.');\n        }\n      },\n    }\n  );\n\n  const handleInputChange = (field: keyof CreatePortfolioForm) => (\n    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | any\n  ) => {\n    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;\n    setFormData(prev => ({\n      ...prev,\n      [field]: value,\n    }));\n    \n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: '',\n      }));\n    }\n  };\n\n  const handleSubmit = (event: React.FormEvent) => {\n    event.preventDefault();\n    \n    // Basic validation\n    const newErrors: Record<string, string> = {};\n    \n    if (!formData.name.trim()) {\n      newErrors.name = 'Portfolio name is required';\n    }\n    \n    if (Object.keys(newErrors).length > 0) {\n      setErrors(newErrors);\n      return;\n    }\n    \n    createPortfolioMutation.mutate(formData);\n  };\n\n  const portfolioTypes = [\n    { value: 'CONSERVATIVE', label: 'Conservative', description: 'Low risk, stable returns' },\n    { value: 'MODERATE', label: 'Moderate', description: 'Balanced risk and return' },\n    { value: 'AGGRESSIVE', label: 'Aggressive', description: 'High risk, high potential returns' },\n    { value: 'CUSTOM', label: 'Custom', description: 'Custom allocation strategy' },\n  ];\n\n  return (\n    <>\n      <Helmet>\n        <title>Create Portfolio - TrustVault</title>\n        <meta name=\"description\" content=\"Create a new investment portfolio\" />\n      </Helmet>\n\n      <Box sx={{ maxWidth: 800, mx: 'auto', p: 3 }}>\n        {/* Debug indicator */}\n        <div style={{ display: 'none' }}>CreatePortfolioPage-Loaded</div>\n        {/* Header */}\n        <Box display=\"flex\" alignItems=\"center\" mb={3}>\n          <Button\n            startIcon={<ArrowBack />}\n            onClick={() => navigate('/portfolios')}\n            sx={{ mr: 2 }}\n          >\n            Back to Portfolios\n          </Button>\n          <Typography variant=\"h4\" component=\"h1\">\n            Create New Portfolio\n          </Typography>\n        </Box>\n\n        {/* Form */}\n        <Paper sx={{ p: 4 }}>\n          <form onSubmit={handleSubmit}>\n            <Box display=\"flex\" flexDirection=\"column\" gap={3}>\n              {/* Portfolio Name */}\n              <TextField\n                label=\"Portfolio Name\"\n                value={formData.name}\n                onChange={handleInputChange('name')}\n                error={!!errors.name}\n                helperText={errors.name}\n                required\n                fullWidth\n                placeholder=\"e.g., My Investment Portfolio\"\n              />\n\n              {/* Description */}\n              <TextField\n                label=\"Description\"\n                value={formData.description}\n                onChange={handleInputChange('description')}\n                error={!!errors.description}\n                helperText={errors.description}\n                multiline\n                rows={3}\n                fullWidth\n                placeholder=\"Describe your investment strategy and goals...\"\n              />\n\n              {/* Portfolio Type */}\n              <FormControl fullWidth>\n                <InputLabel>Portfolio Type</InputLabel>\n                <Select\n                  value={formData.portfolio_type}\n                  onChange={handleInputChange('portfolio_type')}\n                  label=\"Portfolio Type\"\n                >\n                  {portfolioTypes.map((type) => (\n                    <MenuItem key={type.value} value={type.value}>\n                      <Box>\n                        <Typography variant=\"body1\">{type.label}</Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          {type.description}\n                        </Typography>\n                      </Box>\n                    </MenuItem>\n                  ))}\n                </Select>\n              </FormControl>\n\n              {/* Public Portfolio */}\n              <FormControlLabel\n                control={\n                  <Switch\n                    checked={formData.is_public}\n                    onChange={handleInputChange('is_public')}\n                  />\n                }\n                label={\n                  <Box>\n                    <Typography variant=\"body1\">Make Portfolio Public</Typography>\n                    <Typography variant=\"caption\" color=\"text.secondary\">\n                      Allow others to view your portfolio performance (holdings remain private)\n                    </Typography>\n                  </Box>\n                }\n              />\n\n              {/* Error Display */}\n              {Object.keys(errors).length > 0 && (\n                <Alert severity=\"error\">\n                  Please fix the errors above and try again.\n                </Alert>\n              )}\n\n              {/* Submit Button */}\n              <Box display=\"flex\" justifyContent=\"flex-end\" gap={2} mt={2}>\n                <Button\n                  variant=\"outlined\"\n                  onClick={() => navigate('/portfolios')}\n                  disabled={createPortfolioMutation.isLoading}\n                >\n                  Cancel\n                </Button>\n                <Button\n                  type=\"submit\"\n                  variant=\"contained\"\n                  startIcon={createPortfolioMutation.isLoading ? <CircularProgress size={20} /> : <Save />}\n                  disabled={createPortfolioMutation.isLoading}\n                >\n                  {createPortfolioMutation.isLoading ? 'Creating...' : 'Create Portfolio'}\n                </Button>\n              </Box>\n            </Box>\n          </form>\n        </Paper>\n\n        {/* Info Box */}\n        <Paper sx={{ p: 3, mt: 3, bgcolor: 'background.default' }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Getting Started\n          </Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            After creating your portfolio, you can:\n          </Typography>\n          <Box component=\"ul\" sx={{ mt: 1, pl: 2 }}>\n            <Typography component=\"li\" variant=\"body2\" color=\"text.secondary\">\n              Add assets and track your investments\n            </Typography>\n            <Typography component=\"li\" variant=\"body2\" color=\"text.secondary\">\n              Record transactions and monitor performance\n            </Typography>\n            <Typography component=\"li\" variant=\"body2\" color=\"text.secondary\">\n              View detailed analytics and reports\n            </Typography>\n            <Typography component=\"li\" variant=\"body2\" color=\"text.secondary\">\n              Set up alerts and notifications\n            </Typography>\n          </Box>\n        </Paper>\n      </Box>\n    </>\n  );\n};\n\nexport default CreatePortfolioPage;\n"], "mappings": ";;AAAA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,SAAS,EACTC,MAAM,EACNC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,gBAAgB,EAChBC,MAAM,EACNC,KAAK,EACLC,gBAAgB,QACX,eAAe;AACtB,SAASC,SAAS,EAAEC,IAAI,QAAQ,qBAAqB;AACrD,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,EAAEC,cAAc,QAAQ,aAAa;AACzD,SAASC,KAAK,QAAQ,iBAAiB;;AAEvC;AACA,OAAOC,UAAU,MAAM,oBAAoB;;AAE3C;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAQA,MAAMC,mBAA6B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1C,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAMY,WAAW,GAAGV,cAAc,CAAC,CAAC;;EAEpC;EACAW,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;EAExD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAAsB;IAC5DkC,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE,UAAU;IAC1BC,SAAS,EAAE;EACb,CAAC,CAAC;EAEF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGvC,QAAQ,CAAyB,CAAC,CAAC,CAAC;;EAEhE;EACA,MAAMwC,uBAAuB,GAAGtB,WAAW,CACxCuB,IAAyB,IAAKpB,UAAU,CAACqB,eAAe,CAACD,IAAI,CAAC,EAC/D;IACEE,SAAS,EAAGC,SAAS,IAAK;MACxBxB,KAAK,CAACyB,OAAO,CAAC,iCAAiC,CAAC;MAChDhB,WAAW,CAACiB,iBAAiB,CAAC,YAAY,CAAC;MAC3ClB,QAAQ,CAAC,eAAegB,SAAS,CAACG,EAAE,EAAE,CAAC;IACzC,CAAC;IACDC,OAAO,EAAGC,KAAU,IAAK;MAAA,IAAAC,eAAA;MACvBpB,OAAO,CAACmB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,KAAAC,eAAA,GAAID,KAAK,CAACE,QAAQ,cAAAD,eAAA,eAAdA,eAAA,CAAgBT,IAAI,EAAE;QACxBF,SAAS,CAACU,KAAK,CAACE,QAAQ,CAACV,IAAI,CAAC;MAChC,CAAC,MAAM;QACLrB,KAAK,CAAC6B,KAAK,CAAC,+CAA+C,CAAC;MAC9D;IACF;EACF,CACF,CAAC;EAED,MAAMG,iBAAiB,GAAIC,KAAgC,IACzDC,KAAsE,IACnE;IACH,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAAM,CAACC,IAAI,KAAK,UAAU,GAAGH,KAAK,CAACE,MAAM,CAACE,OAAO,GAAGJ,KAAK,CAACE,MAAM,CAACD,KAAK;IAC1FtB,WAAW,CAAC0B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACN,KAAK,GAAGE;IACX,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIjB,MAAM,CAACe,KAAK,CAAC,EAAE;MACjBd,SAAS,CAACoB,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACN,KAAK,GAAG;MACX,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMO,YAAY,GAAIN,KAAsB,IAAK;IAC/CA,KAAK,CAACO,cAAc,CAAC,CAAC;;IAEtB;IACA,MAAMC,SAAiC,GAAG,CAAC,CAAC;IAE5C,IAAI,CAAC9B,QAAQ,CAACE,IAAI,CAAC6B,IAAI,CAAC,CAAC,EAAE;MACzBD,SAAS,CAAC5B,IAAI,GAAG,4BAA4B;IAC/C;IAEA,IAAI8B,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAACI,MAAM,GAAG,CAAC,EAAE;MACrC3B,SAAS,CAACuB,SAAS,CAAC;MACpB;IACF;IAEAtB,uBAAuB,CAAC2B,MAAM,CAACnC,QAAQ,CAAC;EAC1C,CAAC;EAED,MAAMoC,cAAc,GAAG,CACrB;IAAEb,KAAK,EAAE,cAAc;IAAEc,KAAK,EAAE,cAAc;IAAElC,WAAW,EAAE;EAA2B,CAAC,EACzF;IAAEoB,KAAK,EAAE,UAAU;IAAEc,KAAK,EAAE,UAAU;IAAElC,WAAW,EAAE;EAA2B,CAAC,EACjF;IAAEoB,KAAK,EAAE,YAAY;IAAEc,KAAK,EAAE,YAAY;IAAElC,WAAW,EAAE;EAAoC,CAAC,EAC9F;IAAEoB,KAAK,EAAE,QAAQ;IAAEc,KAAK,EAAE,QAAQ;IAAElC,WAAW,EAAE;EAA6B,CAAC,CAChF;EAED,oBACEZ,OAAA,CAAAE,SAAA;IAAA6C,QAAA,gBACE/C,OAAA,CAACP,MAAM;MAAAsD,QAAA,gBACL/C,OAAA;QAAA+C,QAAA,EAAO;MAA6B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC5CnD,OAAA;QAAMW,IAAI,EAAC,aAAa;QAACyC,OAAO,EAAC;MAAmC;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjE,CAAC,eAETnD,OAAA,CAACtB,GAAG;MAAC2E,EAAE,EAAE;QAAEC,QAAQ,EAAE,GAAG;QAAEC,EAAE,EAAE,MAAM;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAT,QAAA,gBAE3C/C,OAAA;QAAKyD,KAAK,EAAE;UAAEC,OAAO,EAAE;QAAO,CAAE;QAAAX,QAAA,EAAC;MAA0B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAEjEnD,OAAA,CAACtB,GAAG;QAACgF,OAAO,EAAC,MAAM;QAACC,UAAU,EAAC,QAAQ;QAACC,EAAE,EAAE,CAAE;QAAAb,QAAA,gBAC5C/C,OAAA,CAAClB,MAAM;UACL+E,SAAS,eAAE7D,OAAA,CAACT,SAAS;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBW,OAAO,EAAEA,CAAA,KAAMzD,QAAQ,CAAC,aAAa,CAAE;UACvCgD,EAAE,EAAE;YAAEU,EAAE,EAAE;UAAE,CAAE;UAAAhB,QAAA,EACf;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnD,OAAA,CAACrB,UAAU;UAACqF,OAAO,EAAC,IAAI;UAACC,SAAS,EAAC,IAAI;UAAAlB,QAAA,EAAC;QAExC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNnD,OAAA,CAACpB,KAAK;QAACyE,EAAE,EAAE;UAAEG,CAAC,EAAE;QAAE,CAAE;QAAAT,QAAA,eAClB/C,OAAA;UAAMkE,QAAQ,EAAE7B,YAAa;UAAAU,QAAA,eAC3B/C,OAAA,CAACtB,GAAG;YAACgF,OAAO,EAAC,MAAM;YAACS,aAAa,EAAC,QAAQ;YAACC,GAAG,EAAE,CAAE;YAAArB,QAAA,gBAEhD/C,OAAA,CAACnB,SAAS;cACRiE,KAAK,EAAC,gBAAgB;cACtBd,KAAK,EAAEvB,QAAQ,CAACE,IAAK;cACrB0D,QAAQ,EAAExC,iBAAiB,CAAC,MAAM,CAAE;cACpCH,KAAK,EAAE,CAAC,CAACX,MAAM,CAACJ,IAAK;cACrB2D,UAAU,EAAEvD,MAAM,CAACJ,IAAK;cACxB4D,QAAQ;cACRC,SAAS;cACTC,WAAW,EAAC;YAA+B;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC,eAGFnD,OAAA,CAACnB,SAAS;cACRiE,KAAK,EAAC,aAAa;cACnBd,KAAK,EAAEvB,QAAQ,CAACG,WAAY;cAC5ByD,QAAQ,EAAExC,iBAAiB,CAAC,aAAa,CAAE;cAC3CH,KAAK,EAAE,CAAC,CAACX,MAAM,CAACH,WAAY;cAC5B0D,UAAU,EAAEvD,MAAM,CAACH,WAAY;cAC/B8D,SAAS;cACTC,IAAI,EAAE,CAAE;cACRH,SAAS;cACTC,WAAW,EAAC;YAAgD;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC,eAGFnD,OAAA,CAACjB,WAAW;cAACyF,SAAS;cAAAzB,QAAA,gBACpB/C,OAAA,CAAChB,UAAU;gBAAA+D,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACvCnD,OAAA,CAACf,MAAM;gBACL+C,KAAK,EAAEvB,QAAQ,CAACI,cAAe;gBAC/BwD,QAAQ,EAAExC,iBAAiB,CAAC,gBAAgB,CAAE;gBAC9CiB,KAAK,EAAC,gBAAgB;gBAAAC,QAAA,EAErBF,cAAc,CAAC+B,GAAG,CAAE1C,IAAI,iBACvBlC,OAAA,CAACd,QAAQ;kBAAkB8C,KAAK,EAAEE,IAAI,CAACF,KAAM;kBAAAe,QAAA,eAC3C/C,OAAA,CAACtB,GAAG;oBAAAqE,QAAA,gBACF/C,OAAA,CAACrB,UAAU;sBAACqF,OAAO,EAAC,OAAO;sBAAAjB,QAAA,EAAEb,IAAI,CAACY;oBAAK;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eACrDnD,OAAA,CAACrB,UAAU;sBAACqF,OAAO,EAAC,SAAS;sBAACa,KAAK,EAAC,gBAAgB;sBAAA9B,QAAA,EACjDb,IAAI,CAACtB;oBAAW;sBAAAoC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACP,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC,GANOjB,IAAI,CAACF,KAAK;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAOf,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGdnD,OAAA,CAACb,gBAAgB;cACf2F,OAAO,eACL9E,OAAA,CAACZ,MAAM;gBACL+C,OAAO,EAAE1B,QAAQ,CAACK,SAAU;gBAC5BuD,QAAQ,EAAExC,iBAAiB,CAAC,WAAW;cAAE;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CACF;cACDL,KAAK,eACH9C,OAAA,CAACtB,GAAG;gBAAAqE,QAAA,gBACF/C,OAAA,CAACrB,UAAU;kBAACqF,OAAO,EAAC,OAAO;kBAAAjB,QAAA,EAAC;gBAAqB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC9DnD,OAAA,CAACrB,UAAU;kBAACqF,OAAO,EAAC,SAAS;kBAACa,KAAK,EAAC,gBAAgB;kBAAA9B,QAAA,EAAC;gBAErD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,EAGDV,MAAM,CAACC,IAAI,CAAC3B,MAAM,CAAC,CAAC4B,MAAM,GAAG,CAAC,iBAC7B3C,OAAA,CAACX,KAAK;cAAC0F,QAAQ,EAAC,OAAO;cAAAhC,QAAA,EAAC;YAExB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CACR,eAGDnD,OAAA,CAACtB,GAAG;cAACgF,OAAO,EAAC,MAAM;cAACsB,cAAc,EAAC,UAAU;cAACZ,GAAG,EAAE,CAAE;cAACa,EAAE,EAAE,CAAE;cAAAlC,QAAA,gBAC1D/C,OAAA,CAAClB,MAAM;gBACLkF,OAAO,EAAC,UAAU;gBAClBF,OAAO,EAAEA,CAAA,KAAMzD,QAAQ,CAAC,aAAa,CAAE;gBACvC6E,QAAQ,EAAEjE,uBAAuB,CAACkE,SAAU;gBAAApC,QAAA,EAC7C;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTnD,OAAA,CAAClB,MAAM;gBACLoD,IAAI,EAAC,QAAQ;gBACb8B,OAAO,EAAC,WAAW;gBACnBH,SAAS,EAAE5C,uBAAuB,CAACkE,SAAS,gBAAGnF,OAAA,CAACV,gBAAgB;kBAAC8F,IAAI,EAAE;gBAAG;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAGnD,OAAA,CAACR,IAAI;kBAAAwD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzF+B,QAAQ,EAAEjE,uBAAuB,CAACkE,SAAU;gBAAApC,QAAA,EAE3C9B,uBAAuB,CAACkE,SAAS,GAAG,aAAa,GAAG;cAAkB;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGRnD,OAAA,CAACpB,KAAK;QAACyE,EAAE,EAAE;UAAEG,CAAC,EAAE,CAAC;UAAEyB,EAAE,EAAE,CAAC;UAAEI,OAAO,EAAE;QAAqB,CAAE;QAAAtC,QAAA,gBACxD/C,OAAA,CAACrB,UAAU;UAACqF,OAAO,EAAC,IAAI;UAACsB,YAAY;UAAAvC,QAAA,EAAC;QAEtC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbnD,OAAA,CAACrB,UAAU;UAACqF,OAAO,EAAC,OAAO;UAACa,KAAK,EAAC,gBAAgB;UAAA9B,QAAA,EAAC;QAEnD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbnD,OAAA,CAACtB,GAAG;UAACuF,SAAS,EAAC,IAAI;UAACZ,EAAE,EAAE;YAAE4B,EAAE,EAAE,CAAC;YAAEM,EAAE,EAAE;UAAE,CAAE;UAAAxC,QAAA,gBACvC/C,OAAA,CAACrB,UAAU;YAACsF,SAAS,EAAC,IAAI;YAACD,OAAO,EAAC,OAAO;YAACa,KAAK,EAAC,gBAAgB;YAAA9B,QAAA,EAAC;UAElE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbnD,OAAA,CAACrB,UAAU;YAACsF,SAAS,EAAC,IAAI;YAACD,OAAO,EAAC,OAAO;YAACa,KAAK,EAAC,gBAAgB;YAAA9B,QAAA,EAAC;UAElE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbnD,OAAA,CAACrB,UAAU;YAACsF,SAAS,EAAC,IAAI;YAACD,OAAO,EAAC,OAAO;YAACa,KAAK,EAAC,gBAAgB;YAAA9B,QAAA,EAAC;UAElE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbnD,OAAA,CAACrB,UAAU;YAACsF,SAAS,EAAC,IAAI;YAACD,OAAO,EAAC,OAAO;YAACa,KAAK,EAAC,gBAAgB;YAAA9B,QAAA,EAAC;UAElE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAAC/C,EAAA,CAlOID,mBAA6B;EAAA,QAChBT,WAAW,EACRE,cAAc,EAeFD,WAAW;AAAA;AAAA6F,EAAA,GAjBvCrF,mBAA6B;AAoOnC,eAAeA,mBAAmB;AAAC,IAAAqF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}