// TrustVault - Dashboard Page

import React from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Paper,
  List,
  ListItem,
  ListItemText,
  Chip,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  TrendingUp,
  Security,
  AccountBalance,
  Warning,
  Refresh,
} from '@mui/icons-material';
import { Helmet } from 'react-helmet-async';
import { useQuery } from 'react-query';

// Store
import { useAuthStore } from '../../store/authStore';

// Services
import apiService from '../../services/api';

const DashboardPage: React.FC = () => {
  const { user } = useAuthStore();

  // Fetch dashboard data
  const { data: portfolios, isLoading: portfoliosLoading } = useQuery(
    'portfolios',
    apiService.getPortfolios,
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  );

  const { data: securityDashboard, isLoading: securityLoading } = useQuery(
    'security-dashboard',
    apiService.getSecurityDashboard,
    {
      staleTime: 2 * 60 * 1000, // 2 minutes
    }
  );

  const totalPortfolioValue = portfolios?.reduce(
    (sum, portfolio) => sum + parseFloat(portfolio.total_value),
    0
  ) || 0;

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(value);
  };

  const getSecurityStatusColor = (unresolved: number) => {
    if (unresolved === 0) return 'success';
    if (unresolved <= 5) return 'warning';
    return 'error';
  };

  return (
    <>
      <Helmet>
        <title>Dashboard - TrustVault</title>
        <meta name="description" content="Your secure portfolio dashboard" />
      </Helmet>

      <Box>
        {/* Welcome Section */}
        <Box mb={4}>
          <Typography variant="h4" component="h1" gutterBottom>
            Welcome back, {user?.first_name}!
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Here's an overview of your portfolio and security status.
          </Typography>
        </Box>

        {/* Key Metrics */}
        <Grid container spacing={3} mb={4}>
          {/* Total Portfolio Value */}
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography color="text.secondary" gutterBottom>
                      Total Portfolio Value
                    </Typography>
                    <Typography variant="h5" component="div">
                      {formatCurrency(totalPortfolioValue)}
                    </Typography>
                  </Box>
                  <TrendingUp color="primary" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Number of Portfolios */}
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography color="text.secondary" gutterBottom>
                      Active Portfolios
                    </Typography>
                    <Typography variant="h5" component="div">
                      {portfolios?.length || 0}
                    </Typography>
                  </Box>
                  <AccountBalance color="primary" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Security Status */}
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography color="text.secondary" gutterBottom>
                      Security Status
                    </Typography>
                    <Chip
                      label={
                        securityDashboard?.security_events.unresolved === 0
                          ? 'Secure'
                          : `${securityDashboard?.security_events.unresolved} Issues`
                      }
                      color={getSecurityStatusColor(
                        securityDashboard?.security_events.unresolved || 0
                      )}
                      size="small"
                    />
                  </Box>
                  <Security color="primary" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Recent Activity */}
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography color="text.secondary" gutterBottom>
                      Login Attempts (24h)
                    </Typography>
                    <Typography variant="h5" component="div">
                      {securityDashboard?.login_attempts.successful_last_24h || 0}
                    </Typography>
                  </Box>
                  <Tooltip title="Refresh">
                    <IconButton size="small">
                      <Refresh />
                    </IconButton>
                  </Tooltip>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Content Grid */}
        <Grid container spacing={3}>
          {/* Portfolio Overview */}
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                  <Typography variant="h6" component="h2">
                    Portfolio Overview
                  </Typography>
                  <Tooltip title="Refresh">
                    <IconButton size="small">
                      <Refresh />
                    </IconButton>
                  </Tooltip>
                </Box>

                {portfoliosLoading ? (
                  <Typography>Loading portfolios...</Typography>
                ) : portfolios && portfolios.length > 0 ? (
                  <List>
                    {portfolios.slice(0, 5).map((portfolio) => (
                      <ListItem key={portfolio.id} divider>
                        <ListItemText
                          primary={portfolio.name}
                          secondary={`${portfolio.portfolio_type} • ${portfolio.holdings_count || 0} holdings`}
                        />
                        <Box textAlign="right">
                          <Typography variant="h6">
                            {formatCurrency(parseFloat(portfolio.total_value))}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {portfolio.currency}
                          </Typography>
                        </Box>
                      </ListItem>
                    ))}
                  </List>
                ) : (
                  <Box textAlign="center" py={4}>
                    <AccountBalance sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                    <Typography variant="h6" color="text.secondary" gutterBottom>
                      No Portfolios Yet
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Create your first portfolio to get started
                    </Typography>
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>

          {/* Security Events */}
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                  <Typography variant="h6" component="h2">
                    Security Events
                  </Typography>
                  <Tooltip title="Refresh">
                    <IconButton size="small">
                      <Refresh />
                    </IconButton>
                  </Tooltip>
                </Box>

                {securityLoading ? (
                  <Typography>Loading security data...</Typography>
                ) : securityDashboard ? (
                  <Box>
                    <Box mb={2}>
                      <Typography variant="body2" color="text.secondary">
                        Last 24 hours
                      </Typography>
                      <Typography variant="h4">
                        {securityDashboard.security_events.last_24h}
                      </Typography>
                    </Box>

                    <List dense>
                      {securityDashboard.recent_events.slice(0, 3).map((event) => (
                        <ListItem key={event.id} sx={{ px: 0 }}>
                          <ListItemText
                            primary={
                              <Box display="flex" alignItems="center" gap={1}>
                                <Warning
                                  color={
                                    event.risk_level === 'CRITICAL'
                                      ? 'error'
                                      : event.risk_level === 'HIGH'
                                      ? 'warning'
                                      : 'info'
                                  }
                                  fontSize="small"
                                />
                                <Typography variant="body2">
                                  {event.event_type.replace('_', ' ')}
                                </Typography>
                              </Box>
                            }
                            secondary={
                              <Typography variant="caption" color="text.secondary">
                                {new Date(event.created_at).toLocaleString()}
                              </Typography>
                            }
                          />
                        </ListItem>
                      ))}
                    </List>

                    {securityDashboard.security_events.unresolved > 0 && (
                      <Box mt={2}>
                        <Chip
                          label={`${securityDashboard.security_events.unresolved} Unresolved`}
                          color="warning"
                          size="small"
                          icon={<Warning />}
                        />
                      </Box>
                    )}
                  </Box>
                ) : (
                  <Typography color="text.secondary">
                    No security data available
                  </Typography>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default DashboardPage;
