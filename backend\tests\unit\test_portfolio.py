# TrustVault - Portfolio Unit Tests

import pytest
from decimal import Decimal
from django.urls import reverse
from rest_framework import status
from apps.portfolio.models import Portfolio, Asset, Holding, Transaction

@pytest.mark.django_db
class TestPortfolioModel:
    """Test Portfolio model functionality."""

    def test_create_portfolio(self, user, portfolio_data):
        """Test portfolio creation."""
        portfolio = Portfolio.objects.create(
            user=user,
            **portfolio_data
        )
        
        assert portfolio.name == portfolio_data['name']
        assert portfolio.description == portfolio_data['description']
        assert portfolio.portfolio_type == portfolio_data['portfolio_type']
        assert portfolio.currency == portfolio_data['currency']
        assert portfolio.is_public == portfolio_data['is_public']
        assert portfolio.total_value == Decimal('0.00')

    def test_portfolio_str_representation(self, user, portfolio_data):
        """Test portfolio string representation."""
        portfolio = Portfolio.objects.create(
            user=user,
            **portfolio_data
        )
        
        assert str(portfolio) == f"{portfolio_data['name']} ({user.email})"

    def test_calculate_total_value(self, user, portfolio_data):
        """Test portfolio total value calculation."""
        portfolio = Portfolio.objects.create(
            user=user,
            **portfolio_data
        )
        
        # Create asset
        asset = Asset.objects.create(
            symbol='AAPL',
            name='Apple Inc.',
            asset_type='STOCK',
            current_price='150.00',
            currency='USD'
        )
        
        # Create holding
        holding = Holding.objects.create(
            portfolio=portfolio,
            asset=asset,
            quantity=Decimal('10'),
            average_cost=Decimal('140.00'),
            current_value=Decimal('1500.00')
        )
        
        # Calculate total value
        portfolio.calculate_total_value()
        
        assert portfolio.total_value == Decimal('1500.00')


@pytest.mark.django_db
class TestAssetModel:
    """Test Asset model functionality."""

    def test_create_asset(self, asset_data):
        """Test asset creation."""
        asset = Asset.objects.create(**asset_data)
        
        assert asset.symbol == asset_data['symbol']
        assert asset.name == asset_data['name']
        assert asset.asset_type == asset_data['asset_type']
        assert asset.current_price == Decimal(asset_data['current_price'])
        assert asset.currency == asset_data['currency']

    def test_asset_str_representation(self, asset_data):
        """Test asset string representation."""
        asset = Asset.objects.create(**asset_data)
        
        assert str(asset) == f"{asset_data['symbol']} - {asset_data['name']}"


@pytest.mark.django_db
class TestHoldingModel:
    """Test Holding model functionality."""

    def test_create_holding(self, user, portfolio_data, asset_data):
        """Test holding creation."""
        portfolio = Portfolio.objects.create(user=user, **portfolio_data)
        asset = Asset.objects.create(**asset_data)
        
        holding = Holding.objects.create(
            portfolio=portfolio,
            asset=asset,
            quantity=Decimal('10'),
            average_cost=Decimal('140.00'),
            current_value=Decimal('1500.00')
        )
        
        assert holding.portfolio == portfolio
        assert holding.asset == asset
        assert holding.quantity == Decimal('10')
        assert holding.average_cost == Decimal('140.00')
        assert holding.current_value == Decimal('1500.00')

    def test_calculate_current_value(self, user, portfolio_data, asset_data):
        """Test holding current value calculation."""
        portfolio = Portfolio.objects.create(user=user, **portfolio_data)
        asset = Asset.objects.create(**asset_data)
        
        holding = Holding.objects.create(
            portfolio=portfolio,
            asset=asset,
            quantity=Decimal('10'),
            average_cost=Decimal('140.00'),
            current_value=Decimal('0.00')
        )
        
        # Calculate current value
        holding.calculate_current_value()
        
        expected_value = holding.quantity * asset.current_price
        assert holding.current_value == expected_value

    def test_profit_loss_calculation(self, user, portfolio_data, asset_data):
        """Test profit/loss calculation."""
        portfolio = Portfolio.objects.create(user=user, **portfolio_data)
        asset = Asset.objects.create(**asset_data)
        
        holding = Holding.objects.create(
            portfolio=portfolio,
            asset=asset,
            quantity=Decimal('10'),
            average_cost=Decimal('140.00'),
            current_value=Decimal('1500.00')
        )
        
        # Calculate profit/loss
        expected_profit_loss = holding.current_value - (holding.quantity * holding.average_cost)
        assert holding.profit_loss == expected_profit_loss
        
        # Calculate profit/loss percentage
        cost_basis = holding.quantity * holding.average_cost
        expected_percentage = (expected_profit_loss / cost_basis) * 100
        assert holding.profit_loss_percentage == expected_percentage


@pytest.mark.django_db
class TestTransactionModel:
    """Test Transaction model functionality."""

    def test_create_buy_transaction(self, user, portfolio_data, asset_data):
        """Test buy transaction creation."""
        portfolio = Portfolio.objects.create(user=user, **portfolio_data)
        asset = Asset.objects.create(**asset_data)
        
        transaction = Transaction.objects.create(
            portfolio=portfolio,
            asset=asset,
            transaction_type='BUY',
            quantity=Decimal('10'),
            price=Decimal('150.00'),
            total_amount=Decimal('1500.00'),
            fees=Decimal('5.00')
        )
        
        assert transaction.portfolio == portfolio
        assert transaction.asset == asset
        assert transaction.transaction_type == 'BUY'
        assert transaction.quantity == Decimal('10')
        assert transaction.price == Decimal('150.00')
        assert transaction.total_amount == Decimal('1500.00')
        assert transaction.fees == Decimal('5.00')

    def test_create_cash_transaction(self, user, portfolio_data):
        """Test cash transaction creation."""
        portfolio = Portfolio.objects.create(user=user, **portfolio_data)
        
        transaction = Transaction.objects.create(
            portfolio=portfolio,
            transaction_type='DEPOSIT',
            total_amount=Decimal('1000.00'),
            fees=Decimal('0.00'),
            notes='Initial deposit'
        )
        
        assert transaction.portfolio == portfolio
        assert transaction.asset is None
        assert transaction.transaction_type == 'DEPOSIT'
        assert transaction.total_amount == Decimal('1000.00')
        assert transaction.notes == 'Initial deposit'


@pytest.mark.django_db
class TestPortfolioAPI:
    """Test Portfolio API endpoints."""

    def test_list_portfolios(self, authenticated_client, user, portfolio_data):
        """Test listing user's portfolios."""
        # Create portfolio
        Portfolio.objects.create(user=user, **portfolio_data)
        
        response = authenticated_client.get(reverse('portfolio:portfolio-list'))
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) == 1
        assert response.data[0]['name'] == portfolio_data['name']

    def test_create_portfolio(self, authenticated_client, portfolio_data):
        """Test creating a new portfolio."""
        response = authenticated_client.post(
            reverse('portfolio:portfolio-list'),
            data=portfolio_data,
            format='json'
        )
        
        assert response.status_code == status.HTTP_201_CREATED
        assert response.data['name'] == portfolio_data['name']
        assert Portfolio.objects.filter(name=portfolio_data['name']).exists()

    def test_get_portfolio_detail(self, authenticated_client, user, portfolio_data):
        """Test getting portfolio details."""
        portfolio = Portfolio.objects.create(user=user, **portfolio_data)
        
        response = authenticated_client.get(
            reverse('portfolio:portfolio-detail', kwargs={'pk': portfolio.id})
        )
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['name'] == portfolio_data['name']

    def test_update_portfolio(self, authenticated_client, user, portfolio_data):
        """Test updating a portfolio."""
        portfolio = Portfolio.objects.create(user=user, **portfolio_data)
        
        updated_data = portfolio_data.copy()
        updated_data['name'] = 'Updated Portfolio Name'
        
        response = authenticated_client.put(
            reverse('portfolio:portfolio-detail', kwargs={'pk': portfolio.id}),
            data=updated_data,
            format='json'
        )
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['name'] == 'Updated Portfolio Name'

    def test_delete_portfolio(self, authenticated_client, user, portfolio_data):
        """Test deleting a portfolio."""
        portfolio = Portfolio.objects.create(user=user, **portfolio_data)
        
        response = authenticated_client.delete(
            reverse('portfolio:portfolio-detail', kwargs={'pk': portfolio.id})
        )
        
        assert response.status_code == status.HTTP_204_NO_CONTENT
        assert not Portfolio.objects.filter(id=portfolio.id).exists()

    def test_unauthorized_access(self, api_client, portfolio_data):
        """Test unauthorized access to portfolio endpoints."""
        response = api_client.get(reverse('portfolio:portfolio-list'))
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    def test_access_other_user_portfolio(self, authenticated_client, admin_user, portfolio_data):
        """Test accessing another user's portfolio."""
        # Create portfolio for admin user
        portfolio = Portfolio.objects.create(user=admin_user, **portfolio_data)
        
        # Try to access with regular user
        response = authenticated_client.get(
            reverse('portfolio:portfolio-detail', kwargs={'pk': portfolio.id})
        )
        
        assert response.status_code == status.HTTP_404_NOT_FOUND


@pytest.mark.django_db
class TestAssetAPI:
    """Test Asset API endpoints."""

    def test_list_assets(self, authenticated_client, asset_data):
        """Test listing available assets."""
        Asset.objects.create(**asset_data)
        
        response = authenticated_client.get(reverse('portfolio:asset-list'))
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) == 1
        assert response.data[0]['symbol'] == asset_data['symbol']

    def test_filter_assets_by_type(self, authenticated_client, asset_data):
        """Test filtering assets by type."""
        Asset.objects.create(**asset_data)
        
        # Create another asset with different type
        crypto_asset = asset_data.copy()
        crypto_asset['symbol'] = 'BTC'
        crypto_asset['name'] = 'Bitcoin'
        crypto_asset['asset_type'] = 'CRYPTO'
        Asset.objects.create(**crypto_asset)
        
        response = authenticated_client.get(
            reverse('portfolio:asset-list'),
            {'type': 'STOCK'}
        )
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) == 1
        assert response.data[0]['asset_type'] == 'STOCK'

    def test_search_assets(self, authenticated_client, asset_data):
        """Test searching assets by symbol or name."""
        Asset.objects.create(**asset_data)
        
        response = authenticated_client.get(
            reverse('portfolio:asset-list'),
            {'search': 'AAPL'}
        )
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) == 1
        assert response.data[0]['symbol'] == 'AAPL'
