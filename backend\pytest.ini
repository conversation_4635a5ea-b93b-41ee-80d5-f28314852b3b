[tool:pytest]
DJANGO_SETTINGS_MODULE = trustvault.settings.test
python_files = tests.py test_*.py *_tests.py
python_classes = Test*
python_functions = test_*
addopts = 
    --verbose
    --tb=short
    --strict-markers
    --disable-warnings
    --cov=apps
    --cov-report=html
    --cov-report=term-missing
    --cov-fail-under=80
    --reuse-db
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    security: marks tests as security tests
    unit: marks tests as unit tests
testpaths = tests
filterwarnings =
    ignore::django.utils.deprecation.RemovedInDjango50Warning
    ignore::DeprecationWarning
