{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nvar _span;\nconst _excluded = [\"children\", \"classes\", \"className\", \"label\", \"notched\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport styled, { rootShouldForwardProp } from '../styles/styled';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst NotchedOutlineRoot = styled('fieldset', {\n  name: 'MuiNotchedOutlined',\n  shouldForwardProp: rootShouldForwardProp\n})({\n  textAlign: 'left',\n  position: 'absolute',\n  bottom: 0,\n  right: 0,\n  top: -5,\n  left: 0,\n  margin: 0,\n  padding: '0 8px',\n  pointerEvents: 'none',\n  borderRadius: 'inherit',\n  borderStyle: 'solid',\n  borderWidth: 1,\n  overflow: 'hidden',\n  minWidth: '0%'\n});\nconst NotchedOutlineLegend = styled('legend', {\n  name: 'MuiNotchedOutlined',\n  shouldForwardProp: rootShouldForwardProp\n})(_ref => {\n  let {\n    ownerState,\n    theme\n  } = _ref;\n  return _extends({\n    float: 'unset',\n    // Fix conflict with bootstrap\n    width: 'auto',\n    // Fix conflict with bootstrap\n    overflow: 'hidden'\n  }, !ownerState.withLabel && {\n    padding: 0,\n    lineHeight: '11px',\n    // sync with `height` in `legend` styles\n    transition: theme.transitions.create('width', {\n      duration: 150,\n      easing: theme.transitions.easing.easeOut\n    })\n  }, ownerState.withLabel && _extends({\n    display: 'block',\n    // Fix conflict with normalize.css and sanitize.css\n    padding: 0,\n    height: 11,\n    // sync with `lineHeight` in `legend` styles\n    fontSize: '0.75em',\n    visibility: 'hidden',\n    maxWidth: 0.01,\n    transition: theme.transitions.create('max-width', {\n      duration: 50,\n      easing: theme.transitions.easing.easeOut\n    }),\n    whiteSpace: 'nowrap',\n    '& > span': {\n      paddingLeft: 5,\n      paddingRight: 5,\n      display: 'inline-block',\n      opacity: 0,\n      visibility: 'visible'\n    }\n  }, ownerState.notched && {\n    maxWidth: '100%',\n    transition: theme.transitions.create('max-width', {\n      duration: 100,\n      easing: theme.transitions.easing.easeOut,\n      delay: 50\n    })\n  }));\n});\n\n/**\n * @ignore - internal component.\n */\nexport default function NotchedOutline(props) {\n  const {\n      className,\n      label,\n      notched\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const withLabel = label != null && label !== '';\n  const ownerState = _extends({}, props, {\n    notched,\n    withLabel\n  });\n  return /*#__PURE__*/_jsx(NotchedOutlineRoot, _extends({\n    \"aria-hidden\": true,\n    className: className,\n    ownerState: ownerState\n  }, other, {\n    children: /*#__PURE__*/_jsx(NotchedOutlineLegend, {\n      ownerState: ownerState,\n      children: withLabel ? /*#__PURE__*/_jsx(\"span\", {\n        children: label\n      }) :\n      // notranslate needed while Google Translate will not fix zero-width space issue\n      _span || (_span = /*#__PURE__*/_jsx(\"span\", {\n        className: \"notranslate\",\n        children: \"\\u200B\"\n      }))\n    })\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? NotchedOutline.propTypes = {\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The label.\n   */\n  label: PropTypes.node,\n  /**\n   * If `true`, the outline is notched to accommodate the label.\n   */\n  notched: PropTypes.bool.isRequired,\n  /**\n   * @ignore\n   */\n  style: PropTypes.object\n} : void 0;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}