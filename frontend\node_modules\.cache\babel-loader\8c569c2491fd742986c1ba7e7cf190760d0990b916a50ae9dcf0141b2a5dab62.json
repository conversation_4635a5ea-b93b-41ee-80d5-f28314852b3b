{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"boundaryCount\", \"className\", \"color\", \"count\", \"defaultPage\", \"disabled\", \"getItemAriaLabel\", \"hideNextButton\", \"hidePrevButton\", \"onChange\", \"page\", \"renderItem\", \"shape\", \"showFirstButton\", \"showLastButton\", \"siblingCount\", \"size\", \"variant\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport integerPropType from '@mui/utils/integerPropType';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport { getPaginationUtilityClass } from './paginationClasses';\nimport usePagination from '../usePagination';\nimport PaginationItem from '../PaginationItem';\nimport styled from '../styles/styled';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    variant\n  } = ownerState;\n  const slots = {\n    root: ['root', variant],\n    ul: ['ul']\n  };\n  return composeClasses(slots, getPaginationUtilityClass, classes);\n};\nconst PaginationRoot = styled('nav', {\n  name: 'MuiPagination',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[ownerState.variant]];\n  }\n})({});\nconst PaginationUl = styled('ul', {\n  name: 'MuiPagination',\n  slot: 'Ul',\n  overridesResolver: (props, styles) => styles.ul\n})({\n  display: 'flex',\n  flexWrap: 'wrap',\n  alignItems: 'center',\n  padding: 0,\n  margin: 0,\n  listStyle: 'none'\n});\nfunction defaultGetAriaLabel(type, page, selected) {\n  if (type === 'page') {\n    return `${selected ? '' : 'Go to '}page ${page}`;\n  }\n  return `Go to ${type} page`;\n}\nconst Pagination = /*#__PURE__*/React.forwardRef(function Pagination(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiPagination'\n  });\n  const {\n      boundaryCount = 1,\n      className,\n      color = 'standard',\n      count = 1,\n      defaultPage = 1,\n      disabled = false,\n      getItemAriaLabel = defaultGetAriaLabel,\n      hideNextButton = false,\n      hidePrevButton = false,\n      renderItem = item => /*#__PURE__*/_jsx(PaginationItem, _extends({}, item)),\n      shape = 'circular',\n      showFirstButton = false,\n      showLastButton = false,\n      siblingCount = 1,\n      size = 'medium',\n      variant = 'text'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    items\n  } = usePagination(_extends({}, props, {\n    componentName: 'Pagination'\n  }));\n  const ownerState = _extends({}, props, {\n    boundaryCount,\n    color,\n    count,\n    defaultPage,\n    disabled,\n    getItemAriaLabel,\n    hideNextButton,\n    hidePrevButton,\n    renderItem,\n    shape,\n    showFirstButton,\n    showLastButton,\n    siblingCount,\n    size,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(PaginationRoot, _extends({\n    \"aria-label\": \"pagination navigation\",\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref\n  }, other, {\n    children: /*#__PURE__*/_jsx(PaginationUl, {\n      className: classes.ul,\n      ownerState: ownerState,\n      children: items.map((item, index) => /*#__PURE__*/_jsx(\"li\", {\n        children: renderItem(_extends({}, item, {\n          color,\n          'aria-label': getItemAriaLabel(item.type, item.page, item.selected),\n          shape,\n          size,\n          variant\n        }))\n      }, index))\n    })\n  }));\n});\n\n// @default tags synced with default values from usePagination\n\nprocess.env.NODE_ENV !== \"production\" ? Pagination.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Number of always visible pages at the beginning and end.\n   * @default 1\n   */\n  boundaryCount: integerPropType,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The active color.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   * @default 'standard'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['primary', 'secondary', 'standard']), PropTypes.string]),\n  /**\n   * The total number of pages.\n   * @default 1\n   */\n  count: integerPropType,\n  /**\n   * The page selected by default when the component is uncontrolled.\n   * @default 1\n   */\n  defaultPage: integerPropType,\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * Accepts a function which returns a string value that provides a user-friendly name for the current page.\n   * This is important for screen reader users.\n   *\n   * For localization purposes, you can use the provided [translations](/material-ui/guides/localization/).\n   * @param {string} type The link or button type to format ('page' | 'first' | 'last' | 'next' | 'previous' | 'start-ellipsis' | 'end-ellipsis'). Defaults to 'page'.\n   * @param {number} page The page number to format.\n   * @param {bool} selected If true, the current page is selected.\n   * @returns {string}\n   */\n  getItemAriaLabel: PropTypes.func,\n  /**\n   * If `true`, hide the next-page button.\n   * @default false\n   */\n  hideNextButton: PropTypes.bool,\n  /**\n   * If `true`, hide the previous-page button.\n   * @default false\n   */\n  hidePrevButton: PropTypes.bool,\n  /**\n   * Callback fired when the page is changed.\n   *\n   * @param {React.ChangeEvent<unknown>} event The event source of the callback.\n   * @param {number} page The page selected.\n   */\n  onChange: PropTypes.func,\n  /**\n   * The current page. Unlike `TablePagination`, which starts numbering from `0`, this pagination starts from `1`.\n   */\n  page: integerPropType,\n  /**\n   * Render the item.\n   * @param {PaginationRenderItemParams} params The props to spread on a PaginationItem.\n   * @returns {ReactNode}\n   * @default (item) => <PaginationItem {...item} />\n   */\n  renderItem: PropTypes.func,\n  /**\n   * The shape of the pagination items.\n   * @default 'circular'\n   */\n  shape: PropTypes.oneOf(['circular', 'rounded']),\n  /**\n   * If `true`, show the first-page button.\n   * @default false\n   */\n  showFirstButton: PropTypes.bool,\n  /**\n   * If `true`, show the last-page button.\n   * @default false\n   */\n  showLastButton: PropTypes.bool,\n  /**\n   * Number of always visible pages before and after the current page.\n   * @default 1\n   */\n  siblingCount: integerPropType,\n  /**\n   * The size of the component.\n   * @default 'medium'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['small', 'medium', 'large']), PropTypes.string]),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The variant to use.\n   * @default 'text'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['outlined', 'text']), PropTypes.string])\n} : void 0;\nexport default Pagination;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}