{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getSnackbarContentUtilityClass(slot) {\n  return generateUtilityClass('MuiSnackbarContent', slot);\n}\nconst snackbarContentClasses = generateUtilityClasses('MuiSnackbarContent', ['root', 'message', 'action']);\nexport default snackbarContentClasses;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}