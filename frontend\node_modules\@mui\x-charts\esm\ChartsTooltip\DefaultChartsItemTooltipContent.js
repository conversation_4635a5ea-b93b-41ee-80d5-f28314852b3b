import * as React from 'react';
import PropTypes from 'prop-types';
import clsx from 'clsx';
import { ChartsTooltipTable, ChartsTooltipCell, ChartsTooltipMark, ChartsTooltipPaper, ChartsTooltipRow } from './ChartsTooltipTable';
import { jsx as _jsx } from "react/jsx-runtime";
import { jsxs as _jsxs } from "react/jsx-runtime";
function DefaultChartsItemTooltipContent(props) {
  const {
    series,
    itemData,
    sx,
    classes
  } = props;
  if (itemData.dataIndex === undefined) {
    return null;
  }
  const {
    displayedLabel,
    color
  } = series.type === 'pie' ? {
    color: series.data[itemData.dataIndex].color,
    displayedLabel: series.data[itemData.dataIndex].label
  } : {
    color: series.color,
    displayedLabel: series.label
  };

  // TODO: Manage to let TS understand series.data and series.valueFormatter are coherent
  // @ts-ignore
  const formattedValue = series.valueFormatter(series.data[itemData.dataIndex]);
  return /*#__PURE__*/_jsx(ChartsTooltipPaper, {
    sx: sx,
    className: classes.root,
    children: /*#__PURE__*/_jsx(ChartsTooltipTable, {
      className: classes.table,
      children: /*#__PURE__*/_jsx("tbody", {
        children: /*#__PURE__*/_jsxs(ChartsTooltipRow, {
          className: classes.row,
          children: [/*#__PURE__*/_jsx(ChartsTooltipCell, {
            className: clsx(classes.markCell, classes.cell),
            children: /*#__PURE__*/_jsx(ChartsTooltipMark, {
              ownerState: {
                color
              },
              className: classes.mark
            })
          }), /*#__PURE__*/_jsx(ChartsTooltipCell, {
            className: clsx(classes.labelCell, classes.cell),
            children: displayedLabel
          }), /*#__PURE__*/_jsx(ChartsTooltipCell, {
            className: clsx(classes.valueCell, classes.cell),
            children: formattedValue
          })]
        })
      })
    })
  });
}
process.env.NODE_ENV !== "production" ? DefaultChartsItemTooltipContent.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * Override or extend the styles applied to the component.
   */
  classes: PropTypes.object.isRequired,
  /**
   * The data used to identify the triggered item.
   */
  itemData: PropTypes.shape({
    dataIndex: PropTypes.number,
    seriesId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,
    type: PropTypes.oneOf(['bar', 'line', 'pie', 'scatter']).isRequired
  }).isRequired,
  /**
   * The series linked to the triggered axis.
   */
  series: PropTypes.shape({
    color: PropTypes.string,
    data: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.number), PropTypes.arrayOf(PropTypes.number), PropTypes.arrayOf(PropTypes.shape({
      id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,
      x: PropTypes.number.isRequired,
      y: PropTypes.number.isRequired
    })), PropTypes.arrayOf(PropTypes.shape({
      color: PropTypes.string.isRequired,
      endAngle: PropTypes.number.isRequired,
      formattedValue: PropTypes.string.isRequired,
      id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,
      index: PropTypes.number.isRequired,
      label: PropTypes.string,
      padAngle: PropTypes.number.isRequired,
      startAngle: PropTypes.number.isRequired,
      value: PropTypes.number.isRequired
    }))]).isRequired,
    highlightScope: PropTypes.shape({
      faded: PropTypes.oneOf(['global', 'none', 'series']),
      highlighted: PropTypes.oneOf(['item', 'none', 'series'])
    }),
    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,
    type: PropTypes.oneOf(['bar', 'line', 'pie', 'scatter']).isRequired,
    valueFormatter: PropTypes.func.isRequired
  }).isRequired,
  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])
} : void 0;
export { DefaultChartsItemTooltipContent };