{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"actions\", \"children\", \"className\", \"defaultValue\", \"name\", \"onChange\", \"value\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport FormGroup from '../FormGroup';\nimport { getRadioGroupUtilityClass } from './radioGroupClasses';\nimport useForkRef from '../utils/useForkRef';\nimport useControlled from '../utils/useControlled';\nimport RadioGroupContext from './RadioGroupContext';\nimport useId from '../utils/useId';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = props => {\n  const {\n    classes,\n    row,\n    error\n  } = props;\n  const slots = {\n    root: ['root', row && 'row', error && 'error']\n  };\n  return composeClasses(slots, getRadioGroupUtilityClass, classes);\n};\nconst RadioGroup = /*#__PURE__*/React.forwardRef(function RadioGroup(props, ref) {\n  const {\n      // private\n      // eslint-disable-next-line react/prop-types\n      actions,\n      children,\n      className,\n      defaultValue,\n      name: nameProp,\n      onChange,\n      value: valueProp\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const rootRef = React.useRef(null);\n  const classes = useUtilityClasses(props);\n  const [value, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue,\n    name: 'RadioGroup'\n  });\n  React.useImperativeHandle(actions, () => ({\n    focus: () => {\n      let input = rootRef.current.querySelector('input:not(:disabled):checked');\n      if (!input) {\n        input = rootRef.current.querySelector('input:not(:disabled)');\n      }\n      if (input) {\n        input.focus();\n      }\n    }\n  }), []);\n  const handleRef = useForkRef(ref, rootRef);\n  const name = useId(nameProp);\n  const contextValue = React.useMemo(() => ({\n    name,\n    onChange(event) {\n      setValueState(event.target.value);\n      if (onChange) {\n        onChange(event, event.target.value);\n      }\n    },\n    value\n  }), [name, onChange, setValueState, value]);\n  return /*#__PURE__*/_jsx(RadioGroupContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(FormGroup, _extends({\n      role: \"radiogroup\",\n      ref: handleRef,\n      className: clsx(classes.root, className)\n    }, other, {\n      children: children\n    }))\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? RadioGroup.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The default value. Use when the component is not controlled.\n   */\n  defaultValue: PropTypes.any,\n  /**\n   * The name used to reference the value of the control.\n   * If you don't provide this prop, it falls back to a randomly generated name.\n   */\n  name: PropTypes.string,\n  /**\n   * Callback fired when a radio button is selected.\n   *\n   * @param {React.ChangeEvent<HTMLInputElement>} event The event source of the callback.\n   * @param {string} value The value of the selected radio button.\n   * You can pull out the new value by accessing `event.target.value` (string).\n   */\n  onChange: PropTypes.func,\n  /**\n   * Value of the selected radio button. The DOM API casts this to a string.\n   */\n  value: PropTypes.any\n} : void 0;\nexport default RadioGroup;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}