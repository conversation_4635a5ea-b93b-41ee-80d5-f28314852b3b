{"ast": null, "code": "// TrustVault - Create Portfolio Page\nimport React,{useState}from'react';import{Box,Typography,Paper,TextField,Button,FormControl,InputLabel,Select,MenuItem,FormControlLabel,Switch,Alert,CircularProgress}from'@mui/material';import{ArrowBack,Save}from'@mui/icons-material';import{Helmet}from'react-helmet-async';import{useNavigate}from'react-router-dom';import{useMutation,useQueryClient}from'react-query';import{toast}from'react-hot-toast';// Services\nimport apiService from'../../services/api';// Types\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const CreatePortfolioPage=()=>{const navigate=useNavigate();const queryClient=useQueryClient();const[formData,setFormData]=useState({name:'',description:'',portfolio_type:'MODERATE',is_public:false});const[errors,setErrors]=useState({});// Move hooks before any conditional returns\nconst createPortfolioMutation=useMutation(data=>apiService.createPortfolio(data),{onSuccess:portfolio=>{toast.success('Portfolio created successfully!');queryClient.invalidateQueries('portfolios');navigate(`/portfolios/${portfolio.id}`);},onError:error=>{var _error$response;console.error('Create portfolio error:',error);if((_error$response=error.response)!==null&&_error$response!==void 0&&_error$response.data){setErrors(error.response.data);}else{toast.error('Failed to create portfolio. Please try again.');}}});const handleInputChange=field=>event=>{const value=event.target.type==='checkbox'?event.target.checked:event.target.value;setFormData(prev=>({...prev,[field]:value}));// Clear error when user starts typing\nif(field in errors){setErrors(prev=>{const newErrors={...prev};delete newErrors[field];return newErrors;});}};const handleSubmit=event=>{event.preventDefault();// Basic validation\nconst newErrors={};if(!formData.name.trim()){newErrors.name='Portfolio name is required';}if(Object.keys(newErrors).length>0){setErrors(newErrors);return;}createPortfolioMutation.mutate(formData);};const portfolioTypes=[{value:'CONSERVATIVE',label:'Conservative',description:'Low risk, stable returns'},{value:'MODERATE',label:'Moderate',description:'Balanced risk and return'},{value:'AGGRESSIVE',label:'Aggressive',description:'High risk, high potential returns'},{value:'CUSTOM',label:'Custom',description:'Custom allocation strategy'}];return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(Helmet,{children:[/*#__PURE__*/_jsx(\"title\",{children:\"Create Portfolio - TrustVault\"}),/*#__PURE__*/_jsx(\"meta\",{name:\"description\",content:\"Create a new investment portfolio\"})]}),/*#__PURE__*/_jsxs(Box,{sx:{maxWidth:800,mx:'auto',p:3},children:[/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",mb:3,children:[/*#__PURE__*/_jsx(Button,{startIcon:/*#__PURE__*/_jsx(ArrowBack,{}),onClick:()=>navigate('/portfolios'),sx:{mr:2},children:\"Back to Portfolios\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",component:\"h1\",children:\"Create New Portfolio\"})]}),/*#__PURE__*/_jsx(Paper,{sx:{p:4},children:/*#__PURE__*/_jsx(\"form\",{onSubmit:handleSubmit,children:/*#__PURE__*/_jsxs(Box,{display:\"flex\",flexDirection:\"column\",gap:3,children:[/*#__PURE__*/_jsx(TextField,{label:\"Portfolio Name\",value:formData.name,onChange:handleInputChange('name'),error:!!errors.name,helperText:errors.name,required:true,fullWidth:true,placeholder:\"e.g., My Investment Portfolio\"}),/*#__PURE__*/_jsx(TextField,{label:\"Description\",value:formData.description,onChange:handleInputChange('description'),error:!!errors.description,helperText:errors.description,multiline:true,rows:3,fullWidth:true,placeholder:\"Describe your investment strategy and goals...\"}),/*#__PURE__*/_jsxs(FormControl,{fullWidth:true,children:[/*#__PURE__*/_jsx(InputLabel,{children:\"Portfolio Type\"}),/*#__PURE__*/_jsx(Select,{value:formData.portfolio_type,onChange:handleInputChange('portfolio_type'),label:\"Portfolio Type\",children:portfolioTypes.map(type=>/*#__PURE__*/_jsx(MenuItem,{value:type.value,children:/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"body1\",children:type.label}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",children:type.description})]})},type.value))})]}),/*#__PURE__*/_jsx(FormControlLabel,{control:/*#__PURE__*/_jsx(Switch,{checked:formData.is_public,onChange:handleInputChange('is_public')}),label:/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"body1\",children:\"Make Portfolio Public\"}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",children:\"Allow others to view your portfolio performance (holdings remain private)\"})]})}),Object.keys(errors).length>0&&/*#__PURE__*/_jsx(Alert,{severity:\"error\",children:\"Please fix the errors above and try again.\"}),/*#__PURE__*/_jsxs(Box,{display:\"flex\",justifyContent:\"flex-end\",gap:2,mt:2,children:[/*#__PURE__*/_jsx(Button,{variant:\"outlined\",onClick:()=>navigate('/portfolios'),disabled:createPortfolioMutation.isLoading,children:\"Cancel\"}),/*#__PURE__*/_jsx(Button,{type:\"submit\",variant:\"contained\",startIcon:createPortfolioMutation.isLoading?/*#__PURE__*/_jsx(CircularProgress,{size:20}):/*#__PURE__*/_jsx(Save,{}),disabled:createPortfolioMutation.isLoading,children:createPortfolioMutation.isLoading?'Creating...':'Create Portfolio'})]})]})})}),/*#__PURE__*/_jsxs(Paper,{sx:{p:3,mt:3,bgcolor:'background.default'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Getting Started\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"After creating your portfolio, you can:\"}),/*#__PURE__*/_jsxs(Box,{component:\"ul\",sx:{mt:1,pl:2},children:[/*#__PURE__*/_jsx(Typography,{component:\"li\",variant:\"body2\",color:\"text.secondary\",children:\"Add assets and track your investments\"}),/*#__PURE__*/_jsx(Typography,{component:\"li\",variant:\"body2\",color:\"text.secondary\",children:\"Record transactions and monitor performance\"}),/*#__PURE__*/_jsx(Typography,{component:\"li\",variant:\"body2\",color:\"text.secondary\",children:\"View detailed analytics and reports\"}),/*#__PURE__*/_jsx(Typography,{component:\"li\",variant:\"body2\",color:\"text.secondary\",children:\"Set up alerts and notifications\"})]})]})]})]});};export default CreatePortfolioPage;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}