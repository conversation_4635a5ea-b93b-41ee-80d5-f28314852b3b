# TrustVault - Security Tests

import pytest
from django.test import override_settings
from django.urls import reverse
from rest_framework import status
from apps.core.models import SecurityEvent, AuditLog
from apps.authentication.models import LoginAttempt


@pytest.mark.django_db
class TestSecurityEvents:
    """Test security event logging and handling."""

    def test_failed_login_creates_security_event(self, api_client, user):
        """Test that failed login attempts create security events."""
        # Attempt login with wrong password multiple times
        for _ in range(3):
            response = api_client.post(
                reverse('authentication:login'),
                data={
                    'email': user.email,
                    'password': 'WrongPassword',
                },
                format='json'
            )
            assert response.status_code == status.HTTP_400_BAD_REQUEST

        # Check that login attempts were logged
        login_attempts = LoginAttempt.objects.filter(
            email_attempted=user.email,
            attempt_type='FAILED_PASSWORD'
        )
        assert login_attempts.count() == 3

        # Check for security events
        security_events = SecurityEvent.objects.filter(
            event_type='MULTIPLE_FAILED_LOGINS'
        )
        assert security_events.exists()

    def test_suspicious_ip_detection(self, api_client, user):
        """Test detection of suspicious IP addresses."""
        # Simulate requests from different IPs
        suspicious_ips = ['*************', '*********', '***********']
        
        for ip in suspicious_ips:
            with override_settings(REMOTE_ADDR=ip):
                api_client.post(
                    reverse('authentication:login'),
                    data={
                        'email': user.email,
                        'password': 'WrongPassword',
                    },
                    format='json',
                    HTTP_X_FORWARDED_FOR=ip
                )

        # Check for suspicious activity detection
        login_attempts = LoginAttempt.objects.filter(
            email_attempted=user.email,
            is_suspicious=True
        )
        assert login_attempts.count() > 0

    def test_rate_limiting(self, api_client, user):
        """Test rate limiting on login attempts."""
        # Make many rapid login attempts
        for i in range(10):
            response = api_client.post(
                reverse('authentication:login'),
                data={
                    'email': user.email,
                    'password': 'WrongPassword',
                },
                format='json'
            )
            
            # After certain number of attempts, should be rate limited
            if i > 5:
                assert response.status_code == status.HTTP_429_TOO_MANY_REQUESTS

    def test_account_lockout(self, api_client, user):
        """Test account lockout after multiple failed attempts."""
        # Make multiple failed login attempts
        for _ in range(6):  # Assuming lockout after 5 attempts
            api_client.post(
                reverse('authentication:login'),
                data={
                    'email': user.email,
                    'password': 'WrongPassword',
                },
                format='json'
            )

        # Refresh user from database
        user.refresh_from_db()
        
        # Account should be locked
        assert user.is_account_locked

        # Even with correct password, login should fail
        response = api_client.post(
            reverse('authentication:login'),
            data={
                'email': user.email,
                'password': 'TestPassword123!',
            },
            format='json'
        )
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST


@pytest.mark.django_db
class TestAuditLogging:
    """Test audit logging functionality."""

    def test_portfolio_creation_audit(self, authenticated_client, portfolio_data):
        """Test that portfolio creation is audited."""
        response = authenticated_client.post(
            reverse('portfolio:portfolio-list'),
            data=portfolio_data,
            format='json'
        )
        
        assert response.status_code == status.HTTP_201_CREATED
        
        # Check audit log
        audit_logs = AuditLog.objects.filter(
            action='CREATE',
            resource_type='Portfolio'
        )
        assert audit_logs.exists()
        
        audit_log = audit_logs.first()
        assert audit_log.details['name'] == portfolio_data['name']

    def test_portfolio_deletion_audit(self, authenticated_client, user, portfolio_data):
        """Test that portfolio deletion is audited with high severity."""
        from apps.portfolio.models import Portfolio
        
        portfolio = Portfolio.objects.create(user=user, **portfolio_data)
        
        response = authenticated_client.delete(
            reverse('portfolio:portfolio-detail', kwargs={'pk': portfolio.id})
        )
        
        assert response.status_code == status.HTTP_204_NO_CONTENT
        
        # Check audit log
        audit_logs = AuditLog.objects.filter(
            action='DELETE',
            resource_type='Portfolio',
            severity='HIGH'
        )
        assert audit_logs.exists()

    def test_sensitive_data_access_audit(self, authenticated_client, user):
        """Test that access to sensitive data is audited."""
        response = authenticated_client.get(reverse('authentication:profile'))
        
        assert response.status_code == status.HTTP_200_OK
        
        # Check audit log for profile access
        audit_logs = AuditLog.objects.filter(
            user=user,
            action='READ',
            resource_type='UserProfile'
        )
        # Note: This would need to be implemented in the view


@pytest.mark.django_db
class TestInputValidation:
    """Test input validation and sanitization."""

    def test_xss_prevention_in_portfolio_name(self, authenticated_client):
        """Test XSS prevention in portfolio name."""
        malicious_data = {
            'name': '<script>alert("XSS")</script>',
            'description': 'Test portfolio',
            'portfolio_type': 'MODERATE',
            'currency': 'USD',
            'is_public': False,
        }
        
        response = authenticated_client.post(
            reverse('portfolio:portfolio-list'),
            data=malicious_data,
            format='json'
        )
        
        # Should either reject the input or sanitize it
        if response.status_code == status.HTTP_201_CREATED:
            # If accepted, should be sanitized
            assert '<script>' not in response.data['name']
        else:
            # Should be rejected with validation error
            assert response.status_code == status.HTTP_400_BAD_REQUEST

    def test_sql_injection_prevention(self, authenticated_client):
        """Test SQL injection prevention."""
        malicious_email = "<EMAIL>'; DROP TABLE users; --"
        
        response = authenticated_client.post(
            reverse('authentication:login'),
            data={
                'email': malicious_email,
                'password': 'TestPassword123!',
            },
            format='json'
        )
        
        # Should handle malicious input gracefully
        assert response.status_code in [status.HTTP_400_BAD_REQUEST, status.HTTP_404_NOT_FOUND]

    def test_large_payload_rejection(self, authenticated_client):
        """Test rejection of excessively large payloads."""
        large_description = 'A' * 10000  # Very long description
        
        portfolio_data = {
            'name': 'Test Portfolio',
            'description': large_description,
            'portfolio_type': 'MODERATE',
            'currency': 'USD',
            'is_public': False,
        }
        
        response = authenticated_client.post(
            reverse('portfolio:portfolio-list'),
            data=portfolio_data,
            format='json'
        )
        
        # Should reject or truncate large input
        assert response.status_code == status.HTTP_400_BAD_REQUEST or \
               len(response.data.get('description', '')) < len(large_description)


@pytest.mark.django_db
class TestAuthenticationSecurity:
    """Test authentication security measures."""

    def test_jwt_token_expiration(self, authenticated_client, user):
        """Test JWT token expiration."""
        # This would require mocking time or using expired tokens
        # Implementation depends on JWT configuration
        pass

    def test_refresh_token_rotation(self, api_client, user):
        """Test refresh token rotation."""
        # Login to get tokens
        response = api_client.post(
            reverse('authentication:login'),
            data={
                'email': user.email,
                'password': 'TestPassword123!',
            },
            format='json'
        )
        
        assert response.status_code == status.HTTP_200_OK
        refresh_token = response.data['refresh_token']
        
        # Use refresh token to get new access token
        response = api_client.post(
            reverse('authentication:token-refresh'),
            data={'refresh': refresh_token},
            format='json'
        )
        
        assert response.status_code == status.HTTP_200_OK
        new_refresh_token = response.data.get('refresh')
        
        # If token rotation is enabled, new refresh token should be different
        if new_refresh_token:
            assert new_refresh_token != refresh_token

    def test_password_strength_validation(self, api_client):
        """Test password strength validation during registration."""
        weak_passwords = [
            'password',
            '123456',
            'qwerty',
            'abc123',
            'Password1',  # Missing special character
            'password123!',  # Missing uppercase
            'PASSWORD123!',  # Missing lowercase
            'Password!',  # Missing number
            'Pass1!',  # Too short
        ]
        
        for weak_password in weak_passwords:
            user_data = {
                'email': f'test{weak_password}@test.com',
                'username': f'test{weak_password}',
                'first_name': 'Test',
                'last_name': 'User',
                'password': weak_password,
                'password_confirm': weak_password,
                'gdpr_consent': True,
            }
            
            response = api_client.post(
                reverse('authentication:register'),
                data=user_data,
                format='json'
            )
            
            # Should reject weak passwords
            assert response.status_code == status.HTTP_400_BAD_REQUEST


@pytest.mark.django_db
class TestDataEncryption:
    """Test data encryption and protection."""

    def test_sensitive_data_encryption(self, user):
        """Test that sensitive data is encrypted in database."""
        # This would test that certain fields are encrypted at rest
        # Implementation depends on encryption setup
        pass

    def test_password_hashing(self, user):
        """Test that passwords are properly hashed."""
        # Password should not be stored in plain text
        assert user.password != 'TestPassword123!'
        
        # Should use strong hashing algorithm
        assert user.password.startswith('pbkdf2_sha256$') or \
               user.password.startswith('argon2$') or \
               user.password.startswith('bcrypt$')

    def test_session_security(self, authenticated_client):
        """Test session security measures."""
        # Test that sessions are properly secured
        # This would check session configuration
        pass


@pytest.mark.django_db
class TestAccessControl:
    """Test access control and authorization."""

    def test_user_can_only_access_own_portfolios(self, authenticated_client, admin_client, user, admin_user):
        """Test that users can only access their own portfolios."""
        from apps.portfolio.models import Portfolio
        
        # Create portfolios for both users
        user_portfolio = Portfolio.objects.create(
            user=user,
            name='User Portfolio',
            portfolio_type='MODERATE',
            currency='USD'
        )
        
        admin_portfolio = Portfolio.objects.create(
            user=admin_user,
            name='Admin Portfolio',
            portfolio_type='AGGRESSIVE',
            currency='USD'
        )
        
        # User should only see their own portfolio
        response = authenticated_client.get(reverse('portfolio:portfolio-list'))
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) == 1
        assert response.data[0]['name'] == 'User Portfolio'
        
        # User should not be able to access admin's portfolio
        response = authenticated_client.get(
            reverse('portfolio:portfolio-detail', kwargs={'pk': admin_portfolio.id})
        )
        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_unauthorized_api_access(self, api_client):
        """Test that unauthorized requests are rejected."""
        protected_endpoints = [
            reverse('portfolio:portfolio-list'),
            reverse('authentication:profile'),
        ]
        
        for endpoint in protected_endpoints:
            response = api_client.get(endpoint)
            assert response.status_code == status.HTTP_401_UNAUTHORIZED
