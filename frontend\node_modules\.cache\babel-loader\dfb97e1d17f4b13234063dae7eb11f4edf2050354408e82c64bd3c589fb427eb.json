{"ast": null, "code": "// TrustVault - Add Holding Page\nimport React,{useState}from'react';import{Box,Typography,Paper,TextField,Button,Autocomplete,Alert,CircularProgress,Grid,InputAdornment}from'@mui/material';import{ArrowBack,Add}from'@mui/icons-material';import{Helmet}from'react-helmet-async';import{useNavigate,useParams}from'react-router-dom';import{useMutation,useQuery,useQueryClient}from'react-query';import{toast}from'react-hot-toast';// Services\nimport apiService from'../../services/api';// Types\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const AddHoldingPage=()=>{const navigate=useNavigate();const{id:portfolioId}=useParams();const queryClient=useQueryClient();const[formData,setFormData]=useState({asset_id:'',quantity:0,purchase_price:0,purchase_date:new Date().toISOString().split('T')[0],notes:''});const[selectedAsset,setSelectedAsset]=useState(null);const[errors,setErrors]=useState({});// Fetch available assets\nconst{data:assets,isLoading:assetsLoading}=useQuery('assets',()=>apiService.getAssets());// Fetch portfolio details\nconst{data:portfolio}=useQuery(['portfolio',portfolioId],()=>apiService.getPortfolio(portfolioId),{enabled:!!portfolioId});const addHoldingMutation=useMutation(data=>{if(!portfolioId)throw new Error('Portfolio ID is required');return apiService.createHolding(portfolioId,data);},{onSuccess:()=>{toast.success('Holding added successfully!');queryClient.invalidateQueries(['portfolio',portfolioId]);queryClient.invalidateQueries(['holdings',portfolioId]);navigate(`/portfolios/${portfolioId}`);},onError:error=>{var _error$response;console.error('Add holding error:',error);if((_error$response=error.response)!==null&&_error$response!==void 0&&_error$response.data){setErrors(error.response.data);}else{toast.error('Failed to add holding. Please try again.');}}});const handleInputChange=field=>event=>{const value=event.target.type==='number'?parseFloat(event.target.value)||0:event.target.value;setFormData(prev=>({...prev,[field]:value}));// Clear error when user starts typing\nif(errors[field]){setErrors(prev=>({...prev,[field]:''}));}};const handleAssetChange=(event,newValue)=>{setSelectedAsset(newValue);setFormData(prev=>({...prev,asset_id:(newValue===null||newValue===void 0?void 0:newValue.id)||''}));if(errors.asset_id){setErrors(prev=>({...prev,asset_id:''}));}};const handleSubmit=event=>{event.preventDefault();// Basic validation\nconst newErrors={};if(!formData.asset_id){newErrors.asset_id='Please select an asset';}if(formData.quantity<=0){newErrors.quantity='Quantity must be greater than 0';}if(formData.purchase_price<=0){newErrors.purchase_price='Purchase price must be greater than 0';}if(!formData.purchase_date){newErrors.purchase_date='Purchase date is required';}if(Object.keys(newErrors).length>0){setErrors(newErrors);return;}addHoldingMutation.mutate(formData);};const calculateTotalValue=()=>{return formData.quantity*formData.purchase_price;};if(!portfolioId){return/*#__PURE__*/_jsx(Box,{sx:{maxWidth:800,mx:'auto',p:3},children:/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"error\",children:\"Portfolio ID is required\"})});}return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(Helmet,{children:[/*#__PURE__*/_jsx(\"title\",{children:\"Add Holding - TrustVault\"}),/*#__PURE__*/_jsx(\"meta\",{name:\"description\",content:\"Add a new holding to your portfolio\"})]}),/*#__PURE__*/_jsxs(Box,{sx:{maxWidth:800,mx:'auto',p:3},children:[/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",mb:3,children:[/*#__PURE__*/_jsx(Button,{startIcon:/*#__PURE__*/_jsx(ArrowBack,{}),onClick:()=>navigate(`/portfolios/${portfolioId}`),sx:{mr:2},children:\"Back to Portfolio\"}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",component:\"h1\",children:\"Add New Holding\"}),portfolio&&/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",color:\"text.secondary\",children:[\"to \",portfolio.name]})]})]}),/*#__PURE__*/_jsx(Paper,{sx:{p:4},children:/*#__PURE__*/_jsx(\"form\",{onSubmit:handleSubmit,children:/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(Autocomplete,{options:assets||[],getOptionLabel:option=>`${option.symbol} - ${option.name}`,value:selectedAsset,onChange:handleAssetChange,loading:assetsLoading,renderInput:params=>/*#__PURE__*/_jsx(TextField,{...params,label:\"Select Asset\",error:!!errors.asset_id,helperText:errors.asset_id,required:true,InputProps:{...params.InputProps,endAdornment:/*#__PURE__*/_jsxs(_Fragment,{children:[assetsLoading?/*#__PURE__*/_jsx(CircularProgress,{color:\"inherit\",size:20}):null,params.InputProps.endAdornment]})}})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsx(TextField,{label:\"Quantity\",type:\"number\",value:formData.quantity||'',onChange:handleInputChange('quantity'),error:!!errors.quantity,helperText:errors.quantity,required:true,fullWidth:true,inputProps:{min:0,step:0.01}})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsx(TextField,{label:\"Purchase Price\",type:\"number\",value:formData.purchase_price||'',onChange:handleInputChange('purchase_price'),error:!!errors.purchase_price,helperText:errors.purchase_price,required:true,fullWidth:true,InputProps:{startAdornment:/*#__PURE__*/_jsx(InputAdornment,{position:\"start\",children:\"$\"})},inputProps:{min:0,step:0.01}})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsx(TextField,{label:\"Purchase Date\",type:\"date\",value:formData.purchase_date,onChange:handleInputChange('purchase_date'),error:!!errors.purchase_date,helperText:errors.purchase_date,required:true,fullWidth:true,InputLabelProps:{shrink:true}})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsx(TextField,{label:\"Total Value\",value:`$${calculateTotalValue().toFixed(2)}`,fullWidth:true,disabled:true,InputProps:{readOnly:true}})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(TextField,{label:\"Notes (Optional)\",value:formData.notes,onChange:handleInputChange('notes'),multiline:true,rows:3,fullWidth:true,placeholder:\"Add any notes about this holding...\"})}),Object.keys(errors).length>0&&/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(Alert,{severity:\"error\",children:\"Please fix the errors above and try again.\"})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Box,{display:\"flex\",justifyContent:\"flex-end\",gap:2,mt:2,children:[/*#__PURE__*/_jsx(Button,{variant:\"outlined\",onClick:()=>navigate(`/portfolios/${portfolioId}`),disabled:addHoldingMutation.isLoading,children:\"Cancel\"}),/*#__PURE__*/_jsx(Button,{type:\"submit\",variant:\"contained\",startIcon:addHoldingMutation.isLoading?/*#__PURE__*/_jsx(CircularProgress,{size:20}):/*#__PURE__*/_jsx(Add,{}),disabled:addHoldingMutation.isLoading,children:addHoldingMutation.isLoading?'Adding...':'Add Holding'})]})})]})})}),/*#__PURE__*/_jsxs(Paper,{sx:{p:3,mt:3,bgcolor:'background.default'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"About Holdings\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Holdings represent your investments in specific assets. Once added, you can:\"}),/*#__PURE__*/_jsxs(Box,{component:\"ul\",sx:{mt:1,pl:2},children:[/*#__PURE__*/_jsx(Typography,{component:\"li\",variant:\"body2\",color:\"text.secondary\",children:\"Track performance and current value\"}),/*#__PURE__*/_jsx(Typography,{component:\"li\",variant:\"body2\",color:\"text.secondary\",children:\"Record additional transactions (buy/sell)\"}),/*#__PURE__*/_jsx(Typography,{component:\"li\",variant:\"body2\",color:\"text.secondary\",children:\"View detailed analytics and charts\"}),/*#__PURE__*/_jsx(Typography,{component:\"li\",variant:\"body2\",color:\"text.secondary\",children:\"Monitor portfolio allocation\"})]})]})]})]});};export default AddHoldingPage;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}