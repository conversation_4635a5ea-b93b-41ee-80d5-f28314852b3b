#!/bin/bash

# TrustVault - Test Runner Script

set -e

echo "🧪 TrustVault Test Suite"
echo "========================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "backend/manage.py" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

# Change to backend directory
cd backend

# Check if virtual environment is activated
if [ -z "$VIRTUAL_ENV" ]; then
    print_warning "Virtual environment not detected. Attempting to activate..."
    if [ -f "../venv/bin/activate" ]; then
        source ../venv/bin/activate
        print_success "Virtual environment activated"
    elif [ -f "../.venv/bin/activate" ]; then
        source ../.venv/bin/activate
        print_success "Virtual environment activated"
    else
        print_error "Virtual environment not found. Please create and activate one."
        exit 1
    fi
fi

# Install test dependencies
print_status "Installing test dependencies..."
pip install -q pytest pytest-django pytest-cov pytest-mock

# Set test environment
export DJANGO_SETTINGS_MODULE=trustvault.settings.test

# Run different test suites based on argument
case "${1:-all}" in
    "unit")
        print_status "Running unit tests..."
        pytest tests/unit/ -v --tb=short
        ;;
    "integration")
        print_status "Running integration tests..."
        pytest tests/integration/ -v --tb=short
        ;;
    "security")
        print_status "Running security tests..."
        pytest tests/security/ -v --tb=short
        ;;
    "coverage")
        print_status "Running tests with coverage report..."
        pytest --cov=apps --cov-report=html --cov-report=term-missing --cov-fail-under=80
        print_success "Coverage report generated in htmlcov/"
        ;;
    "fast")
        print_status "Running fast tests (excluding slow tests)..."
        pytest -m "not slow" -v --tb=short
        ;;
    "all")
        print_status "Running all tests..."
        pytest -v --tb=short
        ;;
    "lint")
        print_status "Running code quality checks..."
        
        # Install linting tools
        pip install -q flake8 black isort mypy
        
        # Check code formatting
        print_status "Checking code formatting with black..."
        black --check --diff .
        
        # Check import sorting
        print_status "Checking import sorting with isort..."
        isort --check-only --diff .
        
        # Run flake8
        print_status "Running flake8 linting..."
        flake8 .
        
        # Run mypy type checking
        print_status "Running mypy type checking..."
        mypy apps/ || print_warning "Type checking completed with warnings"
        
        print_success "Code quality checks completed"
        ;;
    "fix")
        print_status "Fixing code formatting..."
        
        # Install formatting tools
        pip install -q black isort
        
        # Fix code formatting
        black .
        isort .
        
        print_success "Code formatting fixed"
        ;;
    *)
        echo "Usage: $0 [unit|integration|security|coverage|fast|all|lint|fix]"
        echo ""
        echo "Test suites:"
        echo "  unit        - Run unit tests only"
        echo "  integration - Run integration tests only"
        echo "  security    - Run security tests only"
        echo "  coverage    - Run all tests with coverage report"
        echo "  fast        - Run fast tests (exclude slow tests)"
        echo "  all         - Run all tests (default)"
        echo ""
        echo "Code quality:"
        echo "  lint        - Run code quality checks"
        echo "  fix         - Fix code formatting issues"
        exit 1
        ;;
esac

# Check test results
if [ $? -eq 0 ]; then
    print_success "All tests passed! ✅"
else
    print_error "Some tests failed! ❌"
    exit 1
fi
