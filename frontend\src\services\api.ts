// TrustVault - API Service

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { toast } from 'react-hot-toast';

// Types
import {
  AuthResponse,
  LoginCredentials,
  RegisterData,
  User,
  Portfolio,
  Asset,
  Transaction,
  SecurityDashboard,
  ChangePasswordData,
  ApiError,
  PaginatedResponse,
} from '../types';

class ApiService {
  private api: AxiosInstance;
  private baseURL: string;

  constructor() {
    this.baseURL = process.env.REACT_APP_API_URL || '/api/v1';
    
    this.api = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // Request interceptor
    this.api.interceptors.request.use(
      (config) => {
        // Add auth token if available
        const token = this.getAccessToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        // Add security headers
        config.headers['X-Requested-With'] = 'XMLHttpRequest';
        config.headers['X-Client-Version'] = '1.0.0';

        // Log request in development
        if (process.env.NODE_ENV === 'development') {
          console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
        }

        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.api.interceptors.response.use(
      (response: AxiosResponse) => {
        return response;
      },
      async (error) => {
        const originalRequest = error.config;

        // Handle 401 errors (unauthorized)
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            // Try to refresh token
            const refreshToken = this.getRefreshToken();
            if (refreshToken) {
              const response = await this.refreshAccessToken(refreshToken);
              this.setTokens(response.data.access_token, refreshToken);
              
              // Retry original request
              originalRequest.headers.Authorization = `Bearer ${response.data.access_token}`;
              return this.api(originalRequest);
            }
          } catch (refreshError) {
            // Refresh failed, redirect to login
            this.clearTokens();
            window.location.href = '/login';
            return Promise.reject(refreshError);
          }
        }

        // Handle other errors
        this.handleApiError(error);
        return Promise.reject(error);
      }
    );
  }

  private handleApiError(error: any): void {
    const message = error.response?.data?.message || error.message || 'An error occurred';
    
    // Don't show toast for certain errors
    const silentErrors = [401, 403];
    if (!silentErrors.includes(error.response?.status)) {
      toast.error(message);
    }

    // Log error in development
    if (process.env.NODE_ENV === 'development') {
      console.error('API Error:', error.response?.data || error.message);
    }
  }

  // Token management
  private getAccessToken(): string | null {
    return localStorage.getItem('access_token');
  }

  private getRefreshToken(): string | null {
    return localStorage.getItem('refresh_token');
  }

  private setTokens(accessToken: string, refreshToken: string): void {
    localStorage.setItem('access_token', accessToken);
    localStorage.setItem('refresh_token', refreshToken);
  }

  private clearTokens(): void {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
  }

  private async refreshAccessToken(refreshToken: string): Promise<AxiosResponse> {
    return this.api.post('/auth/token/refresh/', {
      refresh: refreshToken,
    });
  }

  // Authentication endpoints
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    const response = await this.api.post<AuthResponse>('/auth/login/', credentials);
    
    if (response.data.access_token) {
      this.setTokens(response.data.access_token, response.data.refresh_token);
    }
    
    return response.data;
  }

  async register(data: RegisterData): Promise<{ message: string; user_id: string }> {
    const response = await this.api.post('/auth/register/', data);
    return response.data;
  }

  async logout(): Promise<void> {
    const refreshToken = this.getRefreshToken();
    
    try {
      await this.api.post('/auth/logout/', {
        refresh_token: refreshToken,
      });
    } finally {
      this.clearTokens();
    }
  }

  async getCurrentUser(): Promise<User> {
    const response = await this.api.get<User>('/auth/profile/');
    return response.data;
  }

  async updateProfile(data: Partial<User>): Promise<User> {
    const response = await this.api.put<User>('/auth/profile/', data);
    return response.data;
  }

  async changePassword(data: ChangePasswordData): Promise<{ message: string }> {
    const response = await this.api.post('/auth/change-password/', data);
    return response.data;
  }

  // Portfolio endpoints
  async getPortfolios(): Promise<Portfolio[]> {
    const response = await this.api.get<Portfolio[]>('/portfolio/');
    return response.data;
  }

  async getPortfolio(id: string): Promise<Portfolio> {
    const response = await this.api.get<Portfolio>(`/portfolio/${id}/`);
    return response.data;
  }

  async createPortfolio(data: Partial<Portfolio>): Promise<Portfolio> {
    const response = await this.api.post<Portfolio>('/portfolio/', data);
    return response.data;
  }

  async updatePortfolio(id: string, data: Partial<Portfolio>): Promise<Portfolio> {
    const response = await this.api.put<Portfolio>(`/portfolio/${id}/`, data);
    return response.data;
  }

  async deletePortfolio(id: string): Promise<void> {
    await this.api.delete(`/portfolio/${id}/`);
  }

  // Asset endpoints
  async getAssets(params?: {
    type?: string;
    search?: string;
    sector?: string;
  }): Promise<Asset[]> {
    const response = await this.api.get<Asset[]>('/portfolio/assets/', { params });
    return response.data;
  }

  // Transaction endpoints
  async getTransactions(portfolioId: string): Promise<Transaction[]> {
    const response = await this.api.get<Transaction[]>(`/portfolio/${portfolioId}/transactions/`);
    return response.data;
  }

  async createTransaction(portfolioId: string, data: Partial<Transaction>): Promise<Transaction> {
    const response = await this.api.post<Transaction>(`/portfolio/${portfolioId}/transactions/`, data);
    return response.data;
  }

  // Security endpoints
  async getSecurityDashboard(): Promise<SecurityDashboard> {
    const response = await this.api.get<SecurityDashboard>('/security/dashboard/');
    return response.data;
  }

  async getSecurityEvents(params?: {
    risk_level?: string;
    event_type?: string;
    is_resolved?: boolean;
    page?: number;
  }): Promise<PaginatedResponse<any>> {
    const response = await this.api.get('/security/events/', { params });
    return response.data;
  }

  async getAuditLogs(params?: {
    action?: string;
    resource_type?: string;
    user_id?: string;
    severity?: string;
    page?: number;
  }): Promise<PaginatedResponse<any>> {
    const response = await this.api.get('/security/audit-logs/', { params });
    return response.data;
  }

  // Health check
  async healthCheck(): Promise<{ status: string }> {
    const response = await this.api.get('/core/status/');
    return response.data;
  }

  // Generic request method
  async request<T>(config: AxiosRequestConfig): Promise<T> {
    const response = await this.api.request<T>(config);
    return response.data;
  }
}

// Create singleton instance
const apiService = new ApiService();

export default apiService;
