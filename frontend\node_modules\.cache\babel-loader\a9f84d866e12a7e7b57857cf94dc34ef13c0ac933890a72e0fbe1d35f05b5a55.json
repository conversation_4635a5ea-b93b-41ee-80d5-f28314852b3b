{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getPopperUtilityClass(slot) {\n  return generateUtilityClass('MuiPopper', slot);\n}\nconst popperClasses = generateUtilityClasses('MuiPopper', ['root']);\nexport default popperClasses;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}