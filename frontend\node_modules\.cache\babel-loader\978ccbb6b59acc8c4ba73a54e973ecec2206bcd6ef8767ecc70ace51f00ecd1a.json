{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\TrustVault\\\\frontend\\\\src\\\\pages\\\\Auth\\\\RegisterPage.tsx\",\n  _s = $RefreshSig$();\n// TrustVault - Register Page\n\nimport React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { Box, Card, CardContent, TextField, Button, Typography, FormControlLabel, Checkbox, Alert, InputAdornment, IconButton, Container, LinearProgress } from '@mui/material';\nimport { Visibility, VisibilityOff, Security, Email, Lock, Person, Phone } from '@mui/icons-material';\nimport { useForm } from 'react-hook-form';\nimport { yupResolver } from '@hookform/resolvers/yup';\nimport * as yup from 'yup';\nimport { toast } from 'react-hot-toast';\nimport { Helmet } from 'react-helmet-async';\n\n// Store\nimport { useAuthStore } from '../../store/authStore';\n\n// Types\n\n// Utils\nimport { validatePasswordStrength } from '../../utils/security';\n\n// Validation schema\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst registerSchema = yup.object({\n  email: yup.string().email('Invalid email format').required('Email is required'),\n  username: yup.string().min(3, 'Username must be at least 3 characters').max(30, 'Username must be less than 30 characters').matches(/^[a-zA-Z0-9_]+$/, 'Username can only contain letters, numbers, and underscores').required('Username is required'),\n  first_name: yup.string().min(2, 'First name must be at least 2 characters').required('First name is required'),\n  last_name: yup.string().min(2, 'Last name must be at least 2 characters').required('Last name is required'),\n  phone_number: yup.string().matches(/^\\+?[1-9]\\d{1,14}$/, 'Invalid phone number format').optional(),\n  password: yup.string().test('password-strength', 'Password does not meet security requirements', value => {\n    if (!value) return false;\n    const {\n      isValid\n    } = validatePasswordStrength(value);\n    return isValid;\n  }).required('Password is required'),\n  password_confirm: yup.string().oneOf([yup.ref('password')], 'Passwords must match').required('Password confirmation is required'),\n  gdpr_consent: yup.boolean().oneOf([true], 'You must accept the privacy policy').required('GDPR consent is required'),\n  marketing_consent: yup.boolean().optional()\n});\nconst RegisterPage = () => {\n  _s();\n  var _errors$first_name, _errors$last_name, _errors$email, _errors$username, _errors$phone_number, _errors$password, _errors$password_conf;\n  const [showPassword, setShowPassword] = useState(false);\n  const [showPasswordConfirm, setShowPasswordConfirm] = useState(false);\n  const [passwordStrength, setPasswordStrength] = useState({\n    score: 0,\n    errors: []\n  });\n  const {\n    register: registerUser,\n    isLoading,\n    error,\n    clearError\n  } = useAuthStore();\n  const navigate = useNavigate();\n  const {\n    register,\n    handleSubmit,\n    watch,\n    formState: {\n      errors\n    }\n  } = useForm({\n    resolver: yupResolver(registerSchema),\n    defaultValues: {\n      email: '',\n      username: '',\n      first_name: '',\n      last_name: '',\n      phone_number: '',\n      password: '',\n      password_confirm: '',\n      gdpr_consent: false,\n      marketing_consent: false\n    }\n  });\n  const watchPassword = watch('password');\n  React.useEffect(() => {\n    if (watchPassword) {\n      const strength = validatePasswordStrength(watchPassword);\n      setPasswordStrength(strength);\n    } else {\n      setPasswordStrength({\n        score: 0,\n        errors: []\n      });\n    }\n  }, [watchPassword]);\n  const onSubmit = async data => {\n    try {\n      clearError();\n      await registerUser(data);\n      toast.success('Registration successful! Please log in.');\n      navigate('/login');\n    } catch (error) {\n      // Error is handled by the store and displayed below\n    }\n  };\n  const getPasswordStrengthColor = score => {\n    if (score <= 1) return 'error';\n    if (score <= 3) return 'warning';\n    return 'success';\n  };\n  const getPasswordStrengthText = score => {\n    if (score <= 1) return 'Weak';\n    if (score <= 3) return 'Medium';\n    return 'Strong';\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"title\", {\n        children: \"Register - TrustVault\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"description\",\n        content: \"Create your secure TrustVault account\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"sm\",\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        flexDirection: \"column\",\n        alignItems: \"center\",\n        justifyContent: \"center\",\n        minHeight: \"100vh\",\n        py: 4,\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          textAlign: \"center\",\n          mb: 4,\n          children: [/*#__PURE__*/_jsxDEV(Security, {\n            sx: {\n              fontSize: 64,\n              color: 'primary.main',\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h3\",\n            component: \"h1\",\n            gutterBottom: true,\n            children: \"TrustVault\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"text.secondary\",\n            children: \"Create Your Secure Account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            width: '100%',\n            maxWidth: 500\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              p: 4\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              component: \"h2\",\n              textAlign: \"center\",\n              mb: 3,\n              children: \"Sign Up\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"error\",\n              sx: {\n                mb: 3\n              },\n              children: error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              component: \"form\",\n              onSubmit: handleSubmit(onSubmit),\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                gap: 2,\n                mb: 2,\n                children: [/*#__PURE__*/_jsxDEV(TextField, {\n                  ...register('first_name'),\n                  fullWidth: true,\n                  label: \"First Name\",\n                  autoComplete: \"given-name\",\n                  error: !!errors.first_name,\n                  helperText: (_errors$first_name = errors.first_name) === null || _errors$first_name === void 0 ? void 0 : _errors$first_name.message,\n                  InputProps: {\n                    startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                      position: \"start\",\n                      children: /*#__PURE__*/_jsxDEV(Person, {\n                        color: \"action\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 208,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 207,\n                      columnNumber: 25\n                    }, this)\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                  ...register('last_name'),\n                  fullWidth: true,\n                  label: \"Last Name\",\n                  autoComplete: \"family-name\",\n                  error: !!errors.last_name,\n                  helperText: (_errors$last_name = errors.last_name) === null || _errors$last_name === void 0 ? void 0 : _errors$last_name.message\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                ...register('email'),\n                fullWidth: true,\n                label: \"Email Address\",\n                type: \"email\",\n                autoComplete: \"email\",\n                margin: \"normal\",\n                error: !!errors.email,\n                helperText: (_errors$email = errors.email) === null || _errors$email === void 0 ? void 0 : _errors$email.message,\n                InputProps: {\n                  startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                    position: \"start\",\n                    children: /*#__PURE__*/_jsxDEV(Email, {\n                      color: \"action\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 236,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 235,\n                    columnNumber: 23\n                  }, this)\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                ...register('username'),\n                fullWidth: true,\n                label: \"Username\",\n                autoComplete: \"username\",\n                margin: \"normal\",\n                error: !!errors.username,\n                helperText: (_errors$username = errors.username) === null || _errors$username === void 0 ? void 0 : _errors$username.message\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                ...register('phone_number'),\n                fullWidth: true,\n                label: \"Phone Number (Optional)\",\n                type: \"tel\",\n                autoComplete: \"tel\",\n                margin: \"normal\",\n                error: !!errors.phone_number,\n                helperText: (_errors$phone_number = errors.phone_number) === null || _errors$phone_number === void 0 ? void 0 : _errors$phone_number.message,\n                InputProps: {\n                  startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                    position: \"start\",\n                    children: /*#__PURE__*/_jsxDEV(Phone, {\n                      color: \"action\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 264,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 263,\n                    columnNumber: 23\n                  }, this)\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                ...register('password'),\n                fullWidth: true,\n                label: \"Password\",\n                type: showPassword ? 'text' : 'password',\n                autoComplete: \"new-password\",\n                margin: \"normal\",\n                error: !!errors.password,\n                helperText: (_errors$password = errors.password) === null || _errors$password === void 0 ? void 0 : _errors$password.message,\n                InputProps: {\n                  startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                    position: \"start\",\n                    children: /*#__PURE__*/_jsxDEV(Lock, {\n                      color: \"action\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 283,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 282,\n                    columnNumber: 23\n                  }, this),\n                  endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                    position: \"end\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      onClick: () => setShowPassword(!showPassword),\n                      edge: \"end\",\n                      children: showPassword ? /*#__PURE__*/_jsxDEV(VisibilityOff, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 292,\n                        columnNumber: 43\n                      }, this) : /*#__PURE__*/_jsxDEV(Visibility, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 292,\n                        columnNumber: 63\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 288,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 287,\n                    columnNumber: 23\n                  }, this)\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 17\n              }, this), watchPassword && /*#__PURE__*/_jsxDEV(Box, {\n                mt: 1,\n                mb: 2,\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  justifyContent: \"space-between\",\n                  alignItems: \"center\",\n                  mb: 1,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: \"Password Strength\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 303,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: `${getPasswordStrengthColor(passwordStrength.score)}.main`,\n                    children: getPasswordStrengthText(passwordStrength.score)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 306,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 302,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {\n                  variant: \"determinate\",\n                  value: passwordStrength.score / 5 * 100,\n                  color: getPasswordStrengthColor(passwordStrength.score),\n                  sx: {\n                    height: 6,\n                    borderRadius: 3\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 21\n                }, this), passwordStrength.errors.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n                  mt: 1,\n                  children: passwordStrength.errors.map((error, index) => /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"error\",\n                    display: \"block\",\n                    children: [\"\\u2022 \", error]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 322,\n                    columnNumber: 27\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                ...register('password_confirm'),\n                fullWidth: true,\n                label: \"Confirm Password\",\n                type: showPasswordConfirm ? 'text' : 'password',\n                autoComplete: \"new-password\",\n                margin: \"normal\",\n                error: !!errors.password_confirm,\n                helperText: (_errors$password_conf = errors.password_confirm) === null || _errors$password_conf === void 0 ? void 0 : _errors$password_conf.message,\n                InputProps: {\n                  startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                    position: \"start\",\n                    children: /*#__PURE__*/_jsxDEV(Lock, {\n                      color: \"action\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 343,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 342,\n                    columnNumber: 23\n                  }, this),\n                  endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                    position: \"end\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      onClick: () => setShowPasswordConfirm(!showPasswordConfirm),\n                      edge: \"end\",\n                      children: showPasswordConfirm ? /*#__PURE__*/_jsxDEV(VisibilityOff, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 352,\n                        columnNumber: 50\n                      }, this) : /*#__PURE__*/_jsxDEV(Visibility, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 352,\n                        columnNumber: 70\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 348,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 347,\n                    columnNumber: 23\n                  }, this)\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n                control: /*#__PURE__*/_jsxDEV(Checkbox, {\n                  ...register('gdpr_consent'),\n                  color: \"primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 362,\n                  columnNumber: 21\n                }, this),\n                label: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [\"I agree to the\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/privacy\",\n                    style: {\n                      color: 'inherit'\n                    },\n                    children: \"Privacy Policy\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 370,\n                    columnNumber: 23\n                  }, this), ' ', \"and\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/terms\",\n                    style: {\n                      color: 'inherit'\n                    },\n                    children: \"Terms of Service\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 374,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 368,\n                  columnNumber: 21\n                }, this),\n                sx: {\n                  mt: 2,\n                  alignItems: 'flex-start'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 17\n              }, this), errors.gdpr_consent && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"error\",\n                children: errors.gdpr_consent.message\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 382,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n                control: /*#__PURE__*/_jsxDEV(Checkbox, {\n                  ...register('marketing_consent'),\n                  color: \"primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 389,\n                  columnNumber: 21\n                }, this),\n                label: \"I would like to receive marketing communications\",\n                sx: {\n                  mt: 1,\n                  mb: 2\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 387,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                type: \"submit\",\n                fullWidth: true,\n                variant: \"contained\",\n                size: \"large\",\n                disabled: isLoading,\n                sx: {\n                  mt: 2,\n                  mb: 2,\n                  py: 1.5\n                },\n                children: isLoading ? 'Creating Account...' : 'Create Account'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                textAlign: \"center\",\n                mt: 2,\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Already have an account?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/login\",\n                    style: {\n                      color: 'inherit',\n                      textDecoration: 'none',\n                      fontWeight: 600\n                    },\n                    children: \"Sign in\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 412,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 410,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(RegisterPage, \"Zl0oNYxB5sK08xLVL9Ob9ajsoJQ=\", false, function () {\n  return [useAuthStore, useNavigate, useForm];\n});\n_c = RegisterPage;\nexport default RegisterPage;\nvar _c;\n$RefreshReg$(_c, \"RegisterPage\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useNavigate", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "TextField", "<PERSON><PERSON>", "Typography", "FormControlLabel", "Checkbox", "<PERSON><PERSON>", "InputAdornment", "IconButton", "Container", "LinearProgress", "Visibility", "VisibilityOff", "Security", "Email", "Lock", "Person", "Phone", "useForm", "yupResolver", "yup", "toast", "<PERSON><PERSON><PERSON>", "useAuthStore", "validatePasswordStrength", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "registerSchema", "object", "email", "string", "required", "username", "min", "max", "matches", "first_name", "last_name", "phone_number", "optional", "password", "test", "value", "<PERSON><PERSON><PERSON><PERSON>", "password_confirm", "oneOf", "ref", "gdpr_consent", "boolean", "marketing_consent", "RegisterPage", "_s", "_errors$first_name", "_errors$last_name", "_errors$email", "_errors$username", "_errors$phone_number", "_errors$password", "_errors$password_conf", "showPassword", "setShowPassword", "showPasswordConfirm", "setShowPasswordConfirm", "passwordStrength", "setPasswordStrength", "score", "errors", "register", "registerUser", "isLoading", "error", "clearError", "navigate", "handleSubmit", "watch", "formState", "resolver", "defaultValues", "watchPassword", "useEffect", "strength", "onSubmit", "data", "success", "getPasswordStrengthColor", "getPasswordStrengthText", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "content", "max<PERSON><PERSON><PERSON>", "display", "flexDirection", "alignItems", "justifyContent", "minHeight", "py", "textAlign", "mb", "sx", "fontSize", "color", "variant", "component", "gutterBottom", "width", "p", "severity", "gap", "fullWidth", "label", "autoComplete", "helperText", "message", "InputProps", "startAdornment", "position", "type", "margin", "endAdornment", "onClick", "edge", "mt", "height", "borderRadius", "length", "map", "index", "control", "to", "style", "size", "disabled", "textDecoration", "fontWeight", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/TrustVault/frontend/src/pages/Auth/RegisterPage.tsx"], "sourcesContent": ["// TrustVault - Register Page\n\nimport React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport {\n  Box,\n  Card,\n  CardContent,\n  TextField,\n  Button,\n  Typography,\n  FormControlLabel,\n  Checkbox,\n  Alert,\n  InputAdornment,\n  IconButton,\n  Container,\n  LinearProgress,\n} from '@mui/material';\nimport {\n  Visibility,\n  VisibilityOff,\n  Security,\n  Email,\n  Lock,\n  Person,\n  Phone,\n} from '@mui/icons-material';\nimport { useForm } from 'react-hook-form';\nimport { yupResolver } from '@hookform/resolvers/yup';\nimport * as yup from 'yup';\nimport { toast } from 'react-hot-toast';\nimport { Helmet } from 'react-helmet-async';\n\n// Store\nimport { useAuthStore } from '../../store/authStore';\n\n// Types\nimport { RegisterData } from '../../types';\n\n// Utils\nimport { validatePasswordStrength } from '../../utils/security';\n\n// Validation schema\nconst registerSchema = yup.object({\n  email: yup\n    .string()\n    .email('Invalid email format')\n    .required('Email is required'),\n  username: yup\n    .string()\n    .min(3, 'Username must be at least 3 characters')\n    .max(30, 'Username must be less than 30 characters')\n    .matches(/^[a-zA-Z0-9_]+$/, 'Username can only contain letters, numbers, and underscores')\n    .required('Username is required'),\n  first_name: yup\n    .string()\n    .min(2, 'First name must be at least 2 characters')\n    .required('First name is required'),\n  last_name: yup\n    .string()\n    .min(2, 'Last name must be at least 2 characters')\n    .required('Last name is required'),\n  phone_number: yup\n    .string()\n    .matches(/^\\+?[1-9]\\d{1,14}$/, 'Invalid phone number format')\n    .optional(),\n  password: yup\n    .string()\n    .test('password-strength', 'Password does not meet security requirements', (value) => {\n      if (!value) return false;\n      const { isValid } = validatePasswordStrength(value);\n      return isValid;\n    })\n    .required('Password is required'),\n  password_confirm: yup\n    .string()\n    .oneOf([yup.ref('password')], 'Passwords must match')\n    .required('Password confirmation is required'),\n  gdpr_consent: yup\n    .boolean()\n    .oneOf([true], 'You must accept the privacy policy')\n    .required('GDPR consent is required'),\n  marketing_consent: yup.boolean().optional(),\n});\n\nconst RegisterPage: React.FC = () => {\n  const [showPassword, setShowPassword] = useState(false);\n  const [showPasswordConfirm, setShowPasswordConfirm] = useState(false);\n  const [passwordStrength, setPasswordStrength] = useState({ score: 0, errors: [] as string[] });\n  \n  const { register: registerUser, isLoading, error, clearError } = useAuthStore();\n  const navigate = useNavigate();\n\n  const {\n    register,\n    handleSubmit,\n    watch,\n    formState: { errors },\n  } = useForm<RegisterData>({\n    resolver: yupResolver(registerSchema),\n    defaultValues: {\n      email: '',\n      username: '',\n      first_name: '',\n      last_name: '',\n      phone_number: '',\n      password: '',\n      password_confirm: '',\n      gdpr_consent: false,\n      marketing_consent: false,\n    },\n  });\n\n  const watchPassword = watch('password');\n\n  React.useEffect(() => {\n    if (watchPassword) {\n      const strength = validatePasswordStrength(watchPassword);\n      setPasswordStrength(strength);\n    } else {\n      setPasswordStrength({ score: 0, errors: [] });\n    }\n  }, [watchPassword]);\n\n  const onSubmit = async (data: RegisterData) => {\n    try {\n      clearError();\n      await registerUser(data);\n      toast.success('Registration successful! Please log in.');\n      navigate('/login');\n    } catch (error: any) {\n      // Error is handled by the store and displayed below\n    }\n  };\n\n  const getPasswordStrengthColor = (score: number) => {\n    if (score <= 1) return 'error';\n    if (score <= 3) return 'warning';\n    return 'success';\n  };\n\n  const getPasswordStrengthText = (score: number) => {\n    if (score <= 1) return 'Weak';\n    if (score <= 3) return 'Medium';\n    return 'Strong';\n  };\n\n  return (\n    <>\n      <Helmet>\n        <title>Register - TrustVault</title>\n        <meta name=\"description\" content=\"Create your secure TrustVault account\" />\n      </Helmet>\n\n      <Container maxWidth=\"sm\">\n        <Box\n          display=\"flex\"\n          flexDirection=\"column\"\n          alignItems=\"center\"\n          justifyContent=\"center\"\n          minHeight=\"100vh\"\n          py={4}\n        >\n          {/* Logo and Title */}\n          <Box textAlign=\"center\" mb={4}>\n            <Security\n              sx={{\n                fontSize: 64,\n                color: 'primary.main',\n                mb: 2,\n              }}\n            />\n            <Typography variant=\"h3\" component=\"h1\" gutterBottom>\n              TrustVault\n            </Typography>\n            <Typography variant=\"h6\" color=\"text.secondary\">\n              Create Your Secure Account\n            </Typography>\n          </Box>\n\n          {/* Registration Form */}\n          <Card sx={{ width: '100%', maxWidth: 500 }}>\n            <CardContent sx={{ p: 4 }}>\n              <Typography variant=\"h4\" component=\"h2\" textAlign=\"center\" mb={3}>\n                Sign Up\n              </Typography>\n\n              {error && (\n                <Alert severity=\"error\" sx={{ mb: 3 }}>\n                  {error}\n                </Alert>\n              )}\n\n              <Box component=\"form\" onSubmit={handleSubmit(onSubmit)}>\n                {/* Personal Information */}\n                <Box display=\"flex\" gap={2} mb={2}>\n                  <TextField\n                    {...register('first_name')}\n                    fullWidth\n                    label=\"First Name\"\n                    autoComplete=\"given-name\"\n                    error={!!errors.first_name}\n                    helperText={errors.first_name?.message}\n                    InputProps={{\n                      startAdornment: (\n                        <InputAdornment position=\"start\">\n                          <Person color=\"action\" />\n                        </InputAdornment>\n                      ),\n                    }}\n                  />\n\n                  <TextField\n                    {...register('last_name')}\n                    fullWidth\n                    label=\"Last Name\"\n                    autoComplete=\"family-name\"\n                    error={!!errors.last_name}\n                    helperText={errors.last_name?.message}\n                  />\n                </Box>\n\n                <TextField\n                  {...register('email')}\n                  fullWidth\n                  label=\"Email Address\"\n                  type=\"email\"\n                  autoComplete=\"email\"\n                  margin=\"normal\"\n                  error={!!errors.email}\n                  helperText={errors.email?.message}\n                  InputProps={{\n                    startAdornment: (\n                      <InputAdornment position=\"start\">\n                        <Email color=\"action\" />\n                      </InputAdornment>\n                    ),\n                  }}\n                />\n\n                <TextField\n                  {...register('username')}\n                  fullWidth\n                  label=\"Username\"\n                  autoComplete=\"username\"\n                  margin=\"normal\"\n                  error={!!errors.username}\n                  helperText={errors.username?.message}\n                />\n\n                <TextField\n                  {...register('phone_number')}\n                  fullWidth\n                  label=\"Phone Number (Optional)\"\n                  type=\"tel\"\n                  autoComplete=\"tel\"\n                  margin=\"normal\"\n                  error={!!errors.phone_number}\n                  helperText={errors.phone_number?.message}\n                  InputProps={{\n                    startAdornment: (\n                      <InputAdornment position=\"start\">\n                        <Phone color=\"action\" />\n                      </InputAdornment>\n                    ),\n                  }}\n                />\n\n                {/* Password Fields */}\n                <TextField\n                  {...register('password')}\n                  fullWidth\n                  label=\"Password\"\n                  type={showPassword ? 'text' : 'password'}\n                  autoComplete=\"new-password\"\n                  margin=\"normal\"\n                  error={!!errors.password}\n                  helperText={errors.password?.message}\n                  InputProps={{\n                    startAdornment: (\n                      <InputAdornment position=\"start\">\n                        <Lock color=\"action\" />\n                      </InputAdornment>\n                    ),\n                    endAdornment: (\n                      <InputAdornment position=\"end\">\n                        <IconButton\n                          onClick={() => setShowPassword(!showPassword)}\n                          edge=\"end\"\n                        >\n                          {showPassword ? <VisibilityOff /> : <Visibility />}\n                        </IconButton>\n                      </InputAdornment>\n                    ),\n                  }}\n                />\n\n                {/* Password Strength Indicator */}\n                {watchPassword && (\n                  <Box mt={1} mb={2}>\n                    <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={1}>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        Password Strength\n                      </Typography>\n                      <Typography\n                        variant=\"body2\"\n                        color={`${getPasswordStrengthColor(passwordStrength.score)}.main`}\n                      >\n                        {getPasswordStrengthText(passwordStrength.score)}\n                      </Typography>\n                    </Box>\n                    <LinearProgress\n                      variant=\"determinate\"\n                      value={(passwordStrength.score / 5) * 100}\n                      color={getPasswordStrengthColor(passwordStrength.score)}\n                      sx={{ height: 6, borderRadius: 3 }}\n                    />\n                    {passwordStrength.errors.length > 0 && (\n                      <Box mt={1}>\n                        {passwordStrength.errors.map((error, index) => (\n                          <Typography key={index} variant=\"caption\" color=\"error\" display=\"block\">\n                            • {error}\n                          </Typography>\n                        ))}\n                      </Box>\n                    )}\n                  </Box>\n                )}\n\n                <TextField\n                  {...register('password_confirm')}\n                  fullWidth\n                  label=\"Confirm Password\"\n                  type={showPasswordConfirm ? 'text' : 'password'}\n                  autoComplete=\"new-password\"\n                  margin=\"normal\"\n                  error={!!errors.password_confirm}\n                  helperText={errors.password_confirm?.message}\n                  InputProps={{\n                    startAdornment: (\n                      <InputAdornment position=\"start\">\n                        <Lock color=\"action\" />\n                      </InputAdornment>\n                    ),\n                    endAdornment: (\n                      <InputAdornment position=\"end\">\n                        <IconButton\n                          onClick={() => setShowPasswordConfirm(!showPasswordConfirm)}\n                          edge=\"end\"\n                        >\n                          {showPasswordConfirm ? <VisibilityOff /> : <Visibility />}\n                        </IconButton>\n                      </InputAdornment>\n                    ),\n                  }}\n                />\n\n                {/* Consent Checkboxes */}\n                <FormControlLabel\n                  control={\n                    <Checkbox\n                      {...register('gdpr_consent')}\n                      color=\"primary\"\n                    />\n                  }\n                  label={\n                    <Typography variant=\"body2\">\n                      I agree to the{' '}\n                      <Link to=\"/privacy\" style={{ color: 'inherit' }}>\n                        Privacy Policy\n                      </Link>{' '}\n                      and{' '}\n                      <Link to=\"/terms\" style={{ color: 'inherit' }}>\n                        Terms of Service\n                      </Link>\n                    </Typography>\n                  }\n                  sx={{ mt: 2, alignItems: 'flex-start' }}\n                />\n                {errors.gdpr_consent && (\n                  <Typography variant=\"caption\" color=\"error\">\n                    {errors.gdpr_consent.message}\n                  </Typography>\n                )}\n\n                <FormControlLabel\n                  control={\n                    <Checkbox\n                      {...register('marketing_consent')}\n                      color=\"primary\"\n                    />\n                  }\n                  label=\"I would like to receive marketing communications\"\n                  sx={{ mt: 1, mb: 2 }}\n                />\n\n                <Button\n                  type=\"submit\"\n                  fullWidth\n                  variant=\"contained\"\n                  size=\"large\"\n                  disabled={isLoading}\n                  sx={{ mt: 2, mb: 2, py: 1.5 }}\n                >\n                  {isLoading ? 'Creating Account...' : 'Create Account'}\n                </Button>\n\n                <Box textAlign=\"center\" mt={2}>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Already have an account?{' '}\n                    <Link\n                      to=\"/login\"\n                      style={{\n                        color: 'inherit',\n                        textDecoration: 'none',\n                        fontWeight: 600,\n                      }}\n                    >\n                      Sign in\n                    </Link>\n                  </Typography>\n                </Box>\n              </Box>\n            </CardContent>\n          </Card>\n        </Box>\n      </Container>\n    </>\n  );\n};\n\nexport default RegisterPage;\n"], "mappings": ";;AAAA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,SAAS,EACTC,MAAM,EACNC,UAAU,EACVC,gBAAgB,EAChBC,QAAQ,EACRC,KAAK,EACLC,cAAc,EACdC,UAAU,EACVC,SAAS,EACTC,cAAc,QACT,eAAe;AACtB,SACEC,UAAU,EACVC,aAAa,EACbC,QAAQ,EACRC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,KAAK,QACA,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,WAAW,QAAQ,yBAAyB;AACrD,OAAO,KAAKC,GAAG,MAAM,KAAK;AAC1B,SAASC,KAAK,QAAQ,iBAAiB;AACvC,SAASC,MAAM,QAAQ,oBAAoB;;AAE3C;AACA,SAASC,YAAY,QAAQ,uBAAuB;;AAEpD;;AAGA;AACA,SAASC,wBAAwB,QAAQ,sBAAsB;;AAE/D;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,cAAc,GAAGT,GAAG,CAACU,MAAM,CAAC;EAChCC,KAAK,EAAEX,GAAG,CACPY,MAAM,CAAC,CAAC,CACRD,KAAK,CAAC,sBAAsB,CAAC,CAC7BE,QAAQ,CAAC,mBAAmB,CAAC;EAChCC,QAAQ,EAAEd,GAAG,CACVY,MAAM,CAAC,CAAC,CACRG,GAAG,CAAC,CAAC,EAAE,wCAAwC,CAAC,CAChDC,GAAG,CAAC,EAAE,EAAE,0CAA0C,CAAC,CACnDC,OAAO,CAAC,iBAAiB,EAAE,6DAA6D,CAAC,CACzFJ,QAAQ,CAAC,sBAAsB,CAAC;EACnCK,UAAU,EAAElB,GAAG,CACZY,MAAM,CAAC,CAAC,CACRG,GAAG,CAAC,CAAC,EAAE,0CAA0C,CAAC,CAClDF,QAAQ,CAAC,wBAAwB,CAAC;EACrCM,SAAS,EAAEnB,GAAG,CACXY,MAAM,CAAC,CAAC,CACRG,GAAG,CAAC,CAAC,EAAE,yCAAyC,CAAC,CACjDF,QAAQ,CAAC,uBAAuB,CAAC;EACpCO,YAAY,EAAEpB,GAAG,CACdY,MAAM,CAAC,CAAC,CACRK,OAAO,CAAC,oBAAoB,EAAE,6BAA6B,CAAC,CAC5DI,QAAQ,CAAC,CAAC;EACbC,QAAQ,EAAEtB,GAAG,CACVY,MAAM,CAAC,CAAC,CACRW,IAAI,CAAC,mBAAmB,EAAE,8CAA8C,EAAGC,KAAK,IAAK;IACpF,IAAI,CAACA,KAAK,EAAE,OAAO,KAAK;IACxB,MAAM;MAAEC;IAAQ,CAAC,GAAGrB,wBAAwB,CAACoB,KAAK,CAAC;IACnD,OAAOC,OAAO;EAChB,CAAC,CAAC,CACDZ,QAAQ,CAAC,sBAAsB,CAAC;EACnCa,gBAAgB,EAAE1B,GAAG,CAClBY,MAAM,CAAC,CAAC,CACRe,KAAK,CAAC,CAAC3B,GAAG,CAAC4B,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,sBAAsB,CAAC,CACpDf,QAAQ,CAAC,mCAAmC,CAAC;EAChDgB,YAAY,EAAE7B,GAAG,CACd8B,OAAO,CAAC,CAAC,CACTH,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE,oCAAoC,CAAC,CACnDd,QAAQ,CAAC,0BAA0B,CAAC;EACvCkB,iBAAiB,EAAE/B,GAAG,CAAC8B,OAAO,CAAC,CAAC,CAACT,QAAQ,CAAC;AAC5C,CAAC,CAAC;AAEF,MAAMW,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,kBAAA,EAAAC,iBAAA,EAAAC,aAAA,EAAAC,gBAAA,EAAAC,oBAAA,EAAAC,gBAAA,EAAAC,qBAAA;EACnC,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoE,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACsE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvE,QAAQ,CAAC;IAAEwE,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAe,CAAC,CAAC;EAE9F,MAAM;IAAEC,QAAQ,EAAEC,YAAY;IAAEC,SAAS;IAAEC,KAAK;IAAEC;EAAW,CAAC,GAAGlD,YAAY,CAAC,CAAC;EAC/E,MAAMmD,QAAQ,GAAG7E,WAAW,CAAC,CAAC;EAE9B,MAAM;IACJwE,QAAQ;IACRM,YAAY;IACZC,KAAK;IACLC,SAAS,EAAE;MAAET;IAAO;EACtB,CAAC,GAAGlD,OAAO,CAAe;IACxB4D,QAAQ,EAAE3D,WAAW,CAACU,cAAc,CAAC;IACrCkD,aAAa,EAAE;MACbhD,KAAK,EAAE,EAAE;MACTG,QAAQ,EAAE,EAAE;MACZI,UAAU,EAAE,EAAE;MACdC,SAAS,EAAE,EAAE;MACbC,YAAY,EAAE,EAAE;MAChBE,QAAQ,EAAE,EAAE;MACZI,gBAAgB,EAAE,EAAE;MACpBG,YAAY,EAAE,KAAK;MACnBE,iBAAiB,EAAE;IACrB;EACF,CAAC,CAAC;EAEF,MAAM6B,aAAa,GAAGJ,KAAK,CAAC,UAAU,CAAC;EAEvClF,KAAK,CAACuF,SAAS,CAAC,MAAM;IACpB,IAAID,aAAa,EAAE;MACjB,MAAME,QAAQ,GAAG1D,wBAAwB,CAACwD,aAAa,CAAC;MACxDd,mBAAmB,CAACgB,QAAQ,CAAC;IAC/B,CAAC,MAAM;MACLhB,mBAAmB,CAAC;QAAEC,KAAK,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAG,CAAC,CAAC;IAC/C;EACF,CAAC,EAAE,CAACY,aAAa,CAAC,CAAC;EAEnB,MAAMG,QAAQ,GAAG,MAAOC,IAAkB,IAAK;IAC7C,IAAI;MACFX,UAAU,CAAC,CAAC;MACZ,MAAMH,YAAY,CAACc,IAAI,CAAC;MACxB/D,KAAK,CAACgE,OAAO,CAAC,yCAAyC,CAAC;MACxDX,QAAQ,CAAC,QAAQ,CAAC;IACpB,CAAC,CAAC,OAAOF,KAAU,EAAE;MACnB;IAAA;EAEJ,CAAC;EAED,MAAMc,wBAAwB,GAAInB,KAAa,IAAK;IAClD,IAAIA,KAAK,IAAI,CAAC,EAAE,OAAO,OAAO;IAC9B,IAAIA,KAAK,IAAI,CAAC,EAAE,OAAO,SAAS;IAChC,OAAO,SAAS;EAClB,CAAC;EAED,MAAMoB,uBAAuB,GAAIpB,KAAa,IAAK;IACjD,IAAIA,KAAK,IAAI,CAAC,EAAE,OAAO,MAAM;IAC7B,IAAIA,KAAK,IAAI,CAAC,EAAE,OAAO,QAAQ;IAC/B,OAAO,QAAQ;EACjB,CAAC;EAED,oBACEzC,OAAA,CAAAE,SAAA;IAAA4D,QAAA,gBACE9D,OAAA,CAACJ,MAAM;MAAAkE,QAAA,gBACL9D,OAAA;QAAA8D,QAAA,EAAO;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACpClE,OAAA;QAAMmE,IAAI,EAAC,aAAa;QAACC,OAAO,EAAC;MAAuC;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrE,CAAC,eAETlE,OAAA,CAACjB,SAAS;MAACsF,QAAQ,EAAC,IAAI;MAAAP,QAAA,eACtB9D,OAAA,CAAC5B,GAAG;QACFkG,OAAO,EAAC,MAAM;QACdC,aAAa,EAAC,QAAQ;QACtBC,UAAU,EAAC,QAAQ;QACnBC,cAAc,EAAC,QAAQ;QACvBC,SAAS,EAAC,OAAO;QACjBC,EAAE,EAAE,CAAE;QAAAb,QAAA,gBAGN9D,OAAA,CAAC5B,GAAG;UAACwG,SAAS,EAAC,QAAQ;UAACC,EAAE,EAAE,CAAE;UAAAf,QAAA,gBAC5B9D,OAAA,CAACb,QAAQ;YACP2F,EAAE,EAAE;cACFC,QAAQ,EAAE,EAAE;cACZC,KAAK,EAAE,cAAc;cACrBH,EAAE,EAAE;YACN;UAAE;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACFlE,OAAA,CAACvB,UAAU;YAACwG,OAAO,EAAC,IAAI;YAACC,SAAS,EAAC,IAAI;YAACC,YAAY;YAAArB,QAAA,EAAC;UAErD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACblE,OAAA,CAACvB,UAAU;YAACwG,OAAO,EAAC,IAAI;YAACD,KAAK,EAAC,gBAAgB;YAAAlB,QAAA,EAAC;UAEhD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGNlE,OAAA,CAAC3B,IAAI;UAACyG,EAAE,EAAE;YAAEM,KAAK,EAAE,MAAM;YAAEf,QAAQ,EAAE;UAAI,CAAE;UAAAP,QAAA,eACzC9D,OAAA,CAAC1B,WAAW;YAACwG,EAAE,EAAE;cAAEO,CAAC,EAAE;YAAE,CAAE;YAAAvB,QAAA,gBACxB9D,OAAA,CAACvB,UAAU;cAACwG,OAAO,EAAC,IAAI;cAACC,SAAS,EAAC,IAAI;cAACN,SAAS,EAAC,QAAQ;cAACC,EAAE,EAAE,CAAE;cAAAf,QAAA,EAAC;YAElE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EAEZpB,KAAK,iBACJ9C,OAAA,CAACpB,KAAK;cAAC0G,QAAQ,EAAC,OAAO;cAACR,EAAE,EAAE;gBAAED,EAAE,EAAE;cAAE,CAAE;cAAAf,QAAA,EACnChB;YAAK;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CACR,eAEDlE,OAAA,CAAC5B,GAAG;cAAC8G,SAAS,EAAC,MAAM;cAACzB,QAAQ,EAAER,YAAY,CAACQ,QAAQ,CAAE;cAAAK,QAAA,gBAErD9D,OAAA,CAAC5B,GAAG;gBAACkG,OAAO,EAAC,MAAM;gBAACiB,GAAG,EAAE,CAAE;gBAACV,EAAE,EAAE,CAAE;gBAAAf,QAAA,gBAChC9D,OAAA,CAACzB,SAAS;kBAAA,GACJoE,QAAQ,CAAC,YAAY,CAAC;kBAC1B6C,SAAS;kBACTC,KAAK,EAAC,YAAY;kBAClBC,YAAY,EAAC,YAAY;kBACzB5C,KAAK,EAAE,CAAC,CAACJ,MAAM,CAAC9B,UAAW;kBAC3B+E,UAAU,GAAA/D,kBAAA,GAAEc,MAAM,CAAC9B,UAAU,cAAAgB,kBAAA,uBAAjBA,kBAAA,CAAmBgE,OAAQ;kBACvCC,UAAU,EAAE;oBACVC,cAAc,eACZ9F,OAAA,CAACnB,cAAc;sBAACkH,QAAQ,EAAC,OAAO;sBAAAjC,QAAA,eAC9B9D,OAAA,CAACV,MAAM;wBAAC0F,KAAK,EAAC;sBAAQ;wBAAAjB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX;kBAEpB;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEFlE,OAAA,CAACzB,SAAS;kBAAA,GACJoE,QAAQ,CAAC,WAAW,CAAC;kBACzB6C,SAAS;kBACTC,KAAK,EAAC,WAAW;kBACjBC,YAAY,EAAC,aAAa;kBAC1B5C,KAAK,EAAE,CAAC,CAACJ,MAAM,CAAC7B,SAAU;kBAC1B8E,UAAU,GAAA9D,iBAAA,GAAEa,MAAM,CAAC7B,SAAS,cAAAgB,iBAAA,uBAAhBA,iBAAA,CAAkB+D;gBAAQ;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENlE,OAAA,CAACzB,SAAS;gBAAA,GACJoE,QAAQ,CAAC,OAAO,CAAC;gBACrB6C,SAAS;gBACTC,KAAK,EAAC,eAAe;gBACrBO,IAAI,EAAC,OAAO;gBACZN,YAAY,EAAC,OAAO;gBACpBO,MAAM,EAAC,QAAQ;gBACfnD,KAAK,EAAE,CAAC,CAACJ,MAAM,CAACrC,KAAM;gBACtBsF,UAAU,GAAA7D,aAAA,GAAEY,MAAM,CAACrC,KAAK,cAAAyB,aAAA,uBAAZA,aAAA,CAAc8D,OAAQ;gBAClCC,UAAU,EAAE;kBACVC,cAAc,eACZ9F,OAAA,CAACnB,cAAc;oBAACkH,QAAQ,EAAC,OAAO;oBAAAjC,QAAA,eAC9B9D,OAAA,CAACZ,KAAK;sBAAC4F,KAAK,EAAC;oBAAQ;sBAAAjB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAEpB;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEFlE,OAAA,CAACzB,SAAS;gBAAA,GACJoE,QAAQ,CAAC,UAAU,CAAC;gBACxB6C,SAAS;gBACTC,KAAK,EAAC,UAAU;gBAChBC,YAAY,EAAC,UAAU;gBACvBO,MAAM,EAAC,QAAQ;gBACfnD,KAAK,EAAE,CAAC,CAACJ,MAAM,CAAClC,QAAS;gBACzBmF,UAAU,GAAA5D,gBAAA,GAAEW,MAAM,CAAClC,QAAQ,cAAAuB,gBAAA,uBAAfA,gBAAA,CAAiB6D;cAAQ;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eAEFlE,OAAA,CAACzB,SAAS;gBAAA,GACJoE,QAAQ,CAAC,cAAc,CAAC;gBAC5B6C,SAAS;gBACTC,KAAK,EAAC,yBAAyB;gBAC/BO,IAAI,EAAC,KAAK;gBACVN,YAAY,EAAC,KAAK;gBAClBO,MAAM,EAAC,QAAQ;gBACfnD,KAAK,EAAE,CAAC,CAACJ,MAAM,CAAC5B,YAAa;gBAC7B6E,UAAU,GAAA3D,oBAAA,GAAEU,MAAM,CAAC5B,YAAY,cAAAkB,oBAAA,uBAAnBA,oBAAA,CAAqB4D,OAAQ;gBACzCC,UAAU,EAAE;kBACVC,cAAc,eACZ9F,OAAA,CAACnB,cAAc;oBAACkH,QAAQ,EAAC,OAAO;oBAAAjC,QAAA,eAC9B9D,OAAA,CAACT,KAAK;sBAACyF,KAAK,EAAC;oBAAQ;sBAAAjB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAEpB;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGFlE,OAAA,CAACzB,SAAS;gBAAA,GACJoE,QAAQ,CAAC,UAAU,CAAC;gBACxB6C,SAAS;gBACTC,KAAK,EAAC,UAAU;gBAChBO,IAAI,EAAE7D,YAAY,GAAG,MAAM,GAAG,UAAW;gBACzCuD,YAAY,EAAC,cAAc;gBAC3BO,MAAM,EAAC,QAAQ;gBACfnD,KAAK,EAAE,CAAC,CAACJ,MAAM,CAAC1B,QAAS;gBACzB2E,UAAU,GAAA1D,gBAAA,GAAES,MAAM,CAAC1B,QAAQ,cAAAiB,gBAAA,uBAAfA,gBAAA,CAAiB2D,OAAQ;gBACrCC,UAAU,EAAE;kBACVC,cAAc,eACZ9F,OAAA,CAACnB,cAAc;oBAACkH,QAAQ,EAAC,OAAO;oBAAAjC,QAAA,eAC9B9D,OAAA,CAACX,IAAI;sBAAC2F,KAAK,EAAC;oBAAQ;sBAAAjB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CACjB;kBACDgC,YAAY,eACVlG,OAAA,CAACnB,cAAc;oBAACkH,QAAQ,EAAC,KAAK;oBAAAjC,QAAA,eAC5B9D,OAAA,CAAClB,UAAU;sBACTqH,OAAO,EAAEA,CAAA,KAAM/D,eAAe,CAAC,CAACD,YAAY,CAAE;sBAC9CiE,IAAI,EAAC,KAAK;sBAAAtC,QAAA,EAET3B,YAAY,gBAAGnC,OAAA,CAACd,aAAa;wBAAA6E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,gBAAGlE,OAAA,CAACf,UAAU;wBAAA8E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAEpB;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGDZ,aAAa,iBACZtD,OAAA,CAAC5B,GAAG;gBAACiI,EAAE,EAAE,CAAE;gBAACxB,EAAE,EAAE,CAAE;gBAAAf,QAAA,gBAChB9D,OAAA,CAAC5B,GAAG;kBAACkG,OAAO,EAAC,MAAM;kBAACG,cAAc,EAAC,eAAe;kBAACD,UAAU,EAAC,QAAQ;kBAACK,EAAE,EAAE,CAAE;kBAAAf,QAAA,gBAC3E9D,OAAA,CAACvB,UAAU;oBAACwG,OAAO,EAAC,OAAO;oBAACD,KAAK,EAAC,gBAAgB;oBAAAlB,QAAA,EAAC;kBAEnD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACblE,OAAA,CAACvB,UAAU;oBACTwG,OAAO,EAAC,OAAO;oBACfD,KAAK,EAAE,GAAGpB,wBAAwB,CAACrB,gBAAgB,CAACE,KAAK,CAAC,OAAQ;oBAAAqB,QAAA,EAEjED,uBAAuB,CAACtB,gBAAgB,CAACE,KAAK;kBAAC;oBAAAsB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNlE,OAAA,CAAChB,cAAc;kBACbiG,OAAO,EAAC,aAAa;kBACrB/D,KAAK,EAAGqB,gBAAgB,CAACE,KAAK,GAAG,CAAC,GAAI,GAAI;kBAC1CuC,KAAK,EAAEpB,wBAAwB,CAACrB,gBAAgB,CAACE,KAAK,CAAE;kBACxDqC,EAAE,EAAE;oBAAEwB,MAAM,EAAE,CAAC;oBAAEC,YAAY,EAAE;kBAAE;gBAAE;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC,EACD3B,gBAAgB,CAACG,MAAM,CAAC8D,MAAM,GAAG,CAAC,iBACjCxG,OAAA,CAAC5B,GAAG;kBAACiI,EAAE,EAAE,CAAE;kBAAAvC,QAAA,EACRvB,gBAAgB,CAACG,MAAM,CAAC+D,GAAG,CAAC,CAAC3D,KAAK,EAAE4D,KAAK,kBACxC1G,OAAA,CAACvB,UAAU;oBAAawG,OAAO,EAAC,SAAS;oBAACD,KAAK,EAAC,OAAO;oBAACV,OAAO,EAAC,OAAO;oBAAAR,QAAA,GAAC,SACpE,EAAChB,KAAK;kBAAA,GADO4D,KAAK;oBAAA3C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEV,CACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACN,eAEDlE,OAAA,CAACzB,SAAS;gBAAA,GACJoE,QAAQ,CAAC,kBAAkB,CAAC;gBAChC6C,SAAS;gBACTC,KAAK,EAAC,kBAAkB;gBACxBO,IAAI,EAAE3D,mBAAmB,GAAG,MAAM,GAAG,UAAW;gBAChDqD,YAAY,EAAC,cAAc;gBAC3BO,MAAM,EAAC,QAAQ;gBACfnD,KAAK,EAAE,CAAC,CAACJ,MAAM,CAACtB,gBAAiB;gBACjCuE,UAAU,GAAAzD,qBAAA,GAAEQ,MAAM,CAACtB,gBAAgB,cAAAc,qBAAA,uBAAvBA,qBAAA,CAAyB0D,OAAQ;gBAC7CC,UAAU,EAAE;kBACVC,cAAc,eACZ9F,OAAA,CAACnB,cAAc;oBAACkH,QAAQ,EAAC,OAAO;oBAAAjC,QAAA,eAC9B9D,OAAA,CAACX,IAAI;sBAAC2F,KAAK,EAAC;oBAAQ;sBAAAjB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CACjB;kBACDgC,YAAY,eACVlG,OAAA,CAACnB,cAAc;oBAACkH,QAAQ,EAAC,KAAK;oBAAAjC,QAAA,eAC5B9D,OAAA,CAAClB,UAAU;sBACTqH,OAAO,EAAEA,CAAA,KAAM7D,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;sBAC5D+D,IAAI,EAAC,KAAK;sBAAAtC,QAAA,EAETzB,mBAAmB,gBAAGrC,OAAA,CAACd,aAAa;wBAAA6E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,gBAAGlE,OAAA,CAACf,UAAU;wBAAA8E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/C;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAEpB;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGFlE,OAAA,CAACtB,gBAAgB;gBACfiI,OAAO,eACL3G,OAAA,CAACrB,QAAQ;kBAAA,GACHgE,QAAQ,CAAC,cAAc,CAAC;kBAC5BqC,KAAK,EAAC;gBAAS;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CACF;gBACDuB,KAAK,eACHzF,OAAA,CAACvB,UAAU;kBAACwG,OAAO,EAAC,OAAO;kBAAAnB,QAAA,GAAC,gBACZ,EAAC,GAAG,eAClB9D,OAAA,CAAC9B,IAAI;oBAAC0I,EAAE,EAAC,UAAU;oBAACC,KAAK,EAAE;sBAAE7B,KAAK,EAAE;oBAAU,CAAE;oBAAAlB,QAAA,EAAC;kBAEjD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAAC,GAAG,EAAC,KACT,EAAC,GAAG,eACPlE,OAAA,CAAC9B,IAAI;oBAAC0I,EAAE,EAAC,QAAQ;oBAACC,KAAK,EAAE;sBAAE7B,KAAK,EAAE;oBAAU,CAAE;oBAAAlB,QAAA,EAAC;kBAE/C;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CACb;gBACDY,EAAE,EAAE;kBAAEuB,EAAE,EAAE,CAAC;kBAAE7B,UAAU,EAAE;gBAAa;cAAE;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC,EACDxB,MAAM,CAACnB,YAAY,iBAClBvB,OAAA,CAACvB,UAAU;gBAACwG,OAAO,EAAC,SAAS;gBAACD,KAAK,EAAC,OAAO;gBAAAlB,QAAA,EACxCpB,MAAM,CAACnB,YAAY,CAACqE;cAAO;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CACb,eAEDlE,OAAA,CAACtB,gBAAgB;gBACfiI,OAAO,eACL3G,OAAA,CAACrB,QAAQ;kBAAA,GACHgE,QAAQ,CAAC,mBAAmB,CAAC;kBACjCqC,KAAK,EAAC;gBAAS;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CACF;gBACDuB,KAAK,EAAC,kDAAkD;gBACxDX,EAAE,EAAE;kBAAEuB,EAAE,EAAE,CAAC;kBAAExB,EAAE,EAAE;gBAAE;cAAE;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC,eAEFlE,OAAA,CAACxB,MAAM;gBACLwH,IAAI,EAAC,QAAQ;gBACbR,SAAS;gBACTP,OAAO,EAAC,WAAW;gBACnB6B,IAAI,EAAC,OAAO;gBACZC,QAAQ,EAAElE,SAAU;gBACpBiC,EAAE,EAAE;kBAAEuB,EAAE,EAAE,CAAC;kBAAExB,EAAE,EAAE,CAAC;kBAAEF,EAAE,EAAE;gBAAI,CAAE;gBAAAb,QAAA,EAE7BjB,SAAS,GAAG,qBAAqB,GAAG;cAAgB;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,eAETlE,OAAA,CAAC5B,GAAG;gBAACwG,SAAS,EAAC,QAAQ;gBAACyB,EAAE,EAAE,CAAE;gBAAAvC,QAAA,eAC5B9D,OAAA,CAACvB,UAAU;kBAACwG,OAAO,EAAC,OAAO;kBAACD,KAAK,EAAC,gBAAgB;kBAAAlB,QAAA,GAAC,0BACzB,EAAC,GAAG,eAC5B9D,OAAA,CAAC9B,IAAI;oBACH0I,EAAE,EAAC,QAAQ;oBACXC,KAAK,EAAE;sBACL7B,KAAK,EAAE,SAAS;sBAChBgC,cAAc,EAAE,MAAM;sBACtBC,UAAU,EAAE;oBACd,CAAE;oBAAAnD,QAAA,EACH;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA,eACZ,CAAC;AAEP,CAAC;AAACvC,EAAA,CAxVID,YAAsB;EAAA,QAKuC7B,YAAY,EAC5D1B,WAAW,EAOxBqB,OAAO;AAAA;AAAA0H,EAAA,GAbPxF,YAAsB;AA0V5B,eAAeA,YAAY;AAAC,IAAAwF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}