# 🚀 TrustVault Deployment Guide

This guide provides step-by-step instructions for deploying TrustVault in different environments.

## 📋 Prerequisites

### System Requirements
- **OS**: Linux (Ubuntu 20.04+ recommended), macOS, or Windows with WSL2
- **RAM**: 8GB minimum, 16GB recommended
- **Storage**: 20GB minimum, 50GB recommended
- **CPU**: 4 cores minimum, 8 cores recommended

### Software Requirements
- Docker 20.10+
- Docker Compose 2.0+
- Git 2.30+
- OpenSSL (for certificate generation)

## 🔧 Environment Setup

### 1. Clone the Repository

```bash
git clone https://github.com/your-org/trustvault.git
cd trustvault
```

### 2. Create Environment Configuration

```bash
# Copy the example environment file
cp .env.example .env

# Edit the environment file with your settings
nano .env
```

### 3. Generate Secrets

```bash
# Create secrets directory
mkdir -p secrets

# Generate Django secret key
python -c "from django.core.management.utils import get_random_secret_key; print(get_random_secret_key())" > secrets/django_secret.txt

# Generate database password
openssl rand -base64 32 > secrets/db_password.txt

# Generate JWT secret
openssl rand -base64 64 > secrets/jwt_secret.txt

# Generate encryption key (32 characters)
openssl rand -hex 16 > secrets/encryption_key.txt

# Set proper permissions
chmod 600 secrets/*
```

### 4. Configure Environment Variables

Edit the `.env` file with your specific configuration:

```bash
# Django Configuration
DEBUG=False
DJANGO_SECRET_KEY=$(cat secrets/django_secret.txt)
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com

# Database Configuration
DB_PASSWORD=$(cat secrets/db_password.txt)
POSTGRES_DB=trustvault
POSTGRES_USER=trustvault

# Redis Configuration
REDIS_PASSWORD=$(openssl rand -base64 32)

# Security Configuration
JWT_SECRET_KEY=$(cat secrets/jwt_secret.txt)
ENCRYPTION_KEY=$(cat secrets/encryption_key.txt)
VAULT_ROOT_TOKEN=$(openssl rand -base64 32)

# Email Configuration (configure with your SMTP provider)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
EMAIL_USE_TLS=True

# Monitoring Configuration
GRAFANA_ADMIN_PASSWORD=$(openssl rand -base64 16)
```

## 🐳 Docker Deployment

### Development Environment

```bash
# Start all services
docker-compose up -d

# Check service status
docker-compose ps

# View logs
docker-compose logs -f django
```

### Production Environment

```bash
# Use production configuration
docker-compose -f docker-compose.prod.yml up -d

# Initialize database
docker-compose exec django python manage.py migrate
docker-compose exec django python manage.py collectstatic --noinput
docker-compose exec django python manage.py createsuperuser
```

## 🔒 SSL/TLS Configuration

### Using Let's Encrypt (Recommended for Production)

```bash
# Install Certbot
sudo apt-get update
sudo apt-get install certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d your-domain.com -d api.your-domain.com

# Test automatic renewal
sudo certbot renew --dry-run
```

### Using Self-Signed Certificates (Development)

```bash
# Create SSL directory
mkdir -p ssl

# Generate private key
openssl genrsa -out ssl/private.key 2048

# Generate certificate signing request
openssl req -new -key ssl/private.key -out ssl/certificate.csr

# Generate self-signed certificate
openssl x509 -req -days 365 -in ssl/certificate.csr -signkey ssl/private.key -out ssl/certificate.crt

# Set proper permissions
chmod 600 ssl/private.key
chmod 644 ssl/certificate.crt
```

## 🌐 Nginx Configuration

Create `/etc/nginx/sites-available/trustvault`:

```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /path/to/ssl/certificate.crt;
    ssl_certificate_key /path/to/ssl/private.key;

    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # Frontend
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # API
    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Admin
    location /admin/ {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

Enable the site:

```bash
sudo ln -s /etc/nginx/sites-available/trustvault /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 🔍 Monitoring Setup

### Grafana Configuration

1. Access Grafana at `http://localhost:3001`
2. Login with admin/admin (change password immediately)
3. Add Prometheus data source: `http://prometheus:9090`
4. Import dashboards from `monitoring/grafana/dashboards/`

### Prometheus Configuration

The Prometheus configuration is automatically loaded from `monitoring/prometheus/prometheus.yml`.

### Wazuh Configuration

1. Access Wazuh dashboard at `http://localhost:5601`
2. Login with admin/admin
3. Configure agents and rules as needed

## 🧪 Testing the Deployment

### Health Checks

```bash
# Check all services are running
docker-compose ps

# Test API endpoints
curl -k https://localhost:8000/api/v1/core/status/

# Test frontend
curl -k https://localhost:3000/health

# Check database connection
docker-compose exec django python manage.py dbshell
```

### Load Testing

```bash
# Install Apache Bench
sudo apt-get install apache2-utils

# Test API performance
ab -n 1000 -c 10 https://localhost:8000/api/v1/core/status/

# Test frontend performance
ab -n 1000 -c 10 https://localhost:3000/
```

## 🔧 Maintenance

### Backup Procedures

```bash
# Database backup
docker-compose exec postgres pg_dump -U trustvault trustvault > backup_$(date +%Y%m%d_%H%M%S).sql

# Volume backup
docker run --rm -v trustvault_postgres_data:/data -v $(pwd):/backup alpine tar czf /backup/postgres_backup_$(date +%Y%m%d_%H%M%S).tar.gz /data
```

### Updates

```bash
# Pull latest images
docker-compose pull

# Restart services
docker-compose down
docker-compose up -d

# Run migrations if needed
docker-compose exec django python manage.py migrate
```

### Log Management

```bash
# View logs
docker-compose logs -f [service_name]

# Rotate logs
docker-compose exec django python manage.py clearsessions
```

## 🚨 Troubleshooting

### Common Issues

1. **Port conflicts**: Ensure ports 3000, 8000, 5432, 6379 are available
2. **Permission issues**: Check file permissions on secrets and SSL certificates
3. **Memory issues**: Increase Docker memory allocation if needed
4. **Database connection**: Verify database credentials and network connectivity

### Debug Mode

```bash
# Enable debug mode temporarily
export DEBUG=True
docker-compose restart django

# View detailed logs
docker-compose logs -f django
```

### Performance Issues

```bash
# Check resource usage
docker stats

# Monitor database performance
docker-compose exec postgres psql -U trustvault -c "SELECT * FROM pg_stat_activity;"

# Check Redis performance
docker-compose exec redis redis-cli info
```

## 📞 Support

For deployment issues:
- Check the logs: `docker-compose logs -f`
- Review the troubleshooting section above
- Open an issue on GitHub with deployment details
- Contact support: <EMAIL>

---

**⚠️ Security Reminder**: Always use strong passwords, enable HTTPS in production, and regularly update your deployment with security patches.
