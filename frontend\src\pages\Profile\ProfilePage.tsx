// TrustVault - Profile Page

import React from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Card,
  CardContent,
  TextField,
  Button,
  Grid,
  Avatar,
  Divider,
  Alert,
} from '@mui/material';
import {
  Person,
  Security,
  Save,
  Lock,
} from '@mui/icons-material';
import { Helmet } from 'react-helmet-async';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { toast } from 'react-hot-toast';

// Store
import { useAuthStore } from '../../store/authStore';

// Types
import { User } from '../../types';

// Validation schema
const profileSchema = yup.object({
  first_name: yup
    .string()
    .min(2, 'First name must be at least 2 characters')
    .required('First name is required'),
  last_name: yup
    .string()
    .min(2, 'Last name must be at least 2 characters')
    .required('Last name is required'),
  phone_number: yup
    .string()
    .matches(/^\+?[1-9]\d{1,14}$/, 'Invalid phone number format')
    .optional(),
  timezone: yup.string().required('Timezone is required'),
  language: yup.string().required('Language is required'),
});

const ProfilePage: React.FC = () => {
  const { user, updateProfile, isLoading } = useAuthStore();

  const {
    register,
    handleSubmit,
    formState: { errors, isDirty },
  } = useForm<Partial<User>>({
    resolver: yupResolver(profileSchema),
    defaultValues: {
      first_name: user?.first_name || '',
      last_name: user?.last_name || '',
      phone_number: user?.phone_number || '',
      timezone: user?.timezone || 'UTC',
      language: user?.language || 'en',
    },
  });

  const onSubmit = async (data: Partial<User>) => {
    try {
      await updateProfile(data);
      toast.success('Profile updated successfully!');
    } catch (error) {
      toast.error('Failed to update profile');
    }
  };

  if (!user) {
    return <Typography>Loading...</Typography>;
  }

  return (
    <>
      <Helmet>
        <title>Profile - TrustVault</title>
        <meta name="description" content="Manage your profile settings" />
      </Helmet>

      <Box>
        {/* Header */}
        <Box mb={4}>
          <Typography variant="h4" component="h1" gutterBottom>
            Profile Settings
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage your personal information and account settings
          </Typography>
        </Box>

        <Grid container spacing={3}>
          {/* Profile Information */}
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={2} mb={3}>
                  <Person color="primary" />
                  <Typography variant="h6">
                    Personal Information
                  </Typography>
                </Box>

                <Box component="form" onSubmit={handleSubmit(onSubmit)}>
                  <Grid container spacing={3}>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        {...register('first_name')}
                        fullWidth
                        label="First Name"
                        error={!!errors.first_name}
                        helperText={errors.first_name?.message}
                      />
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <TextField
                        {...register('last_name')}
                        fullWidth
                        label="Last Name"
                        error={!!errors.last_name}
                        helperText={errors.last_name?.message}
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <TextField
                        value={user.email}
                        fullWidth
                        label="Email Address"
                        disabled
                        helperText="Email cannot be changed"
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <TextField
                        value={user.username}
                        fullWidth
                        label="Username"
                        disabled
                        helperText="Username cannot be changed"
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <TextField
                        {...register('phone_number')}
                        fullWidth
                        label="Phone Number"
                        type="tel"
                        error={!!errors.phone_number}
                        helperText={errors.phone_number?.message}
                      />
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <TextField
                        {...register('timezone')}
                        fullWidth
                        label="Timezone"
                        select
                        SelectProps={{ native: true }}
                        error={!!errors.timezone}
                        helperText={errors.timezone?.message}
                      >
                        <option value="UTC">UTC</option>
                        <option value="America/New_York">Eastern Time</option>
                        <option value="America/Chicago">Central Time</option>
                        <option value="America/Denver">Mountain Time</option>
                        <option value="America/Los_Angeles">Pacific Time</option>
                        <option value="Europe/London">London</option>
                        <option value="Europe/Paris">Paris</option>
                        <option value="Asia/Tokyo">Tokyo</option>
                      </TextField>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <TextField
                        {...register('language')}
                        fullWidth
                        label="Language"
                        select
                        SelectProps={{ native: true }}
                        error={!!errors.language}
                        helperText={errors.language?.message}
                      >
                        <option value="en">English</option>
                        <option value="fr">Français</option>
                        <option value="es">Español</option>
                        <option value="de">Deutsch</option>
                      </TextField>
                    </Grid>
                  </Grid>

                  <Box mt={3}>
                    <Button
                      type="submit"
                      variant="contained"
                      startIcon={<Save />}
                      disabled={!isDirty || isLoading}
                    >
                      {isLoading ? 'Saving...' : 'Save Changes'}
                    </Button>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Profile Summary & Security */}
          <Grid item xs={12} md={4}>
            {/* Profile Summary */}
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Box display="flex" flexDirection="column" alignItems="center" textAlign="center">
                  <Avatar
                    sx={{
                      width: 80,
                      height: 80,
                      mb: 2,
                      fontSize: '2rem',
                    }}
                  >
                    {user.first_name?.[0]}{user.last_name?.[0]}
                  </Avatar>
                  
                  <Typography variant="h6" gutterBottom>
                    {user.first_name} {user.last_name}
                  </Typography>
                  
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    {user.email}
                  </Typography>
                  
                  <Typography variant="caption" color="text.secondary">
                    Member since {new Date(user.date_joined).toLocaleDateString()}
                  </Typography>
                </Box>
              </CardContent>
            </Card>

            {/* Security Settings */}
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={2} mb={3}>
                  <Security color="primary" />
                  <Typography variant="h6">
                    Security
                  </Typography>
                </Box>

                <Box mb={2}>
                  <Typography variant="body2" gutterBottom>
                    Two-Factor Authentication
                  </Typography>
                  <Alert
                    severity={user.is_mfa_enabled ? 'success' : 'warning'}
                    sx={{ mb: 2 }}
                  >
                    {user.is_mfa_enabled ? 'Enabled' : 'Disabled'}
                  </Alert>
                  {!user.is_mfa_enabled && (
                    <Button
                      variant="outlined"
                      size="small"
                      fullWidth
                      sx={{ mb: 2 }}
                    >
                      Enable 2FA
                    </Button>
                  )}
                </Box>

                <Divider sx={{ my: 2 }} />

                <Box>
                  <Typography variant="body2" gutterBottom>
                    Password
                  </Typography>
                  <Typography variant="caption" color="text.secondary" display="block" mb={2}>
                    Last changed: {new Date(user.date_joined).toLocaleDateString()}
                  </Typography>
                  <Button
                    variant="outlined"
                    size="small"
                    fullWidth
                    startIcon={<Lock />}
                  >
                    Change Password
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default ProfilePage;
