// TrustVault - Add Holding Page

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Autocomplete,
  Alert,
  CircularProgress,
  Grid,
  InputAdornment,
} from '@mui/material';
import { ArrowBack, Add } from '@mui/icons-material';
import { Helmet } from 'react-helmet-async';
import { useNavigate, useParams } from 'react-router-dom';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import { toast } from 'react-hot-toast';

// Services
import apiService from '../../services/api';

// Types
interface AddHoldingForm {
  asset_id: string;
  quantity: number;
  purchase_price: number;
  purchase_date: string;
  notes?: string;
}

const AddHoldingPage: React.FC = () => {
  const navigate = useNavigate();
  const { id: portfolioId } = useParams<{ id: string }>();
  const queryClient = useQueryClient();

  const [formData, setFormData] = useState<AddHoldingForm>({
    asset_id: '',
    quantity: 0,
    purchase_price: 0,
    purchase_date: new Date().toISOString().split('T')[0],
    notes: '',
  });

  const [selectedAsset, setSelectedAsset] = useState<any>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Fetch available assets
  const { data: assets, isLoading: assetsLoading } = useQuery(
    'assets',
    () => apiService.getAssets()
  );

  // Fetch portfolio details
  const { data: portfolio } = useQuery(
    ['portfolio', portfolioId],
    () => apiService.getPortfolio(portfolioId!),
    {
      enabled: !!portfolioId,
    }
  );

  const addHoldingMutation = useMutation(
    (data: AddHoldingForm) => {
      if (!portfolioId) throw new Error('Portfolio ID is required');
      return apiService.createHolding(portfolioId, data);
    },
    {
      onSuccess: () => {
        toast.success('Holding added successfully!');
        queryClient.invalidateQueries(['portfolio', portfolioId]);
        queryClient.invalidateQueries(['holdings', portfolioId]);
        navigate(`/portfolios/${portfolioId}`);
      },
      onError: (error: any) => {
        console.error('Add holding error:', error);
        if (error.response?.data) {
          setErrors(error.response.data);
        } else {
          toast.error('Failed to add holding. Please try again.');
        }
      },
    }
  );

  const handleInputChange = (field: keyof AddHoldingForm) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = event.target.type === 'number' ? parseFloat(event.target.value) || 0 : event.target.value;
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
    
    // Clear error when user starts typing
    if (field in errors) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const handleAssetChange = (event: any, newValue: any) => {
    setSelectedAsset(newValue);
    setFormData(prev => ({
      ...prev,
      asset_id: newValue?.id || '',
    }));
    
    if (errors.asset_id) {
      setErrors(prev => ({
        ...prev,
        asset_id: '',
      }));
    }
  };

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    
    // Basic validation
    const newErrors: Record<string, string> = {};
    
    if (!formData.asset_id) {
      newErrors.asset_id = 'Please select an asset';
    }
    
    if (formData.quantity <= 0) {
      newErrors.quantity = 'Quantity must be greater than 0';
    }
    
    if (formData.purchase_price <= 0) {
      newErrors.purchase_price = 'Purchase price must be greater than 0';
    }
    
    if (!formData.purchase_date) {
      newErrors.purchase_date = 'Purchase date is required';
    }
    
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }
    
    addHoldingMutation.mutate(formData);
  };

  const calculateTotalValue = () => {
    return formData.quantity * formData.purchase_price;
  };

  if (!portfolioId) {
    return (
      <Box sx={{ maxWidth: 800, mx: 'auto', p: 3 }}>
        <Typography variant="h6" color="error">
          Portfolio ID is required
        </Typography>
      </Box>
    );
  }

  return (
    <>
      <Helmet>
        <title>Add Holding - TrustVault</title>
        <meta name="description" content="Add a new holding to your portfolio" />
      </Helmet>

      <Box sx={{ maxWidth: 800, mx: 'auto', p: 3 }}>
        {/* Header */}
        <Box display="flex" alignItems="center" mb={3}>
          <Button
            startIcon={<ArrowBack />}
            onClick={() => navigate(`/portfolios/${portfolioId}`)}
            sx={{ mr: 2 }}
          >
            Back to Portfolio
          </Button>
          <Box>
            <Typography variant="h4" component="h1">
              Add New Holding
            </Typography>
            {portfolio && (
              <Typography variant="body2" color="text.secondary">
                to {portfolio.name}
              </Typography>
            )}
          </Box>
        </Box>

        {/* Form */}
        <Paper sx={{ p: 4 }}>
          <form onSubmit={handleSubmit}>
            <Grid container spacing={3}>
              {/* Asset Selection */}
              <Grid item xs={12}>
                <Autocomplete
                  options={assets || []}
                  getOptionLabel={(option) => `${option.symbol} - ${option.name}`}
                  value={selectedAsset}
                  onChange={handleAssetChange}
                  loading={assetsLoading}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Select Asset"
                      error={!!errors.asset_id}
                      helperText={errors.asset_id}
                      required
                      InputProps={{
                        ...params.InputProps,
                        endAdornment: (
                          <>
                            {assetsLoading ? <CircularProgress color="inherit" size={20} /> : null}
                            {params.InputProps.endAdornment}
                          </>
                        ),
                      }}
                    />
                  )}
                />
              </Grid>

              {/* Quantity */}
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Quantity"
                  type="number"
                  value={formData.quantity || ''}
                  onChange={handleInputChange('quantity')}
                  error={!!errors.quantity}
                  helperText={errors.quantity}
                  required
                  fullWidth
                  inputProps={{ min: 0, step: 0.01 }}
                />
              </Grid>

              {/* Purchase Price */}
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Purchase Price"
                  type="number"
                  value={formData.purchase_price || ''}
                  onChange={handleInputChange('purchase_price')}
                  error={!!errors.purchase_price}
                  helperText={errors.purchase_price}
                  required
                  fullWidth
                  InputProps={{
                    startAdornment: <InputAdornment position="start">$</InputAdornment>,
                  }}
                  inputProps={{ min: 0, step: 0.01 }}
                />
              </Grid>

              {/* Purchase Date */}
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Purchase Date"
                  type="date"
                  value={formData.purchase_date}
                  onChange={handleInputChange('purchase_date')}
                  error={!!errors.purchase_date}
                  helperText={errors.purchase_date}
                  required
                  fullWidth
                  InputLabelProps={{
                    shrink: true,
                  }}
                />
              </Grid>

              {/* Total Value Display */}
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Total Value"
                  value={`$${calculateTotalValue().toFixed(2)}`}
                  fullWidth
                  disabled
                  InputProps={{
                    readOnly: true,
                  }}
                />
              </Grid>

              {/* Notes */}
              <Grid item xs={12}>
                <TextField
                  label="Notes (Optional)"
                  value={formData.notes}
                  onChange={handleInputChange('notes')}
                  multiline
                  rows={3}
                  fullWidth
                  placeholder="Add any notes about this holding..."
                />
              </Grid>

              {/* Error Display */}
              {Object.keys(errors).length > 0 && (
                <Grid item xs={12}>
                  <Alert severity="error">
                    Please fix the errors above and try again.
                  </Alert>
                </Grid>
              )}

              {/* Submit Buttons */}
              <Grid item xs={12}>
                <Box display="flex" justifyContent="flex-end" gap={2} mt={2}>
                  <Button
                    variant="outlined"
                    onClick={() => navigate(`/portfolios/${portfolioId}`)}
                    disabled={addHoldingMutation.isLoading}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    variant="contained"
                    startIcon={addHoldingMutation.isLoading ? <CircularProgress size={20} /> : <Add />}
                    disabled={addHoldingMutation.isLoading}
                  >
                    {addHoldingMutation.isLoading ? 'Adding...' : 'Add Holding'}
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </form>
        </Paper>

        {/* Info Box */}
        <Paper sx={{ p: 3, mt: 3, bgcolor: 'background.default' }}>
          <Typography variant="h6" gutterBottom>
            About Holdings
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Holdings represent your investments in specific assets. Once added, you can:
          </Typography>
          <Box component="ul" sx={{ mt: 1, pl: 2 }}>
            <Typography component="li" variant="body2" color="text.secondary">
              Track performance and current value
            </Typography>
            <Typography component="li" variant="body2" color="text.secondary">
              Record additional transactions (buy/sell)
            </Typography>
            <Typography component="li" variant="body2" color="text.secondary">
              View detailed analytics and charts
            </Typography>
            <Typography component="li" variant="body2" color="text.secondary">
              Monitor portfolio allocation
            </Typography>
          </Box>
        </Paper>
      </Box>
    </>
  );
};

export default AddHoldingPage;
