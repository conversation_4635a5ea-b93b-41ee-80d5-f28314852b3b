import * as React from 'react';
import PropTypes from 'prop-types';
import useChartDimensions from '../hooks/useChartDimensions';

/**
 * Defines the area in which it is possible to draw the charts.
 */
import { jsx as _jsx } from "react/jsx-runtime";
export const DrawingContext = /*#__PURE__*/React.createContext({
  top: 0,
  left: 0,
  bottom: 0,
  right: 0,
  height: 300,
  width: 400
});
export const SVGContext = /*#__PURE__*/React.createContext({
  current: null
});

/**
 * API:
 *
 * - [DrawingProvider API](https://mui.com/x/api/charts/drawing-provider/)
 */
function DrawingProvider(props) {
  const {
    width,
    height,
    margin,
    svgRef,
    children
  } = props;
  const drawingArea = useChartDimensions(width, height, margin);
  return /*#__PURE__*/_jsx(SVGContext.Provider, {
    value: svgRef,
    children: /*#__PURE__*/_jsx(DrawingContext.Provider, {
      value: drawingArea,
      children: children
    })
  });
}
process.env.NODE_ENV !== "production" ? DrawingProvider.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  children: PropTypes.node,
  height: PropTypes.number.isRequired,
  /**
   * The margin between the SVG and the drawing area.
   * It's used for leaving some space for extra information such as the x- and y-axis or legend.
   * Accepts an object with the optional properties: `top`, `bottom`, `left`, and `right`.
   * @default object Depends on the charts type.
   */
  margin: PropTypes.shape({
    bottom: PropTypes.number,
    left: PropTypes.number,
    right: PropTypes.number,
    top: PropTypes.number
  }),
  svgRef: PropTypes.shape({
    current: PropTypes.object
  }).isRequired,
  width: PropTypes.number.isRequired
} : void 0;
export { DrawingProvider };