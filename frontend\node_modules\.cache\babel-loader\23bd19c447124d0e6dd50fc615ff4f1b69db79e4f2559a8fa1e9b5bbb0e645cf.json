{"ast": null, "code": "// TrustVault - Portfolio Analytics Page\nimport React,{useState}from'react';import{Box,Typography,Button,Grid,Card,CardContent,Paper,Chip,FormControl,InputLabel,Select,MenuItem,LinearProgress}from'@mui/material';import{ArrowBack,TrendingUp,TrendingDown,PieChart,<PERSON><PERSON>hart,Assessment}from'@mui/icons-material';import{Helmet}from'react-helmet-async';import{useNavigate,useParams}from'react-router-dom';import{useQuery}from'react-query';// Services\nimport apiService from'../../services/api';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const AnalyticsPage=()=>{const navigate=useNavigate();const{id:portfolioId}=useParams();const[timeRange,setTimeRange]=useState('1M');// Fetch portfolio details\nconst{data:portfolio}=useQuery(['portfolio',portfolioId],()=>apiService.getPortfolio(portfolioId),{enabled:!!portfolioId});// Fetch holdings for allocation analysis\nconst{data:holdings}=useQuery(['holdings',portfolioId],()=>apiService.getHoldings(portfolioId),{enabled:!!portfolioId});const formatCurrency=value=>{return new Intl.NumberFormat('en-US',{style:'currency',currency:'USD'}).format(typeof value==='string'?parseFloat(value):value);};const formatPercentage=value=>{return`${value>=0?'+':''}${value.toFixed(2)}%`;};// Calculate portfolio metrics\nconst totalValue=portfolio?parseFloat(portfolio.total_value):0;const mockMetrics={totalReturn:1250.75,totalReturnPercent:8.45,dayChange:-45.32,dayChangePercent:-0.32,weekChange:125.50,weekChangePercent:0.89,monthChange:450.25,monthChangePercent:3.21};// Mock allocation data\nconst allocationData=(holdings===null||holdings===void 0?void 0:holdings.map(holding=>({symbol:holding.asset.symbol,name:holding.asset.name,value:parseFloat(holding.current_value),percentage:parseFloat(holding.current_value)/totalValue*100,sector:holding.asset.sector})))||[];// Group by sector\nconst sectorAllocation=allocationData.reduce((acc,holding)=>{const sector=holding.sector||'Other';if(!(sector in acc)){acc[sector]={value:0,percentage:0};}acc[sector].value+=holding.value;acc[sector].percentage+=holding.percentage;return acc;},{});if(!portfolioId){return/*#__PURE__*/_jsx(Box,{sx:{maxWidth:1200,mx:'auto',p:3},children:/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"error\",children:\"Portfolio ID is required\"})});}return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(Helmet,{children:[/*#__PURE__*/_jsx(\"title\",{children:\"Analytics - TrustVault\"}),/*#__PURE__*/_jsx(\"meta\",{name:\"description\",content:\"Portfolio performance analytics and insights\"})]}),/*#__PURE__*/_jsxs(Box,{sx:{maxWidth:1200,mx:'auto',p:3},children:[/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",justifyContent:\"space-between\",mb:3,children:[/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",children:[/*#__PURE__*/_jsx(Button,{startIcon:/*#__PURE__*/_jsx(ArrowBack,{}),onClick:()=>navigate(`/portfolios/${portfolioId}`),sx:{mr:2},children:\"Back to Portfolio\"}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",component:\"h1\",children:\"Portfolio Analytics\"}),portfolio&&/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:portfolio.name})]})]}),/*#__PURE__*/_jsxs(FormControl,{size:\"small\",sx:{minWidth:120},children:[/*#__PURE__*/_jsx(InputLabel,{children:\"Time Range\"}),/*#__PURE__*/_jsxs(Select,{value:timeRange,onChange:e=>setTimeRange(e.target.value),label:\"Time Range\",children:[/*#__PURE__*/_jsx(MenuItem,{value:\"1D\",children:\"1 Day\"}),/*#__PURE__*/_jsx(MenuItem,{value:\"1W\",children:\"1 Week\"}),/*#__PURE__*/_jsx(MenuItem,{value:\"1M\",children:\"1 Month\"}),/*#__PURE__*/_jsx(MenuItem,{value:\"3M\",children:\"3 Months\"}),/*#__PURE__*/_jsx(MenuItem,{value:\"6M\",children:\"6 Months\"}),/*#__PURE__*/_jsx(MenuItem,{value:\"1Y\",children:\"1 Year\"}),/*#__PURE__*/_jsx(MenuItem,{value:\"ALL\",children:\"All Time\"})]})]})]}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Paper,{sx:{p:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Performance Overview\"}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsx(Card,{variant:\"outlined\",children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",mb:1,children:[/*#__PURE__*/_jsx(Assessment,{color:\"primary\",sx:{mr:1}}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Total Value\"})]}),/*#__PURE__*/_jsx(Typography,{variant:\"h5\",fontWeight:\"bold\",children:formatCurrency(totalValue)})]})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsx(Card,{variant:\"outlined\",children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",mb:1,children:[/*#__PURE__*/_jsx(TrendingUp,{color:\"success\",sx:{mr:1}}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Total Return\"})]}),/*#__PURE__*/_jsx(Typography,{variant:\"h5\",fontWeight:\"bold\",color:\"success.main\",children:formatCurrency(mockMetrics.totalReturn)}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"success.main\",children:formatPercentage(mockMetrics.totalReturnPercent)})]})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsx(Card,{variant:\"outlined\",children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",mb:1,children:[/*#__PURE__*/_jsx(TrendingDown,{color:\"error\",sx:{mr:1}}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Day Change\"})]}),/*#__PURE__*/_jsx(Typography,{variant:\"h5\",fontWeight:\"bold\",color:\"error.main\",children:formatCurrency(mockMetrics.dayChange)}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"error.main\",children:formatPercentage(mockMetrics.dayChangePercent)})]})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsx(Card,{variant:\"outlined\",children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",mb:1,children:[/*#__PURE__*/_jsx(ShowChart,{color:\"info\",sx:{mr:1}}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Month Change\"})]}),/*#__PURE__*/_jsx(Typography,{variant:\"h5\",fontWeight:\"bold\",color:\"success.main\",children:formatCurrency(mockMetrics.monthChange)}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"success.main\",children:formatPercentage(mockMetrics.monthChangePercent)})]})})})]})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsxs(Paper,{sx:{p:3},children:[/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",mb:3,children:[/*#__PURE__*/_jsx(PieChart,{color:\"primary\",sx:{mr:1}}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:\"Asset Allocation\"})]}),allocationData.length===0?/*#__PURE__*/_jsx(Box,{textAlign:\"center\",py:4,children:/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"No holdings to display\"})}):/*#__PURE__*/_jsx(Box,{children:allocationData.slice(0,5).map((holding,index)=>/*#__PURE__*/_jsxs(Box,{mb:2,children:[/*#__PURE__*/_jsxs(Box,{display:\"flex\",justifyContent:\"space-between\",alignItems:\"center\",mb:1,children:[/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",fontWeight:\"medium\",children:holding.symbol}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",children:holding.name})]}),/*#__PURE__*/_jsxs(Box,{textAlign:\"right\",children:[/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",fontWeight:\"medium\",children:[holding.percentage.toFixed(1),\"%\"]}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",children:formatCurrency(holding.value)})]})]}),/*#__PURE__*/_jsx(LinearProgress,{variant:\"determinate\",value:holding.percentage,sx:{height:6,borderRadius:3}})]},holding.symbol))})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsxs(Paper,{sx:{p:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Sector Allocation\"}),Object.keys(sectorAllocation).length===0?/*#__PURE__*/_jsx(Box,{textAlign:\"center\",py:4,children:/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"No sector data available\"})}):/*#__PURE__*/_jsx(Box,{children:Object.entries(sectorAllocation).map(_ref=>{let[sector,data]=_ref;return/*#__PURE__*/_jsxs(Box,{mb:2,children:[/*#__PURE__*/_jsxs(Box,{display:\"flex\",justifyContent:\"space-between\",alignItems:\"center\",mb:1,children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",fontWeight:\"medium\",children:sector}),/*#__PURE__*/_jsxs(Box,{textAlign:\"right\",children:[/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",fontWeight:\"medium\",children:[data.percentage.toFixed(1),\"%\"]}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",children:formatCurrency(data.value)})]})]}),/*#__PURE__*/_jsx(LinearProgress,{variant:\"determinate\",value:data.percentage,sx:{height:6,borderRadius:3}})]},sector);})})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Paper,{sx:{p:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Performance Chart\"}),/*#__PURE__*/_jsx(Box,{sx:{height:300,display:'flex',alignItems:'center',justifyContent:'center',bgcolor:'background.default',borderRadius:1},children:/*#__PURE__*/_jsxs(Box,{textAlign:\"center\",children:[/*#__PURE__*/_jsx(ShowChart,{sx:{fontSize:48,color:'text.secondary',mb:2}}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"text.secondary\",gutterBottom:true,children:\"Performance Chart\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Interactive chart showing portfolio performance over time\"}),/*#__PURE__*/_jsx(Chip,{label:\"Coming Soon\",color:\"primary\",size:\"small\",sx:{mt:1}})]})})]})})]})]})]});};export default AnalyticsPage;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}