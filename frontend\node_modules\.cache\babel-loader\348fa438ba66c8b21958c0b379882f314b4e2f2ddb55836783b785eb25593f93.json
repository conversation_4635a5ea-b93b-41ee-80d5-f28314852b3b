{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\TrustVault\\\\frontend\\\\src\\\\pages\\\\Portfolio\\\\AddHoldingPage.tsx\",\n  _s = $RefreshSig$();\n// TrustVault - Add Holding Page\n\nimport React, { useState } from 'react';\nimport { Box, Typography, Paper, TextField, Button, Autocomplete, Alert, CircularProgress, Grid, InputAdornment } from '@mui/material';\nimport { ArrowBack, Add } from '@mui/icons-material';\nimport { Helmet } from 'react-helmet-async';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { useMutation, useQuery, useQueryClient } from 'react-query';\nimport { toast } from 'react-hot-toast';\n\n// Services\nimport apiService from '../../services/api';\n\n// Types\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AddHoldingPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    id: portfolioId\n  } = useParams();\n  const queryClient = useQueryClient();\n  const [formData, setFormData] = useState({\n    asset_id: '',\n    quantity: 0,\n    purchase_price: 0,\n    purchase_date: new Date().toISOString().split('T')[0],\n    notes: ''\n  });\n  const [selectedAsset, setSelectedAsset] = useState(null);\n  const [errors, setErrors] = useState({});\n\n  // Fetch available assets\n  const {\n    data: assets,\n    isLoading: assetsLoading\n  } = useQuery('assets', apiService.getAssets);\n\n  // Fetch portfolio details\n  const {\n    data: portfolio\n  } = useQuery(['portfolio', portfolioId], () => apiService.getPortfolio(portfolioId), {\n    enabled: !!portfolioId\n  });\n  const addHoldingMutation = useMutation(data => {\n    if (!portfolioId) throw new Error('Portfolio ID is required');\n    return apiService.createHolding(portfolioId, data);\n  }, {\n    onSuccess: () => {\n      toast.success('Holding added successfully!');\n      queryClient.invalidateQueries(['portfolio', portfolioId]);\n      queryClient.invalidateQueries(['holdings', portfolioId]);\n      navigate(`/portfolios/${portfolioId}`);\n    },\n    onError: error => {\n      var _error$response;\n      console.error('Add holding error:', error);\n      if ((_error$response = error.response) !== null && _error$response !== void 0 && _error$response.data) {\n        setErrors(error.response.data);\n      } else {\n        toast.error('Failed to add holding. Please try again.');\n      }\n    }\n  });\n  const handleInputChange = field => event => {\n    const value = event.target.type === 'number' ? parseFloat(event.target.value) || 0 : event.target.value;\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n\n    // Clear error when user starts typing\n    if (Object.prototype.hasOwnProperty.call(errors, field)) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: ''\n      }));\n    }\n  };\n  const handleAssetChange = (event, newValue) => {\n    setSelectedAsset(newValue);\n    setFormData(prev => ({\n      ...prev,\n      asset_id: (newValue === null || newValue === void 0 ? void 0 : newValue.id) || ''\n    }));\n    if (errors.asset_id) {\n      setErrors(prev => ({\n        ...prev,\n        asset_id: ''\n      }));\n    }\n  };\n  const handleSubmit = event => {\n    event.preventDefault();\n\n    // Basic validation\n    const newErrors = {};\n    if (!formData.asset_id) {\n      newErrors.asset_id = 'Please select an asset';\n    }\n    if (formData.quantity <= 0) {\n      newErrors.quantity = 'Quantity must be greater than 0';\n    }\n    if (formData.purchase_price <= 0) {\n      newErrors.purchase_price = 'Purchase price must be greater than 0';\n    }\n    if (!formData.purchase_date) {\n      newErrors.purchase_date = 'Purchase date is required';\n    }\n    if (Object.keys(newErrors).length > 0) {\n      setErrors(newErrors);\n      return;\n    }\n    addHoldingMutation.mutate(formData);\n  };\n  const calculateTotalValue = () => {\n    return formData.quantity * formData.purchase_price;\n  };\n  if (!portfolioId) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        maxWidth: 800,\n        mx: 'auto',\n        p: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"error\",\n        children: \"Portfolio ID is required\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"title\", {\n        children: \"Add Holding - TrustVault\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"description\",\n        content: \"Add a new holding to your portfolio\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        maxWidth: 800,\n        mx: 'auto',\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        alignItems: \"center\",\n        mb: 3,\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          startIcon: /*#__PURE__*/_jsxDEV(ArrowBack, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 24\n          }, this),\n          onClick: () => navigate(`/portfolios/${portfolioId}`),\n          sx: {\n            mr: 2\n          },\n          children: \"Back to Portfolio\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            component: \"h1\",\n            children: \"Add New Holding\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this), portfolio && /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: [\"to \", portfolio.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 3,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Autocomplete, {\n                options: assets || [],\n                getOptionLabel: option => `${option.symbol} - ${option.name}`,\n                value: selectedAsset,\n                onChange: handleAssetChange,\n                loading: assetsLoading,\n                renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n                  ...params,\n                  label: \"Select Asset\",\n                  error: !!errors.asset_id,\n                  helperText: errors.asset_id,\n                  required: true,\n                  InputProps: {\n                    ...params.InputProps,\n                    endAdornment: /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [assetsLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                        color: \"inherit\",\n                        size: 20\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 221,\n                        columnNumber: 46\n                      }, this) : null, params.InputProps.endAdornment]\n                    }, void 0, true)\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                label: \"Quantity\",\n                type: \"number\",\n                value: formData.quantity || '',\n                onChange: handleInputChange('quantity'),\n                error: !!errors.quantity,\n                helperText: errors.quantity,\n                required: true,\n                fullWidth: true,\n                inputProps: {\n                  min: 0,\n                  step: 0.01\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                label: \"Purchase Price\",\n                type: \"number\",\n                value: formData.purchase_price || '',\n                onChange: handleInputChange('purchase_price'),\n                error: !!errors.purchase_price,\n                helperText: errors.purchase_price,\n                required: true,\n                fullWidth: true,\n                InputProps: {\n                  startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                    position: \"start\",\n                    children: \"$\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 258,\n                    columnNumber: 37\n                  }, this)\n                },\n                inputProps: {\n                  min: 0,\n                  step: 0.01\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                label: \"Purchase Date\",\n                type: \"date\",\n                value: formData.purchase_date,\n                onChange: handleInputChange('purchase_date'),\n                error: !!errors.purchase_date,\n                helperText: errors.purchase_date,\n                required: true,\n                fullWidth: true,\n                InputLabelProps: {\n                  shrink: true\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                label: \"Total Value\",\n                value: `$${calculateTotalValue().toFixed(2)}`,\n                fullWidth: true,\n                disabled: true,\n                InputProps: {\n                  readOnly: true\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                label: \"Notes (Optional)\",\n                value: formData.notes,\n                onChange: handleInputChange('notes'),\n                multiline: true,\n                rows: 3,\n                fullWidth: true,\n                placeholder: \"Add any notes about this holding...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 15\n            }, this), Object.keys(errors).length > 0 && /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Alert, {\n                severity: \"error\",\n                children: \"Please fix the errors above and try again.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                justifyContent: \"flex-end\",\n                gap: 2,\n                mt: 2,\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  onClick: () => navigate(`/portfolios/${portfolioId}`),\n                  disabled: addHoldingMutation.isLoading,\n                  children: \"Cancel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 319,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"submit\",\n                  variant: \"contained\",\n                  startIcon: addHoldingMutation.isLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                    size: 20\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 329,\n                    columnNumber: 63\n                  }, this) : /*#__PURE__*/_jsxDEV(Add, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 329,\n                    columnNumber: 96\n                  }, this),\n                  disabled: addHoldingMutation.isLoading,\n                  children: addHoldingMutation.isLoading ? 'Adding...' : 'Add Holding'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 326,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mt: 3,\n          bgcolor: 'background.default'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"About Holdings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: \"Holdings represent your investments in specific assets. Once added, you can:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          component: \"ul\",\n          sx: {\n            mt: 1,\n            pl: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            component: \"li\",\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Track performance and current value\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            component: \"li\",\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Record additional transactions (buy/sell)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            component: \"li\",\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"View detailed analytics and charts\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            component: \"li\",\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Monitor portfolio allocation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(AddHoldingPage, \"4axtRVPKJyx5HGDSUDtT0Wbp2fY=\", false, function () {\n  return [useNavigate, useParams, useQueryClient, useQuery, useQuery, useMutation];\n});\n_c = AddHoldingPage;\nexport default AddHoldingPage;\nvar _c;\n$RefreshReg$(_c, \"AddHoldingPage\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Paper", "TextField", "<PERSON><PERSON>", "Autocomplete", "<PERSON><PERSON>", "CircularProgress", "Grid", "InputAdornment", "ArrowBack", "Add", "<PERSON><PERSON><PERSON>", "useNavigate", "useParams", "useMutation", "useQuery", "useQueryClient", "toast", "apiService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AddHoldingPage", "_s", "navigate", "id", "portfolioId", "queryClient", "formData", "setFormData", "asset_id", "quantity", "purchase_price", "purchase_date", "Date", "toISOString", "split", "notes", "selectedAsset", "setSelectedAsset", "errors", "setErrors", "data", "assets", "isLoading", "assetsLoading", "getAssets", "portfolio", "getPortfolio", "enabled", "addHoldingMutation", "Error", "createHolding", "onSuccess", "success", "invalidateQueries", "onError", "error", "_error$response", "console", "response", "handleInputChange", "field", "event", "value", "target", "type", "parseFloat", "prev", "Object", "prototype", "hasOwnProperty", "call", "handleAssetChange", "newValue", "handleSubmit", "preventDefault", "newErrors", "keys", "length", "mutate", "calculateTotalValue", "sx", "max<PERSON><PERSON><PERSON>", "mx", "p", "children", "variant", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "content", "display", "alignItems", "mb", "startIcon", "onClick", "mr", "component", "onSubmit", "container", "spacing", "item", "xs", "options", "getOptionLabel", "option", "symbol", "onChange", "loading", "renderInput", "params", "label", "helperText", "required", "InputProps", "endAdornment", "size", "sm", "fullWidth", "inputProps", "min", "step", "startAdornment", "position", "InputLabelProps", "shrink", "toFixed", "disabled", "readOnly", "multiline", "rows", "placeholder", "severity", "justifyContent", "gap", "mt", "bgcolor", "gutterBottom", "pl", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/TrustVault/frontend/src/pages/Portfolio/AddHoldingPage.tsx"], "sourcesContent": ["// TrustVault - Add Holding Page\n\nimport React, { useState } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  TextField,\n  Button,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Autocomplete,\n  Alert,\n  CircularProgress,\n  Grid,\n  InputAdornment,\n} from '@mui/material';\nimport { ArrowBack, Add } from '@mui/icons-material';\nimport { Helmet } from 'react-helmet-async';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { useMutation, useQuery, useQueryClient } from 'react-query';\nimport { toast } from 'react-hot-toast';\n\n// Services\nimport apiService from '../../services/api';\n\n// Types\ninterface AddHoldingForm {\n  asset_id: string;\n  quantity: number;\n  purchase_price: number;\n  purchase_date: string;\n  notes?: string;\n}\n\nconst AddHoldingPage: React.FC = () => {\n  const navigate = useNavigate();\n  const { id: portfolioId } = useParams<{ id: string }>();\n  const queryClient = useQueryClient();\n\n  const [formData, setFormData] = useState<AddHoldingForm>({\n    asset_id: '',\n    quantity: 0,\n    purchase_price: 0,\n    purchase_date: new Date().toISOString().split('T')[0],\n    notes: '',\n  });\n\n  const [selectedAsset, setSelectedAsset] = useState<any>(null);\n  const [errors, setErrors] = useState<Record<string, string>>({});\n\n  // Fetch available assets\n  const { data: assets, isLoading: assetsLoading } = useQuery(\n    'assets',\n    apiService.getAssets\n  );\n\n  // Fetch portfolio details\n  const { data: portfolio } = useQuery(\n    ['portfolio', portfolioId],\n    () => apiService.getPortfolio(portfolioId!),\n    {\n      enabled: !!portfolioId,\n    }\n  );\n\n  const addHoldingMutation = useMutation(\n    (data: AddHoldingForm) => {\n      if (!portfolioId) throw new Error('Portfolio ID is required');\n      return apiService.createHolding(portfolioId, data);\n    },\n    {\n      onSuccess: () => {\n        toast.success('Holding added successfully!');\n        queryClient.invalidateQueries(['portfolio', portfolioId]);\n        queryClient.invalidateQueries(['holdings', portfolioId]);\n        navigate(`/portfolios/${portfolioId}`);\n      },\n      onError: (error: any) => {\n        console.error('Add holding error:', error);\n        if (error.response?.data) {\n          setErrors(error.response.data);\n        } else {\n          toast.error('Failed to add holding. Please try again.');\n        }\n      },\n    }\n  );\n\n  const handleInputChange = (field: keyof AddHoldingForm) => (\n    event: React.ChangeEvent<HTMLInputElement>\n  ) => {\n    const value = event.target.type === 'number' ? parseFloat(event.target.value) || 0 : event.target.value;\n    setFormData(prev => ({\n      ...prev,\n      [field]: value,\n    }));\n    \n    // Clear error when user starts typing\n    if (Object.prototype.hasOwnProperty.call(errors, field)) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: '',\n      }));\n    }\n  };\n\n  const handleAssetChange = (event: any, newValue: any) => {\n    setSelectedAsset(newValue);\n    setFormData(prev => ({\n      ...prev,\n      asset_id: newValue?.id || '',\n    }));\n    \n    if (errors.asset_id) {\n      setErrors(prev => ({\n        ...prev,\n        asset_id: '',\n      }));\n    }\n  };\n\n  const handleSubmit = (event: React.FormEvent) => {\n    event.preventDefault();\n    \n    // Basic validation\n    const newErrors: Record<string, string> = {};\n    \n    if (!formData.asset_id) {\n      newErrors.asset_id = 'Please select an asset';\n    }\n    \n    if (formData.quantity <= 0) {\n      newErrors.quantity = 'Quantity must be greater than 0';\n    }\n    \n    if (formData.purchase_price <= 0) {\n      newErrors.purchase_price = 'Purchase price must be greater than 0';\n    }\n    \n    if (!formData.purchase_date) {\n      newErrors.purchase_date = 'Purchase date is required';\n    }\n    \n    if (Object.keys(newErrors).length > 0) {\n      setErrors(newErrors);\n      return;\n    }\n    \n    addHoldingMutation.mutate(formData);\n  };\n\n  const calculateTotalValue = () => {\n    return formData.quantity * formData.purchase_price;\n  };\n\n  if (!portfolioId) {\n    return (\n      <Box sx={{ maxWidth: 800, mx: 'auto', p: 3 }}>\n        <Typography variant=\"h6\" color=\"error\">\n          Portfolio ID is required\n        </Typography>\n      </Box>\n    );\n  }\n\n  return (\n    <>\n      <Helmet>\n        <title>Add Holding - TrustVault</title>\n        <meta name=\"description\" content=\"Add a new holding to your portfolio\" />\n      </Helmet>\n\n      <Box sx={{ maxWidth: 800, mx: 'auto', p: 3 }}>\n        {/* Header */}\n        <Box display=\"flex\" alignItems=\"center\" mb={3}>\n          <Button\n            startIcon={<ArrowBack />}\n            onClick={() => navigate(`/portfolios/${portfolioId}`)}\n            sx={{ mr: 2 }}\n          >\n            Back to Portfolio\n          </Button>\n          <Box>\n            <Typography variant=\"h4\" component=\"h1\">\n              Add New Holding\n            </Typography>\n            {portfolio && (\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                to {portfolio.name}\n              </Typography>\n            )}\n          </Box>\n        </Box>\n\n        {/* Form */}\n        <Paper sx={{ p: 4 }}>\n          <form onSubmit={handleSubmit}>\n            <Grid container spacing={3}>\n              {/* Asset Selection */}\n              <Grid item xs={12}>\n                <Autocomplete\n                  options={assets || []}\n                  getOptionLabel={(option) => `${option.symbol} - ${option.name}`}\n                  value={selectedAsset}\n                  onChange={handleAssetChange}\n                  loading={assetsLoading}\n                  renderInput={(params) => (\n                    <TextField\n                      {...params}\n                      label=\"Select Asset\"\n                      error={!!errors.asset_id}\n                      helperText={errors.asset_id}\n                      required\n                      InputProps={{\n                        ...params.InputProps,\n                        endAdornment: (\n                          <>\n                            {assetsLoading ? <CircularProgress color=\"inherit\" size={20} /> : null}\n                            {params.InputProps.endAdornment}\n                          </>\n                        ),\n                      }}\n                    />\n                  )}\n                />\n              </Grid>\n\n              {/* Quantity */}\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  label=\"Quantity\"\n                  type=\"number\"\n                  value={formData.quantity || ''}\n                  onChange={handleInputChange('quantity')}\n                  error={!!errors.quantity}\n                  helperText={errors.quantity}\n                  required\n                  fullWidth\n                  inputProps={{ min: 0, step: 0.01 }}\n                />\n              </Grid>\n\n              {/* Purchase Price */}\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  label=\"Purchase Price\"\n                  type=\"number\"\n                  value={formData.purchase_price || ''}\n                  onChange={handleInputChange('purchase_price')}\n                  error={!!errors.purchase_price}\n                  helperText={errors.purchase_price}\n                  required\n                  fullWidth\n                  InputProps={{\n                    startAdornment: <InputAdornment position=\"start\">$</InputAdornment>,\n                  }}\n                  inputProps={{ min: 0, step: 0.01 }}\n                />\n              </Grid>\n\n              {/* Purchase Date */}\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  label=\"Purchase Date\"\n                  type=\"date\"\n                  value={formData.purchase_date}\n                  onChange={handleInputChange('purchase_date')}\n                  error={!!errors.purchase_date}\n                  helperText={errors.purchase_date}\n                  required\n                  fullWidth\n                  InputLabelProps={{\n                    shrink: true,\n                  }}\n                />\n              </Grid>\n\n              {/* Total Value Display */}\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  label=\"Total Value\"\n                  value={`$${calculateTotalValue().toFixed(2)}`}\n                  fullWidth\n                  disabled\n                  InputProps={{\n                    readOnly: true,\n                  }}\n                />\n              </Grid>\n\n              {/* Notes */}\n              <Grid item xs={12}>\n                <TextField\n                  label=\"Notes (Optional)\"\n                  value={formData.notes}\n                  onChange={handleInputChange('notes')}\n                  multiline\n                  rows={3}\n                  fullWidth\n                  placeholder=\"Add any notes about this holding...\"\n                />\n              </Grid>\n\n              {/* Error Display */}\n              {Object.keys(errors).length > 0 && (\n                <Grid item xs={12}>\n                  <Alert severity=\"error\">\n                    Please fix the errors above and try again.\n                  </Alert>\n                </Grid>\n              )}\n\n              {/* Submit Buttons */}\n              <Grid item xs={12}>\n                <Box display=\"flex\" justifyContent=\"flex-end\" gap={2} mt={2}>\n                  <Button\n                    variant=\"outlined\"\n                    onClick={() => navigate(`/portfolios/${portfolioId}`)}\n                    disabled={addHoldingMutation.isLoading}\n                  >\n                    Cancel\n                  </Button>\n                  <Button\n                    type=\"submit\"\n                    variant=\"contained\"\n                    startIcon={addHoldingMutation.isLoading ? <CircularProgress size={20} /> : <Add />}\n                    disabled={addHoldingMutation.isLoading}\n                  >\n                    {addHoldingMutation.isLoading ? 'Adding...' : 'Add Holding'}\n                  </Button>\n                </Box>\n              </Grid>\n            </Grid>\n          </form>\n        </Paper>\n\n        {/* Info Box */}\n        <Paper sx={{ p: 3, mt: 3, bgcolor: 'background.default' }}>\n          <Typography variant=\"h6\" gutterBottom>\n            About Holdings\n          </Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            Holdings represent your investments in specific assets. Once added, you can:\n          </Typography>\n          <Box component=\"ul\" sx={{ mt: 1, pl: 2 }}>\n            <Typography component=\"li\" variant=\"body2\" color=\"text.secondary\">\n              Track performance and current value\n            </Typography>\n            <Typography component=\"li\" variant=\"body2\" color=\"text.secondary\">\n              Record additional transactions (buy/sell)\n            </Typography>\n            <Typography component=\"li\" variant=\"body2\" color=\"text.secondary\">\n              View detailed analytics and charts\n            </Typography>\n            <Typography component=\"li\" variant=\"body2\" color=\"text.secondary\">\n              Monitor portfolio allocation\n            </Typography>\n          </Box>\n        </Paper>\n      </Box>\n    </>\n  );\n};\n\nexport default AddHoldingPage;\n"], "mappings": ";;AAAA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,SAAS,EACTC,MAAM,EAKNC,YAAY,EACZC,KAAK,EACLC,gBAAgB,EAChBC,IAAI,EACJC,cAAc,QACT,eAAe;AACtB,SAASC,SAAS,EAAEC,GAAG,QAAQ,qBAAqB;AACpD,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACzD,SAASC,WAAW,EAAEC,QAAQ,EAAEC,cAAc,QAAQ,aAAa;AACnE,SAASC,KAAK,QAAQ,iBAAiB;;AAEvC;AACA,OAAOC,UAAU,MAAM,oBAAoB;;AAE3C;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AASA,MAAMC,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAMC,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEc,EAAE,EAAEC;EAAY,CAAC,GAAGd,SAAS,CAAiB,CAAC;EACvD,MAAMe,WAAW,GAAGZ,cAAc,CAAC,CAAC;EAEpC,MAAM,CAACa,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAAiB;IACvDiC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,CAAC;IACXC,cAAc,EAAE,CAAC;IACjBC,aAAa,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACrDC,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG1C,QAAQ,CAAM,IAAI,CAAC;EAC7D,MAAM,CAAC2C,MAAM,EAAEC,SAAS,CAAC,GAAG5C,QAAQ,CAAyB,CAAC,CAAC,CAAC;;EAEhE;EACA,MAAM;IAAE6C,IAAI,EAAEC,MAAM;IAAEC,SAAS,EAAEC;EAAc,CAAC,GAAG/B,QAAQ,CACzD,QAAQ,EACRG,UAAU,CAAC6B,SACb,CAAC;;EAED;EACA,MAAM;IAAEJ,IAAI,EAAEK;EAAU,CAAC,GAAGjC,QAAQ,CAClC,CAAC,WAAW,EAAEY,WAAW,CAAC,EAC1B,MAAMT,UAAU,CAAC+B,YAAY,CAACtB,WAAY,CAAC,EAC3C;IACEuB,OAAO,EAAE,CAAC,CAACvB;EACb,CACF,CAAC;EAED,MAAMwB,kBAAkB,GAAGrC,WAAW,CACnC6B,IAAoB,IAAK;IACxB,IAAI,CAAChB,WAAW,EAAE,MAAM,IAAIyB,KAAK,CAAC,0BAA0B,CAAC;IAC7D,OAAOlC,UAAU,CAACmC,aAAa,CAAC1B,WAAW,EAAEgB,IAAI,CAAC;EACpD,CAAC,EACD;IACEW,SAAS,EAAEA,CAAA,KAAM;MACfrC,KAAK,CAACsC,OAAO,CAAC,6BAA6B,CAAC;MAC5C3B,WAAW,CAAC4B,iBAAiB,CAAC,CAAC,WAAW,EAAE7B,WAAW,CAAC,CAAC;MACzDC,WAAW,CAAC4B,iBAAiB,CAAC,CAAC,UAAU,EAAE7B,WAAW,CAAC,CAAC;MACxDF,QAAQ,CAAC,eAAeE,WAAW,EAAE,CAAC;IACxC,CAAC;IACD8B,OAAO,EAAGC,KAAU,IAAK;MAAA,IAAAC,eAAA;MACvBC,OAAO,CAACF,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,KAAAC,eAAA,GAAID,KAAK,CAACG,QAAQ,cAAAF,eAAA,eAAdA,eAAA,CAAgBhB,IAAI,EAAE;QACxBD,SAAS,CAACgB,KAAK,CAACG,QAAQ,CAAClB,IAAI,CAAC;MAChC,CAAC,MAAM;QACL1B,KAAK,CAACyC,KAAK,CAAC,0CAA0C,CAAC;MACzD;IACF;EACF,CACF,CAAC;EAED,MAAMI,iBAAiB,GAAIC,KAA2B,IACpDC,KAA0C,IACvC;IACH,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAAM,CAACC,IAAI,KAAK,QAAQ,GAAGC,UAAU,CAACJ,KAAK,CAACE,MAAM,CAACD,KAAK,CAAC,IAAI,CAAC,GAAGD,KAAK,CAACE,MAAM,CAACD,KAAK;IACvGnC,WAAW,CAACuC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACN,KAAK,GAAGE;IACX,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIK,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAAChC,MAAM,EAAEsB,KAAK,CAAC,EAAE;MACvDrB,SAAS,CAAC2B,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACN,KAAK,GAAG;MACX,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMW,iBAAiB,GAAGA,CAACV,KAAU,EAAEW,QAAa,KAAK;IACvDnC,gBAAgB,CAACmC,QAAQ,CAAC;IAC1B7C,WAAW,CAACuC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPtC,QAAQ,EAAE,CAAA4C,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEjD,EAAE,KAAI;IAC5B,CAAC,CAAC,CAAC;IAEH,IAAIe,MAAM,CAACV,QAAQ,EAAE;MACnBW,SAAS,CAAC2B,IAAI,KAAK;QACjB,GAAGA,IAAI;QACPtC,QAAQ,EAAE;MACZ,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAM6C,YAAY,GAAIZ,KAAsB,IAAK;IAC/CA,KAAK,CAACa,cAAc,CAAC,CAAC;;IAEtB;IACA,MAAMC,SAAiC,GAAG,CAAC,CAAC;IAE5C,IAAI,CAACjD,QAAQ,CAACE,QAAQ,EAAE;MACtB+C,SAAS,CAAC/C,QAAQ,GAAG,wBAAwB;IAC/C;IAEA,IAAIF,QAAQ,CAACG,QAAQ,IAAI,CAAC,EAAE;MAC1B8C,SAAS,CAAC9C,QAAQ,GAAG,iCAAiC;IACxD;IAEA,IAAIH,QAAQ,CAACI,cAAc,IAAI,CAAC,EAAE;MAChC6C,SAAS,CAAC7C,cAAc,GAAG,uCAAuC;IACpE;IAEA,IAAI,CAACJ,QAAQ,CAACK,aAAa,EAAE;MAC3B4C,SAAS,CAAC5C,aAAa,GAAG,2BAA2B;IACvD;IAEA,IAAIoC,MAAM,CAACS,IAAI,CAACD,SAAS,CAAC,CAACE,MAAM,GAAG,CAAC,EAAE;MACrCtC,SAAS,CAACoC,SAAS,CAAC;MACpB;IACF;IAEA3B,kBAAkB,CAAC8B,MAAM,CAACpD,QAAQ,CAAC;EACrC,CAAC;EAED,MAAMqD,mBAAmB,GAAGA,CAAA,KAAM;IAChC,OAAOrD,QAAQ,CAACG,QAAQ,GAAGH,QAAQ,CAACI,cAAc;EACpD,CAAC;EAED,IAAI,CAACN,WAAW,EAAE;IAChB,oBACEP,OAAA,CAACrB,GAAG;MAACoF,EAAE,EAAE;QAAEC,QAAQ,EAAE,GAAG;QAAEC,EAAE,EAAE,MAAM;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAC,QAAA,eAC3CnE,OAAA,CAACpB,UAAU;QAACwF,OAAO,EAAC,IAAI;QAACC,KAAK,EAAC,OAAO;QAAAF,QAAA,EAAC;MAEvC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV;EAEA,oBACEzE,OAAA,CAAAE,SAAA;IAAAiE,QAAA,gBACEnE,OAAA,CAACT,MAAM;MAAA4E,QAAA,gBACLnE,OAAA;QAAAmE,QAAA,EAAO;MAAwB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACvCzE,OAAA;QAAM0E,IAAI,EAAC,aAAa;QAACC,OAAO,EAAC;MAAqC;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnE,CAAC,eAETzE,OAAA,CAACrB,GAAG;MAACoF,EAAE,EAAE;QAAEC,QAAQ,EAAE,GAAG;QAAEC,EAAE,EAAE,MAAM;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAC,QAAA,gBAE3CnE,OAAA,CAACrB,GAAG;QAACiG,OAAO,EAAC,MAAM;QAACC,UAAU,EAAC,QAAQ;QAACC,EAAE,EAAE,CAAE;QAAAX,QAAA,gBAC5CnE,OAAA,CAACjB,MAAM;UACLgG,SAAS,eAAE/E,OAAA,CAACX,SAAS;YAAAiF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBO,OAAO,EAAEA,CAAA,KAAM3E,QAAQ,CAAC,eAAeE,WAAW,EAAE,CAAE;UACtDwD,EAAE,EAAE;YAAEkB,EAAE,EAAE;UAAE,CAAE;UAAAd,QAAA,EACf;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTzE,OAAA,CAACrB,GAAG;UAAAwF,QAAA,gBACFnE,OAAA,CAACpB,UAAU;YAACwF,OAAO,EAAC,IAAI;YAACc,SAAS,EAAC,IAAI;YAAAf,QAAA,EAAC;UAExC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZ7C,SAAS,iBACR5B,OAAA,CAACpB,UAAU;YAACwF,OAAO,EAAC,OAAO;YAACC,KAAK,EAAC,gBAAgB;YAAAF,QAAA,GAAC,KAC9C,EAACvC,SAAS,CAAC8C,IAAI;UAAA;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CACb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNzE,OAAA,CAACnB,KAAK;QAACkF,EAAE,EAAE;UAAEG,CAAC,EAAE;QAAE,CAAE;QAAAC,QAAA,eAClBnE,OAAA;UAAMmF,QAAQ,EAAE3B,YAAa;UAAAW,QAAA,eAC3BnE,OAAA,CAACb,IAAI;YAACiG,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAlB,QAAA,gBAEzBnE,OAAA,CAACb,IAAI;cAACmG,IAAI;cAACC,EAAE,EAAE,EAAG;cAAApB,QAAA,eAChBnE,OAAA,CAAChB,YAAY;gBACXwG,OAAO,EAAEhE,MAAM,IAAI,EAAG;gBACtBiE,cAAc,EAAGC,MAAM,IAAK,GAAGA,MAAM,CAACC,MAAM,MAAMD,MAAM,CAAChB,IAAI,EAAG;gBAChE7B,KAAK,EAAE1B,aAAc;gBACrByE,QAAQ,EAAEtC,iBAAkB;gBAC5BuC,OAAO,EAAEnE,aAAc;gBACvBoE,WAAW,EAAGC,MAAM,iBAClB/F,OAAA,CAAClB,SAAS;kBAAA,GACJiH,MAAM;kBACVC,KAAK,EAAC,cAAc;kBACpB1D,KAAK,EAAE,CAAC,CAACjB,MAAM,CAACV,QAAS;kBACzBsF,UAAU,EAAE5E,MAAM,CAACV,QAAS;kBAC5BuF,QAAQ;kBACRC,UAAU,EAAE;oBACV,GAAGJ,MAAM,CAACI,UAAU;oBACpBC,YAAY,eACVpG,OAAA,CAAAE,SAAA;sBAAAiE,QAAA,GACGzC,aAAa,gBAAG1B,OAAA,CAACd,gBAAgB;wBAACmF,KAAK,EAAC,SAAS;wBAACgC,IAAI,EAAE;sBAAG;wBAAA/B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,GAAG,IAAI,EACrEsB,MAAM,CAACI,UAAU,CAACC,YAAY;oBAAA,eAC/B;kBAEN;gBAAE;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cACD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGPzE,OAAA,CAACb,IAAI;cAACmG,IAAI;cAACC,EAAE,EAAE,EAAG;cAACe,EAAE,EAAE,CAAE;cAAAnC,QAAA,eACvBnE,OAAA,CAAClB,SAAS;gBACRkH,KAAK,EAAC,UAAU;gBAChBjD,IAAI,EAAC,QAAQ;gBACbF,KAAK,EAAEpC,QAAQ,CAACG,QAAQ,IAAI,EAAG;gBAC/BgF,QAAQ,EAAElD,iBAAiB,CAAC,UAAU,CAAE;gBACxCJ,KAAK,EAAE,CAAC,CAACjB,MAAM,CAACT,QAAS;gBACzBqF,UAAU,EAAE5E,MAAM,CAACT,QAAS;gBAC5BsF,QAAQ;gBACRK,SAAS;gBACTC,UAAU,EAAE;kBAAEC,GAAG,EAAE,CAAC;kBAAEC,IAAI,EAAE;gBAAK;cAAE;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGPzE,OAAA,CAACb,IAAI;cAACmG,IAAI;cAACC,EAAE,EAAE,EAAG;cAACe,EAAE,EAAE,CAAE;cAAAnC,QAAA,eACvBnE,OAAA,CAAClB,SAAS;gBACRkH,KAAK,EAAC,gBAAgB;gBACtBjD,IAAI,EAAC,QAAQ;gBACbF,KAAK,EAAEpC,QAAQ,CAACI,cAAc,IAAI,EAAG;gBACrC+E,QAAQ,EAAElD,iBAAiB,CAAC,gBAAgB,CAAE;gBAC9CJ,KAAK,EAAE,CAAC,CAACjB,MAAM,CAACR,cAAe;gBAC/BoF,UAAU,EAAE5E,MAAM,CAACR,cAAe;gBAClCqF,QAAQ;gBACRK,SAAS;gBACTJ,UAAU,EAAE;kBACVQ,cAAc,eAAE3G,OAAA,CAACZ,cAAc;oBAACwH,QAAQ,EAAC,OAAO;oBAAAzC,QAAA,EAAC;kBAAC;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAgB;gBACpE,CAAE;gBACF+B,UAAU,EAAE;kBAAEC,GAAG,EAAE,CAAC;kBAAEC,IAAI,EAAE;gBAAK;cAAE;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGPzE,OAAA,CAACb,IAAI;cAACmG,IAAI;cAACC,EAAE,EAAE,EAAG;cAACe,EAAE,EAAE,CAAE;cAAAnC,QAAA,eACvBnE,OAAA,CAAClB,SAAS;gBACRkH,KAAK,EAAC,eAAe;gBACrBjD,IAAI,EAAC,MAAM;gBACXF,KAAK,EAAEpC,QAAQ,CAACK,aAAc;gBAC9B8E,QAAQ,EAAElD,iBAAiB,CAAC,eAAe,CAAE;gBAC7CJ,KAAK,EAAE,CAAC,CAACjB,MAAM,CAACP,aAAc;gBAC9BmF,UAAU,EAAE5E,MAAM,CAACP,aAAc;gBACjCoF,QAAQ;gBACRK,SAAS;gBACTM,eAAe,EAAE;kBACfC,MAAM,EAAE;gBACV;cAAE;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGPzE,OAAA,CAACb,IAAI;cAACmG,IAAI;cAACC,EAAE,EAAE,EAAG;cAACe,EAAE,EAAE,CAAE;cAAAnC,QAAA,eACvBnE,OAAA,CAAClB,SAAS;gBACRkH,KAAK,EAAC,aAAa;gBACnBnD,KAAK,EAAE,IAAIiB,mBAAmB,CAAC,CAAC,CAACiD,OAAO,CAAC,CAAC,CAAC,EAAG;gBAC9CR,SAAS;gBACTS,QAAQ;gBACRb,UAAU,EAAE;kBACVc,QAAQ,EAAE;gBACZ;cAAE;gBAAA3C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGPzE,OAAA,CAACb,IAAI;cAACmG,IAAI;cAACC,EAAE,EAAE,EAAG;cAAApB,QAAA,eAChBnE,OAAA,CAAClB,SAAS;gBACRkH,KAAK,EAAC,kBAAkB;gBACxBnD,KAAK,EAAEpC,QAAQ,CAACS,KAAM;gBACtB0E,QAAQ,EAAElD,iBAAiB,CAAC,OAAO,CAAE;gBACrCwE,SAAS;gBACTC,IAAI,EAAE,CAAE;gBACRZ,SAAS;gBACTa,WAAW,EAAC;cAAqC;gBAAA9C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EAGNvB,MAAM,CAACS,IAAI,CAACtC,MAAM,CAAC,CAACuC,MAAM,GAAG,CAAC,iBAC7B5D,OAAA,CAACb,IAAI;cAACmG,IAAI;cAACC,EAAE,EAAE,EAAG;cAAApB,QAAA,eAChBnE,OAAA,CAACf,KAAK;gBAACoI,QAAQ,EAAC,OAAO;gBAAAlD,QAAA,EAAC;cAExB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACP,eAGDzE,OAAA,CAACb,IAAI;cAACmG,IAAI;cAACC,EAAE,EAAE,EAAG;cAAApB,QAAA,eAChBnE,OAAA,CAACrB,GAAG;gBAACiG,OAAO,EAAC,MAAM;gBAAC0C,cAAc,EAAC,UAAU;gBAACC,GAAG,EAAE,CAAE;gBAACC,EAAE,EAAE,CAAE;gBAAArD,QAAA,gBAC1DnE,OAAA,CAACjB,MAAM;kBACLqF,OAAO,EAAC,UAAU;kBAClBY,OAAO,EAAEA,CAAA,KAAM3E,QAAQ,CAAC,eAAeE,WAAW,EAAE,CAAE;kBACtDyG,QAAQ,EAAEjF,kBAAkB,CAACN,SAAU;kBAAA0C,QAAA,EACxC;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTzE,OAAA,CAACjB,MAAM;kBACLgE,IAAI,EAAC,QAAQ;kBACbqB,OAAO,EAAC,WAAW;kBACnBW,SAAS,EAAEhD,kBAAkB,CAACN,SAAS,gBAAGzB,OAAA,CAACd,gBAAgB;oBAACmH,IAAI,EAAE;kBAAG;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGzE,OAAA,CAACV,GAAG;oBAAAgF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACnFuC,QAAQ,EAAEjF,kBAAkB,CAACN,SAAU;kBAAA0C,QAAA,EAEtCpC,kBAAkB,CAACN,SAAS,GAAG,WAAW,GAAG;gBAAa;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGRzE,OAAA,CAACnB,KAAK;QAACkF,EAAE,EAAE;UAAEG,CAAC,EAAE,CAAC;UAAEsD,EAAE,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAqB,CAAE;QAAAtD,QAAA,gBACxDnE,OAAA,CAACpB,UAAU;UAACwF,OAAO,EAAC,IAAI;UAACsD,YAAY;UAAAvD,QAAA,EAAC;QAEtC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbzE,OAAA,CAACpB,UAAU;UAACwF,OAAO,EAAC,OAAO;UAACC,KAAK,EAAC,gBAAgB;UAAAF,QAAA,EAAC;QAEnD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbzE,OAAA,CAACrB,GAAG;UAACuG,SAAS,EAAC,IAAI;UAACnB,EAAE,EAAE;YAAEyD,EAAE,EAAE,CAAC;YAAEG,EAAE,EAAE;UAAE,CAAE;UAAAxD,QAAA,gBACvCnE,OAAA,CAACpB,UAAU;YAACsG,SAAS,EAAC,IAAI;YAACd,OAAO,EAAC,OAAO;YAACC,KAAK,EAAC,gBAAgB;YAAAF,QAAA,EAAC;UAElE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbzE,OAAA,CAACpB,UAAU;YAACsG,SAAS,EAAC,IAAI;YAACd,OAAO,EAAC,OAAO;YAACC,KAAK,EAAC,gBAAgB;YAAAF,QAAA,EAAC;UAElE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbzE,OAAA,CAACpB,UAAU;YAACsG,SAAS,EAAC,IAAI;YAACd,OAAO,EAAC,OAAO;YAACC,KAAK,EAAC,gBAAgB;YAAAF,QAAA,EAAC;UAElE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbzE,OAAA,CAACpB,UAAU;YAACsG,SAAS,EAAC,IAAI;YAACd,OAAO,EAAC,OAAO;YAACC,KAAK,EAAC,gBAAgB;YAAAF,QAAA,EAAC;UAElE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACrE,EAAA,CAxUID,cAAwB;EAAA,QACXX,WAAW,EACAC,SAAS,EACjBG,cAAc,EAciBD,QAAQ,EAM/BA,QAAQ,EAQTD,WAAW;AAAA;AAAAkI,EAAA,GA/BlCzH,cAAwB;AA0U9B,eAAeA,cAAc;AAAC,IAAAyH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}