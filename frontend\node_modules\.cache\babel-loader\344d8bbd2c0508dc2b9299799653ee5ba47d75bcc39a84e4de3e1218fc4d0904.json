{"ast": null, "code": "// TrustVault - Security Utilities\n\nimport CryptoJS from 'crypto-js';\n\n/**\n * Validate environment security settings\n */\nexport const validateEnvironment = () => {\n  // Check if running in development\n  if (process.env.NODE_ENV === 'development') {\n    console.warn('🔒 Running in development mode - security features may be relaxed');\n  }\n\n  // Validate required environment variables\n  const requiredEnvVars = ['REACT_APP_API_URL'];\n  const missingVars = requiredEnvVars.filter(varName => {\n    const envValue = varName === 'REACT_APP_API_URL' ? process.env.REACT_APP_API_URL : undefined;\n    return !envValue;\n  });\n  if (missingVars.length > 0) {\n    console.error('❌ Missing required environment variables:', missingVars);\n  }\n\n  // Check for HTTPS in production\n  if (process.env.NODE_ENV === 'production' && window.location.protocol !== 'https:') {\n    console.warn('⚠️ Application should be served over HTTPS in production');\n  }\n};\n\n/**\n * Sanitize user input to prevent XSS\n */\nexport const sanitizeInput = input => {\n  const div = document.createElement('div');\n  div.textContent = input;\n  return div.innerHTML;\n};\n\n/**\n * Validate email format\n */\nexport const isValidEmail = email => {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n};\n\n/**\n * Validate password strength\n */\nexport const validatePasswordStrength = password => {\n  const errors = [];\n  let score = 0;\n\n  // Length check\n  if (password.length < 12) {\n    errors.push('Password must be at least 12 characters long');\n  } else {\n    score += 1;\n  }\n\n  // Uppercase check\n  if (!/[A-Z]/.test(password)) {\n    errors.push('Password must contain at least one uppercase letter');\n  } else {\n    score += 1;\n  }\n\n  // Lowercase check\n  if (!/[a-z]/.test(password)) {\n    errors.push('Password must contain at least one lowercase letter');\n  } else {\n    score += 1;\n  }\n\n  // Number check\n  if (!/\\d/.test(password)) {\n    errors.push('Password must contain at least one number');\n  } else {\n    score += 1;\n  }\n\n  // Special character check\n  if (!/[!@#$%^&*(),.?\":{}|<>]/.test(password)) {\n    errors.push('Password must contain at least one special character');\n  } else {\n    score += 1;\n  }\n\n  // Common patterns check\n  const commonPatterns = [/123456/, /password/i, /qwerty/i, /abc123/i, /admin/i, /letmein/i, /welcome/i];\n  if (commonPatterns.some(pattern => pattern.test(password))) {\n    errors.push('Password contains common patterns that are not allowed');\n    score -= 1;\n  }\n\n  // Sequential characters check\n  if (hasSequentialChars(password)) {\n    errors.push('Password cannot contain sequential characters');\n    score -= 1;\n  }\n  return {\n    isValid: errors.length === 0,\n    errors,\n    score: Math.max(0, score)\n  };\n};\n\n/**\n * Check for sequential characters in password\n */\nconst hasSequentialChars = password => {\n  const sequences = ['abcdefghijklmnopqrstuvwxyz', '0123456789', 'qwertyuiopasdfghjklzxcvbnm' // keyboard layout\n  ];\n  const passwordLower = password.toLowerCase();\n  for (const sequence of sequences) {\n    for (let i = 0; i <= sequence.length - 3; i++) {\n      const subseq = sequence.substring(i, i + 3);\n      const reverseSubseq = subseq.split('').reverse().join('');\n      if (passwordLower.includes(subseq) || passwordLower.includes(reverseSubseq)) {\n        return true;\n      }\n    }\n  }\n  return false;\n};\n\n/**\n * Generate secure random string\n */\nexport const generateSecureRandom = (length = 32) => {\n  const array = new Uint8Array(length);\n  window.crypto.getRandomValues(array);\n  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');\n};\n\n/**\n * Hash sensitive data for client-side storage\n */\nexport const hashData = (data, salt) => {\n  const saltToUse = salt || generateSecureRandom(16);\n  return CryptoJS.SHA256(data + saltToUse).toString();\n};\n\n/**\n * Encrypt sensitive data for client-side storage\n */\nexport const encryptData = (data, key) => {\n  return CryptoJS.AES.encrypt(data, key).toString();\n};\n\n/**\n * Decrypt sensitive data from client-side storage\n */\nexport const decryptData = (encryptedData, key) => {\n  const bytes = CryptoJS.AES.decrypt(encryptedData, key);\n  return bytes.toString(CryptoJS.enc.Utf8);\n};\n\n/**\n * Secure localStorage wrapper\n */\nexport const secureStorage = {\n  setItem: (key, value, encrypt = false) => {\n    try {\n      const dataToStore = encrypt ? encryptData(value, key) : value;\n      localStorage.setItem(key, dataToStore);\n    } catch (error) {\n      console.error('Failed to store data securely:', error);\n    }\n  },\n  getItem: (key, decrypt = false) => {\n    try {\n      const data = localStorage.getItem(key);\n      if (!data) return null;\n      return decrypt ? decryptData(data, key) : data;\n    } catch (error) {\n      console.error('Failed to retrieve data securely:', error);\n      return null;\n    }\n  },\n  removeItem: key => {\n    localStorage.removeItem(key);\n  },\n  clear: () => {\n    localStorage.clear();\n  }\n};\n\n/**\n * Detect potential security threats in user input\n */\nexport const detectSecurityThreats = input => {\n  const threats = [];\n\n  // XSS patterns\n  const xssPatterns = [/<script[^>]*>.*?<\\/script>/gi, /javascript:/gi, /vbscript:/gi, /on\\w+\\s*=/gi, /eval\\s*\\(/gi, /document\\.cookie/gi, /alert\\s*\\(/gi];\n\n  // SQL injection patterns\n  const sqlPatterns = [/union\\s+select/gi, /or\\s+1\\s*=\\s*1/gi, /drop\\s+table/gi, /insert\\s+into/gi, /delete\\s+from/gi, /update\\s+.*set/gi];\n\n  // Check for XSS\n  if (xssPatterns.some(pattern => pattern.test(input))) {\n    threats.push('Potential XSS attack detected');\n  }\n\n  // Check for SQL injection\n  if (sqlPatterns.some(pattern => pattern.test(input))) {\n    threats.push('Potential SQL injection detected');\n  }\n  return {\n    isSafe: threats.length === 0,\n    threats\n  };\n};\n\n/**\n * Rate limiting utility\n */\nexport const createRateLimiter = (maxRequests, windowMs) => {\n  const requests = [];\n  return {\n    isAllowed: () => {\n      const now = Date.now();\n\n      // Remove old requests outside the window\n      while (requests.length > 0 && requests[0] <= now - windowMs) {\n        requests.shift();\n      }\n\n      // Check if we're under the limit\n      if (requests.length < maxRequests) {\n        requests.push(now);\n        return true;\n      }\n      return false;\n    },\n    getRemainingRequests: () => {\n      const now = Date.now();\n\n      // Remove old requests\n      while (requests.length > 0 && requests[0] <= now - windowMs) {\n        requests.shift();\n      }\n      return Math.max(0, maxRequests - requests.length);\n    }\n  };\n};\n\n/**\n * Content Security Policy violation handler\n */\nexport const handleCSPViolation = event => {\n  console.error('CSP Violation:', {\n    blockedURI: event.blockedURI,\n    violatedDirective: event.violatedDirective,\n    originalPolicy: event.originalPolicy,\n    sourceFile: event.sourceFile,\n    lineNumber: event.lineNumber\n  });\n\n  // In production, you might want to send this to your security monitoring system\n  if (process.env.NODE_ENV === 'production') {\n    // Send to security monitoring endpoint\n    fetch('/api/v1/security/csp-violation/', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify({\n        blockedURI: event.blockedURI,\n        violatedDirective: event.violatedDirective,\n        sourceFile: event.sourceFile,\n        lineNumber: event.lineNumber,\n        timestamp: new Date().toISOString()\n      })\n    }).catch(console.error);\n  }\n};\n\n// Set up CSP violation listener\nif (typeof window !== 'undefined') {\n  document.addEventListener('securitypolicyviolation', handleCSPViolation);\n}", "map": {"version": 3, "names": ["CryptoJS", "validateEnvironment", "process", "env", "NODE_ENV", "console", "warn", "requiredEnvVars", "missingVars", "filter", "varName", "envValue", "REACT_APP_API_URL", "undefined", "length", "error", "window", "location", "protocol", "sanitizeInput", "input", "div", "document", "createElement", "textContent", "innerHTML", "isValidEmail", "email", "emailRegex", "test", "validatePasswordStrength", "password", "errors", "score", "push", "commonPatterns", "some", "pattern", "hasSequentialChars", "<PERSON><PERSON><PERSON><PERSON>", "Math", "max", "sequences", "passwordLower", "toLowerCase", "sequence", "i", "subseq", "substring", "reverseSubseq", "split", "reverse", "join", "includes", "generateSecureRandom", "array", "Uint8Array", "crypto", "getRandomValues", "Array", "from", "byte", "toString", "padStart", "hashData", "data", "salt", "saltToUse", "SHA256", "encryptData", "key", "AES", "encrypt", "decryptData", "encryptedData", "bytes", "decrypt", "enc", "Utf8", "secureStorage", "setItem", "value", "dataToStore", "localStorage", "getItem", "removeItem", "clear", "detectSecurityThreats", "threats", "xssPatterns", "sqlPatterns", "isSafe", "createRateLimiter", "maxRequests", "windowMs", "requests", "isAllowed", "now", "Date", "shift", "getRemainingRequests", "handleCSPViolation", "event", "blockedURI", "violatedDirective", "originalPolicy", "sourceFile", "lineNumber", "fetch", "method", "headers", "body", "JSON", "stringify", "timestamp", "toISOString", "catch", "addEventListener"], "sources": ["C:/Users/<USER>/Documents/augment-projects/TrustVault/frontend/src/utils/security.ts"], "sourcesContent": ["// TrustVault - Security Utilities\n\nimport CryptoJS from 'crypto-js';\n\n/**\n * Validate environment security settings\n */\nexport const validateEnvironment = (): void => {\n  // Check if running in development\n  if (process.env.NODE_ENV === 'development') {\n    console.warn('🔒 Running in development mode - security features may be relaxed');\n  }\n\n  // Validate required environment variables\n  const requiredEnvVars = [\n    'REACT_APP_API_URL',\n  ];\n\n  const missingVars = requiredEnvVars.filter(\n    (varName) => {\n      const envValue = varName === 'REACT_APP_API_URL' ? process.env.REACT_APP_API_URL : undefined;\n      return !envValue;\n    }\n  );\n\n  if (missingVars.length > 0) {\n    console.error('❌ Missing required environment variables:', missingVars);\n  }\n\n  // Check for HTTPS in production\n  if (process.env.NODE_ENV === 'production' && window.location.protocol !== 'https:') {\n    console.warn('⚠️ Application should be served over HTTPS in production');\n  }\n};\n\n/**\n * Sanitize user input to prevent XSS\n */\nexport const sanitizeInput = (input: string): string => {\n  const div = document.createElement('div');\n  div.textContent = input;\n  return div.innerHTML;\n};\n\n/**\n * Validate email format\n */\nexport const isValidEmail = (email: string): boolean => {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n};\n\n/**\n * Validate password strength\n */\nexport const validatePasswordStrength = (password: string): {\n  isValid: boolean;\n  errors: string[];\n  score: number;\n} => {\n  const errors: string[] = [];\n  let score = 0;\n\n  // Length check\n  if (password.length < 12) {\n    errors.push('Password must be at least 12 characters long');\n  } else {\n    score += 1;\n  }\n\n  // Uppercase check\n  if (!/[A-Z]/.test(password)) {\n    errors.push('Password must contain at least one uppercase letter');\n  } else {\n    score += 1;\n  }\n\n  // Lowercase check\n  if (!/[a-z]/.test(password)) {\n    errors.push('Password must contain at least one lowercase letter');\n  } else {\n    score += 1;\n  }\n\n  // Number check\n  if (!/\\d/.test(password)) {\n    errors.push('Password must contain at least one number');\n  } else {\n    score += 1;\n  }\n\n  // Special character check\n  if (!/[!@#$%^&*(),.?\":{}|<>]/.test(password)) {\n    errors.push('Password must contain at least one special character');\n  } else {\n    score += 1;\n  }\n\n  // Common patterns check\n  const commonPatterns = [\n    /123456/,\n    /password/i,\n    /qwerty/i,\n    /abc123/i,\n    /admin/i,\n    /letmein/i,\n    /welcome/i,\n  ];\n\n  if (commonPatterns.some(pattern => pattern.test(password))) {\n    errors.push('Password contains common patterns that are not allowed');\n    score -= 1;\n  }\n\n  // Sequential characters check\n  if (hasSequentialChars(password)) {\n    errors.push('Password cannot contain sequential characters');\n    score -= 1;\n  }\n\n  return {\n    isValid: errors.length === 0,\n    errors,\n    score: Math.max(0, score),\n  };\n};\n\n/**\n * Check for sequential characters in password\n */\nconst hasSequentialChars = (password: string): boolean => {\n  const sequences = [\n    'abcdefghijklmnopqrstuvwxyz',\n    '0123456789',\n    'qwertyuiopasdfghjklzxcvbnm', // keyboard layout\n  ];\n\n  const passwordLower = password.toLowerCase();\n\n  for (const sequence of sequences) {\n    for (let i = 0; i <= sequence.length - 3; i++) {\n      const subseq = sequence.substring(i, i + 3);\n      const reverseSubseq = subseq.split('').reverse().join('');\n      \n      if (passwordLower.includes(subseq) || passwordLower.includes(reverseSubseq)) {\n        return true;\n      }\n    }\n  }\n\n  return false;\n};\n\n/**\n * Generate secure random string\n */\nexport const generateSecureRandom = (length: number = 32): string => {\n  const array = new Uint8Array(length);\n  window.crypto.getRandomValues(array);\n  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');\n};\n\n/**\n * Hash sensitive data for client-side storage\n */\nexport const hashData = (data: string, salt?: string): string => {\n  const saltToUse = salt || generateSecureRandom(16);\n  return CryptoJS.SHA256(data + saltToUse).toString();\n};\n\n/**\n * Encrypt sensitive data for client-side storage\n */\nexport const encryptData = (data: string, key: string): string => {\n  return CryptoJS.AES.encrypt(data, key).toString();\n};\n\n/**\n * Decrypt sensitive data from client-side storage\n */\nexport const decryptData = (encryptedData: string, key: string): string => {\n  const bytes = CryptoJS.AES.decrypt(encryptedData, key);\n  return bytes.toString(CryptoJS.enc.Utf8);\n};\n\n/**\n * Secure localStorage wrapper\n */\nexport const secureStorage = {\n  setItem: (key: string, value: string, encrypt: boolean = false): void => {\n    try {\n      const dataToStore = encrypt ? encryptData(value, key) : value;\n      localStorage.setItem(key, dataToStore);\n    } catch (error) {\n      console.error('Failed to store data securely:', error);\n    }\n  },\n\n  getItem: (key: string, decrypt: boolean = false): string | null => {\n    try {\n      const data = localStorage.getItem(key);\n      if (!data) return null;\n      \n      return decrypt ? decryptData(data, key) : data;\n    } catch (error) {\n      console.error('Failed to retrieve data securely:', error);\n      return null;\n    }\n  },\n\n  removeItem: (key: string): void => {\n    localStorage.removeItem(key);\n  },\n\n  clear: (): void => {\n    localStorage.clear();\n  },\n};\n\n/**\n * Detect potential security threats in user input\n */\nexport const detectSecurityThreats = (input: string): {\n  isSafe: boolean;\n  threats: string[];\n} => {\n  const threats: string[] = [];\n\n  // XSS patterns\n  const xssPatterns = [\n    /<script[^>]*>.*?<\\/script>/gi,\n    /javascript:/gi,\n    /vbscript:/gi,\n    /on\\w+\\s*=/gi,\n    /eval\\s*\\(/gi,\n    /document\\.cookie/gi,\n    /alert\\s*\\(/gi,\n  ];\n\n  // SQL injection patterns\n  const sqlPatterns = [\n    /union\\s+select/gi,\n    /or\\s+1\\s*=\\s*1/gi,\n    /drop\\s+table/gi,\n    /insert\\s+into/gi,\n    /delete\\s+from/gi,\n    /update\\s+.*set/gi,\n  ];\n\n  // Check for XSS\n  if (xssPatterns.some(pattern => pattern.test(input))) {\n    threats.push('Potential XSS attack detected');\n  }\n\n  // Check for SQL injection\n  if (sqlPatterns.some(pattern => pattern.test(input))) {\n    threats.push('Potential SQL injection detected');\n  }\n\n  return {\n    isSafe: threats.length === 0,\n    threats,\n  };\n};\n\n/**\n * Rate limiting utility\n */\nexport const createRateLimiter = (maxRequests: number, windowMs: number) => {\n  const requests: number[] = [];\n\n  return {\n    isAllowed: (): boolean => {\n      const now = Date.now();\n      \n      // Remove old requests outside the window\n      while (requests.length > 0 && requests[0] <= now - windowMs) {\n        requests.shift();\n      }\n\n      // Check if we're under the limit\n      if (requests.length < maxRequests) {\n        requests.push(now);\n        return true;\n      }\n\n      return false;\n    },\n    \n    getRemainingRequests: (): number => {\n      const now = Date.now();\n      \n      // Remove old requests\n      while (requests.length > 0 && requests[0] <= now - windowMs) {\n        requests.shift();\n      }\n\n      return Math.max(0, maxRequests - requests.length);\n    },\n  };\n};\n\n/**\n * Content Security Policy violation handler\n */\nexport const handleCSPViolation = (event: SecurityPolicyViolationEvent): void => {\n  console.error('CSP Violation:', {\n    blockedURI: event.blockedURI,\n    violatedDirective: event.violatedDirective,\n    originalPolicy: event.originalPolicy,\n    sourceFile: event.sourceFile,\n    lineNumber: event.lineNumber,\n  });\n\n  // In production, you might want to send this to your security monitoring system\n  if (process.env.NODE_ENV === 'production') {\n    // Send to security monitoring endpoint\n    fetch('/api/v1/security/csp-violation/', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        blockedURI: event.blockedURI,\n        violatedDirective: event.violatedDirective,\n        sourceFile: event.sourceFile,\n        lineNumber: event.lineNumber,\n        timestamp: new Date().toISOString(),\n      }),\n    }).catch(console.error);\n  }\n};\n\n// Set up CSP violation listener\nif (typeof window !== 'undefined') {\n  document.addEventListener('securitypolicyviolation', handleCSPViolation);\n}\n"], "mappings": "AAAA;;AAEA,OAAOA,QAAQ,MAAM,WAAW;;AAEhC;AACA;AACA;AACA,OAAO,MAAMC,mBAAmB,GAAGA,CAAA,KAAY;EAC7C;EACA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;IAC1CC,OAAO,CAACC,IAAI,CAAC,mEAAmE,CAAC;EACnF;;EAEA;EACA,MAAMC,eAAe,GAAG,CACtB,mBAAmB,CACpB;EAED,MAAMC,WAAW,GAAGD,eAAe,CAACE,MAAM,CACvCC,OAAO,IAAK;IACX,MAAMC,QAAQ,GAAGD,OAAO,KAAK,mBAAmB,GAAGR,OAAO,CAACC,GAAG,CAACS,iBAAiB,GAAGC,SAAS;IAC5F,OAAO,CAACF,QAAQ;EAClB,CACF,CAAC;EAED,IAAIH,WAAW,CAACM,MAAM,GAAG,CAAC,EAAE;IAC1BT,OAAO,CAACU,KAAK,CAAC,2CAA2C,EAAEP,WAAW,CAAC;EACzE;;EAEA;EACA,IAAIN,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIY,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,QAAQ,EAAE;IAClFb,OAAO,CAACC,IAAI,CAAC,0DAA0D,CAAC;EAC1E;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMa,aAAa,GAAIC,KAAa,IAAa;EACtD,MAAMC,GAAG,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;EACzCF,GAAG,CAACG,WAAW,GAAGJ,KAAK;EACvB,OAAOC,GAAG,CAACI,SAAS;AACtB,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMC,YAAY,GAAIC,KAAa,IAAc;EACtD,MAAMC,UAAU,GAAG,4BAA4B;EAC/C,OAAOA,UAAU,CAACC,IAAI,CAACF,KAAK,CAAC;AAC/B,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMG,wBAAwB,GAAIC,QAAgB,IAIpD;EACH,MAAMC,MAAgB,GAAG,EAAE;EAC3B,IAAIC,KAAK,GAAG,CAAC;;EAEb;EACA,IAAIF,QAAQ,CAACjB,MAAM,GAAG,EAAE,EAAE;IACxBkB,MAAM,CAACE,IAAI,CAAC,8CAA8C,CAAC;EAC7D,CAAC,MAAM;IACLD,KAAK,IAAI,CAAC;EACZ;;EAEA;EACA,IAAI,CAAC,OAAO,CAACJ,IAAI,CAACE,QAAQ,CAAC,EAAE;IAC3BC,MAAM,CAACE,IAAI,CAAC,qDAAqD,CAAC;EACpE,CAAC,MAAM;IACLD,KAAK,IAAI,CAAC;EACZ;;EAEA;EACA,IAAI,CAAC,OAAO,CAACJ,IAAI,CAACE,QAAQ,CAAC,EAAE;IAC3BC,MAAM,CAACE,IAAI,CAAC,qDAAqD,CAAC;EACpE,CAAC,MAAM;IACLD,KAAK,IAAI,CAAC;EACZ;;EAEA;EACA,IAAI,CAAC,IAAI,CAACJ,IAAI,CAACE,QAAQ,CAAC,EAAE;IACxBC,MAAM,CAACE,IAAI,CAAC,2CAA2C,CAAC;EAC1D,CAAC,MAAM;IACLD,KAAK,IAAI,CAAC;EACZ;;EAEA;EACA,IAAI,CAAC,wBAAwB,CAACJ,IAAI,CAACE,QAAQ,CAAC,EAAE;IAC5CC,MAAM,CAACE,IAAI,CAAC,sDAAsD,CAAC;EACrE,CAAC,MAAM;IACLD,KAAK,IAAI,CAAC;EACZ;;EAEA;EACA,MAAME,cAAc,GAAG,CACrB,QAAQ,EACR,WAAW,EACX,SAAS,EACT,SAAS,EACT,QAAQ,EACR,UAAU,EACV,UAAU,CACX;EAED,IAAIA,cAAc,CAACC,IAAI,CAACC,OAAO,IAAIA,OAAO,CAACR,IAAI,CAACE,QAAQ,CAAC,CAAC,EAAE;IAC1DC,MAAM,CAACE,IAAI,CAAC,wDAAwD,CAAC;IACrED,KAAK,IAAI,CAAC;EACZ;;EAEA;EACA,IAAIK,kBAAkB,CAACP,QAAQ,CAAC,EAAE;IAChCC,MAAM,CAACE,IAAI,CAAC,+CAA+C,CAAC;IAC5DD,KAAK,IAAI,CAAC;EACZ;EAEA,OAAO;IACLM,OAAO,EAAEP,MAAM,CAAClB,MAAM,KAAK,CAAC;IAC5BkB,MAAM;IACNC,KAAK,EAAEO,IAAI,CAACC,GAAG,CAAC,CAAC,EAAER,KAAK;EAC1B,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA,MAAMK,kBAAkB,GAAIP,QAAgB,IAAc;EACxD,MAAMW,SAAS,GAAG,CAChB,4BAA4B,EAC5B,YAAY,EACZ,4BAA4B,CAAE;EAAA,CAC/B;EAED,MAAMC,aAAa,GAAGZ,QAAQ,CAACa,WAAW,CAAC,CAAC;EAE5C,KAAK,MAAMC,QAAQ,IAAIH,SAAS,EAAE;IAChC,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAID,QAAQ,CAAC/B,MAAM,GAAG,CAAC,EAAEgC,CAAC,EAAE,EAAE;MAC7C,MAAMC,MAAM,GAAGF,QAAQ,CAACG,SAAS,CAACF,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC;MAC3C,MAAMG,aAAa,GAAGF,MAAM,CAACG,KAAK,CAAC,EAAE,CAAC,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;MAEzD,IAAIT,aAAa,CAACU,QAAQ,CAACN,MAAM,CAAC,IAAIJ,aAAa,CAACU,QAAQ,CAACJ,aAAa,CAAC,EAAE;QAC3E,OAAO,IAAI;MACb;IACF;EACF;EAEA,OAAO,KAAK;AACd,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMK,oBAAoB,GAAGA,CAACxC,MAAc,GAAG,EAAE,KAAa;EACnE,MAAMyC,KAAK,GAAG,IAAIC,UAAU,CAAC1C,MAAM,CAAC;EACpCE,MAAM,CAACyC,MAAM,CAACC,eAAe,CAACH,KAAK,CAAC;EACpC,OAAOI,KAAK,CAACC,IAAI,CAACL,KAAK,EAAEM,IAAI,IAAIA,IAAI,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAACX,IAAI,CAAC,EAAE,CAAC;AAC/E,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMY,QAAQ,GAAGA,CAACC,IAAY,EAAEC,IAAa,KAAa;EAC/D,MAAMC,SAAS,GAAGD,IAAI,IAAIZ,oBAAoB,CAAC,EAAE,CAAC;EAClD,OAAOtD,QAAQ,CAACoE,MAAM,CAACH,IAAI,GAAGE,SAAS,CAAC,CAACL,QAAQ,CAAC,CAAC;AACrD,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMO,WAAW,GAAGA,CAACJ,IAAY,EAAEK,GAAW,KAAa;EAChE,OAAOtE,QAAQ,CAACuE,GAAG,CAACC,OAAO,CAACP,IAAI,EAAEK,GAAG,CAAC,CAACR,QAAQ,CAAC,CAAC;AACnD,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMW,WAAW,GAAGA,CAACC,aAAqB,EAAEJ,GAAW,KAAa;EACzE,MAAMK,KAAK,GAAG3E,QAAQ,CAACuE,GAAG,CAACK,OAAO,CAACF,aAAa,EAAEJ,GAAG,CAAC;EACtD,OAAOK,KAAK,CAACb,QAAQ,CAAC9D,QAAQ,CAAC6E,GAAG,CAACC,IAAI,CAAC;AAC1C,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMC,aAAa,GAAG;EAC3BC,OAAO,EAAEA,CAACV,GAAW,EAAEW,KAAa,EAAET,OAAgB,GAAG,KAAK,KAAW;IACvE,IAAI;MACF,MAAMU,WAAW,GAAGV,OAAO,GAAGH,WAAW,CAACY,KAAK,EAAEX,GAAG,CAAC,GAAGW,KAAK;MAC7DE,YAAY,CAACH,OAAO,CAACV,GAAG,EAAEY,WAAW,CAAC;IACxC,CAAC,CAAC,OAAOnE,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD;EACF,CAAC;EAEDqE,OAAO,EAAEA,CAACd,GAAW,EAAEM,OAAgB,GAAG,KAAK,KAAoB;IACjE,IAAI;MACF,MAAMX,IAAI,GAAGkB,YAAY,CAACC,OAAO,CAACd,GAAG,CAAC;MACtC,IAAI,CAACL,IAAI,EAAE,OAAO,IAAI;MAEtB,OAAOW,OAAO,GAAGH,WAAW,CAACR,IAAI,EAAEK,GAAG,CAAC,GAAGL,IAAI;IAChD,CAAC,CAAC,OAAOlD,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,OAAO,IAAI;IACb;EACF,CAAC;EAEDsE,UAAU,EAAGf,GAAW,IAAW;IACjCa,YAAY,CAACE,UAAU,CAACf,GAAG,CAAC;EAC9B,CAAC;EAEDgB,KAAK,EAAEA,CAAA,KAAY;IACjBH,YAAY,CAACG,KAAK,CAAC,CAAC;EACtB;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMC,qBAAqB,GAAInE,KAAa,IAG9C;EACH,MAAMoE,OAAiB,GAAG,EAAE;;EAE5B;EACA,MAAMC,WAAW,GAAG,CAClB,8BAA8B,EAC9B,eAAe,EACf,aAAa,EACb,aAAa,EACb,aAAa,EACb,oBAAoB,EACpB,cAAc,CACf;;EAED;EACA,MAAMC,WAAW,GAAG,CAClB,kBAAkB,EAClB,kBAAkB,EAClB,gBAAgB,EAChB,iBAAiB,EACjB,iBAAiB,EACjB,kBAAkB,CACnB;;EAED;EACA,IAAID,WAAW,CAACrD,IAAI,CAACC,OAAO,IAAIA,OAAO,CAACR,IAAI,CAACT,KAAK,CAAC,CAAC,EAAE;IACpDoE,OAAO,CAACtD,IAAI,CAAC,+BAA+B,CAAC;EAC/C;;EAEA;EACA,IAAIwD,WAAW,CAACtD,IAAI,CAACC,OAAO,IAAIA,OAAO,CAACR,IAAI,CAACT,KAAK,CAAC,CAAC,EAAE;IACpDoE,OAAO,CAACtD,IAAI,CAAC,kCAAkC,CAAC;EAClD;EAEA,OAAO;IACLyD,MAAM,EAAEH,OAAO,CAAC1E,MAAM,KAAK,CAAC;IAC5B0E;EACF,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMI,iBAAiB,GAAGA,CAACC,WAAmB,EAAEC,QAAgB,KAAK;EAC1E,MAAMC,QAAkB,GAAG,EAAE;EAE7B,OAAO;IACLC,SAAS,EAAEA,CAAA,KAAe;MACxB,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;;MAEtB;MACA,OAAOF,QAAQ,CAACjF,MAAM,GAAG,CAAC,IAAIiF,QAAQ,CAAC,CAAC,CAAC,IAAIE,GAAG,GAAGH,QAAQ,EAAE;QAC3DC,QAAQ,CAACI,KAAK,CAAC,CAAC;MAClB;;MAEA;MACA,IAAIJ,QAAQ,CAACjF,MAAM,GAAG+E,WAAW,EAAE;QACjCE,QAAQ,CAAC7D,IAAI,CAAC+D,GAAG,CAAC;QAClB,OAAO,IAAI;MACb;MAEA,OAAO,KAAK;IACd,CAAC;IAEDG,oBAAoB,EAAEA,CAAA,KAAc;MAClC,MAAMH,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;;MAEtB;MACA,OAAOF,QAAQ,CAACjF,MAAM,GAAG,CAAC,IAAIiF,QAAQ,CAAC,CAAC,CAAC,IAAIE,GAAG,GAAGH,QAAQ,EAAE;QAC3DC,QAAQ,CAACI,KAAK,CAAC,CAAC;MAClB;MAEA,OAAO3D,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEoD,WAAW,GAAGE,QAAQ,CAACjF,MAAM,CAAC;IACnD;EACF,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMuF,kBAAkB,GAAIC,KAAmC,IAAW;EAC/EjG,OAAO,CAACU,KAAK,CAAC,gBAAgB,EAAE;IAC9BwF,UAAU,EAAED,KAAK,CAACC,UAAU;IAC5BC,iBAAiB,EAAEF,KAAK,CAACE,iBAAiB;IAC1CC,cAAc,EAAEH,KAAK,CAACG,cAAc;IACpCC,UAAU,EAAEJ,KAAK,CAACI,UAAU;IAC5BC,UAAU,EAAEL,KAAK,CAACK;EACpB,CAAC,CAAC;;EAEF;EACA,IAAIzG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC;IACAwG,KAAK,CAAC,iCAAiC,EAAE;MACvCC,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QACnBV,UAAU,EAAED,KAAK,CAACC,UAAU;QAC5BC,iBAAiB,EAAEF,KAAK,CAACE,iBAAiB;QAC1CE,UAAU,EAAEJ,KAAK,CAACI,UAAU;QAC5BC,UAAU,EAAEL,KAAK,CAACK,UAAU;QAC5BO,SAAS,EAAE,IAAIhB,IAAI,CAAC,CAAC,CAACiB,WAAW,CAAC;MACpC,CAAC;IACH,CAAC,CAAC,CAACC,KAAK,CAAC/G,OAAO,CAACU,KAAK,CAAC;EACzB;AACF,CAAC;;AAED;AACA,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;EACjCM,QAAQ,CAAC+F,gBAAgB,CAAC,yBAAyB,EAAEhB,kBAAkB,CAAC;AAC1E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}