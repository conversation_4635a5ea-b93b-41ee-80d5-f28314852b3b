{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nvar _InputBase;\nconst _excluded = [\"ActionsComponent\", \"backIconButtonProps\", \"className\", \"colSpan\", \"component\", \"count\", \"disabled\", \"getItemAriaLabel\", \"labelDisplayedRows\", \"labelRowsPerPage\", \"nextIconButtonProps\", \"onPageChange\", \"onRowsPerPageChange\", \"page\", \"rowsPerPage\", \"rowsPerPageOptions\", \"SelectProps\", \"showFirstButton\", \"showLastButton\", \"slotProps\", \"slots\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport integerPropType from '@mui/utils/integerPropType';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport composeClasses from '@mui/utils/composeClasses';\nimport isHostComponent from '@mui/utils/isHostComponent';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport InputBase from '../InputBase';\nimport MenuItem from '../MenuItem';\nimport Select from '../Select';\nimport TableCell from '../TableCell';\nimport Toolbar from '../Toolbar';\nimport TablePaginationActions from './TablePaginationActions';\nimport useId from '../utils/useId';\nimport tablePaginationClasses, { getTablePaginationUtilityClass } from './tablePaginationClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { createElement as _createElement } from \"react\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst TablePaginationRoot = styled(TableCell, {\n  name: 'MuiTablePagination',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(_ref => {\n  let {\n    theme\n  } = _ref;\n  return {\n    overflow: 'auto',\n    color: (theme.vars || theme).palette.text.primary,\n    fontSize: theme.typography.pxToRem(14),\n    // Increase the specificity to override TableCell.\n    '&:last-child': {\n      padding: 0\n    }\n  };\n});\nconst TablePaginationToolbar = styled(Toolbar, {\n  name: 'MuiTablePagination',\n  slot: 'Toolbar',\n  overridesResolver: (props, styles) => _extends({\n    [`& .${tablePaginationClasses.actions}`]: styles.actions\n  }, styles.toolbar)\n})(_ref2 => {\n  let {\n    theme\n  } = _ref2;\n  return {\n    minHeight: 52,\n    paddingRight: 2,\n    [`${theme.breakpoints.up('xs')} and (orientation: landscape)`]: {\n      minHeight: 52\n    },\n    [theme.breakpoints.up('sm')]: {\n      minHeight: 52,\n      paddingRight: 2\n    },\n    [`& .${tablePaginationClasses.actions}`]: {\n      flexShrink: 0,\n      marginLeft: 20\n    }\n  };\n});\nconst TablePaginationSpacer = styled('div', {\n  name: 'MuiTablePagination',\n  slot: 'Spacer',\n  overridesResolver: (props, styles) => styles.spacer\n})({\n  flex: '1 1 100%'\n});\nconst TablePaginationSelectLabel = styled('p', {\n  name: 'MuiTablePagination',\n  slot: 'SelectLabel',\n  overridesResolver: (props, styles) => styles.selectLabel\n})(_ref3 => {\n  let {\n    theme\n  } = _ref3;\n  return _extends({}, theme.typography.body2, {\n    flexShrink: 0\n  });\n});\nconst TablePaginationSelect = styled(Select, {\n  name: 'MuiTablePagination',\n  slot: 'Select',\n  overridesResolver: (props, styles) => _extends({\n    [`& .${tablePaginationClasses.selectIcon}`]: styles.selectIcon,\n    [`& .${tablePaginationClasses.select}`]: styles.select\n  }, styles.input, styles.selectRoot)\n})({\n  color: 'inherit',\n  fontSize: 'inherit',\n  flexShrink: 0,\n  marginRight: 32,\n  marginLeft: 8,\n  [`& .${tablePaginationClasses.select}`]: {\n    paddingLeft: 8,\n    paddingRight: 24,\n    textAlign: 'right',\n    textAlignLast: 'right' // Align <select> on Chrome.\n  }\n});\nconst TablePaginationMenuItem = styled(MenuItem, {\n  name: 'MuiTablePagination',\n  slot: 'MenuItem',\n  overridesResolver: (props, styles) => styles.menuItem\n})({});\nconst TablePaginationDisplayedRows = styled('p', {\n  name: 'MuiTablePagination',\n  slot: 'DisplayedRows',\n  overridesResolver: (props, styles) => styles.displayedRows\n})(_ref4 => {\n  let {\n    theme\n  } = _ref4;\n  return _extends({}, theme.typography.body2, {\n    flexShrink: 0\n  });\n});\nfunction defaultLabelDisplayedRows(_ref5) {\n  let {\n    from,\n    to,\n    count\n  } = _ref5;\n  return `${from}–${to} of ${count !== -1 ? count : `more than ${to}`}`;\n}\nfunction defaultGetAriaLabel(type) {\n  return `Go to ${type} page`;\n}\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    toolbar: ['toolbar'],\n    spacer: ['spacer'],\n    selectLabel: ['selectLabel'],\n    select: ['select'],\n    input: ['input'],\n    selectIcon: ['selectIcon'],\n    menuItem: ['menuItem'],\n    displayedRows: ['displayedRows'],\n    actions: ['actions']\n  };\n  return composeClasses(slots, getTablePaginationUtilityClass, classes);\n};\n\n/**\n * A `TableCell` based component for placing inside `TableFooter` for pagination.\n */\nconst TablePagination = /*#__PURE__*/React.forwardRef(function TablePagination(inProps, ref) {\n  var _slotProps$select;\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiTablePagination'\n  });\n  const {\n      ActionsComponent = TablePaginationActions,\n      backIconButtonProps,\n      className,\n      colSpan: colSpanProp,\n      component = TableCell,\n      count,\n      disabled = false,\n      getItemAriaLabel = defaultGetAriaLabel,\n      labelDisplayedRows = defaultLabelDisplayedRows,\n      labelRowsPerPage = 'Rows per page:',\n      nextIconButtonProps,\n      onPageChange,\n      onRowsPerPageChange,\n      page,\n      rowsPerPage,\n      rowsPerPageOptions = [10, 25, 50, 100],\n      SelectProps = {},\n      showFirstButton = false,\n      showLastButton = false,\n      slotProps = {},\n      slots = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = props;\n  const classes = useUtilityClasses(ownerState);\n  const selectProps = (_slotProps$select = slotProps == null ? void 0 : slotProps.select) != null ? _slotProps$select : SelectProps;\n  const MenuItemComponent = selectProps.native ? 'option' : TablePaginationMenuItem;\n  let colSpan;\n  if (component === TableCell || component === 'td') {\n    colSpan = colSpanProp || 1000; // col-span over everything\n  }\n  const selectId = useId(selectProps.id);\n  const labelId = useId(selectProps.labelId);\n  const getLabelDisplayedRowsTo = () => {\n    if (count === -1) {\n      return (page + 1) * rowsPerPage;\n    }\n    return rowsPerPage === -1 ? count : Math.min(count, (page + 1) * rowsPerPage);\n  };\n  return /*#__PURE__*/_jsx(TablePaginationRoot, _extends({\n    colSpan: colSpan,\n    ref: ref,\n    as: component,\n    ownerState: ownerState,\n    className: clsx(classes.root, className)\n  }, other, {\n    children: /*#__PURE__*/_jsxs(TablePaginationToolbar, {\n      className: classes.toolbar,\n      children: [/*#__PURE__*/_jsx(TablePaginationSpacer, {\n        className: classes.spacer\n      }), rowsPerPageOptions.length > 1 && /*#__PURE__*/_jsx(TablePaginationSelectLabel, {\n        className: classes.selectLabel,\n        id: labelId,\n        children: labelRowsPerPage\n      }), rowsPerPageOptions.length > 1 && /*#__PURE__*/_jsx(TablePaginationSelect, _extends({\n        variant: \"standard\"\n      }, !selectProps.variant && {\n        input: _InputBase || (_InputBase = /*#__PURE__*/_jsx(InputBase, {}))\n      }, {\n        value: rowsPerPage,\n        onChange: onRowsPerPageChange,\n        id: selectId,\n        labelId: labelId\n      }, selectProps, {\n        classes: _extends({}, selectProps.classes, {\n          // TODO v5 remove `classes.input`\n          root: clsx(classes.input, classes.selectRoot, (selectProps.classes || {}).root),\n          select: clsx(classes.select, (selectProps.classes || {}).select),\n          // TODO v5 remove `selectIcon`\n          icon: clsx(classes.selectIcon, (selectProps.classes || {}).icon)\n        }),\n        disabled: disabled,\n        children: rowsPerPageOptions.map(rowsPerPageOption => /*#__PURE__*/_createElement(MenuItemComponent, _extends({}, !isHostComponent(MenuItemComponent) && {\n          ownerState\n        }, {\n          className: classes.menuItem,\n          key: rowsPerPageOption.label ? rowsPerPageOption.label : rowsPerPageOption,\n          value: rowsPerPageOption.value ? rowsPerPageOption.value : rowsPerPageOption\n        }), rowsPerPageOption.label ? rowsPerPageOption.label : rowsPerPageOption))\n      })), /*#__PURE__*/_jsx(TablePaginationDisplayedRows, {\n        className: classes.displayedRows,\n        children: labelDisplayedRows({\n          from: count === 0 ? 0 : page * rowsPerPage + 1,\n          to: getLabelDisplayedRowsTo(),\n          count: count === -1 ? -1 : count,\n          page\n        })\n      }), /*#__PURE__*/_jsx(ActionsComponent, {\n        className: classes.actions,\n        backIconButtonProps: backIconButtonProps,\n        count: count,\n        nextIconButtonProps: nextIconButtonProps,\n        onPageChange: onPageChange,\n        page: page,\n        rowsPerPage: rowsPerPage,\n        showFirstButton: showFirstButton,\n        showLastButton: showLastButton,\n        slotProps: slotProps.actions,\n        slots: slots.actions,\n        getItemAriaLabel: getItemAriaLabel,\n        disabled: disabled\n      })]\n    })\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? TablePagination.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The component used for displaying the actions.\n   * Either a string to use a HTML element or a component.\n   * @default TablePaginationActions\n   */\n  ActionsComponent: PropTypes.elementType,\n  /**\n   * Props applied to the back arrow [`IconButton`](/material-ui/api/icon-button/) component.\n   *\n   * This prop is an alias for `slotProps.actions.previousButton` and will be overriden by it if both are used.\n   * @deprecated Use `slotProps.actions.previousButton` instead.\n   */\n  backIconButtonProps: PropTypes.object,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * @ignore\n   */\n  colSpan: PropTypes.number,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The total number of rows.\n   *\n   * To enable server side pagination for an unknown number of items, provide -1.\n   */\n  count: integerPropType.isRequired,\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * Accepts a function which returns a string value that provides a user-friendly name for the current page.\n   * This is important for screen reader users.\n   *\n   * For localization purposes, you can use the provided [translations](/material-ui/guides/localization/).\n   * @param {string} type The link or button type to format ('first' | 'last' | 'next' | 'previous').\n   * @returns {string}\n   * @default function defaultGetAriaLabel(type) {\n   *   return `Go to ${type} page`;\n   * }\n   */\n  getItemAriaLabel: PropTypes.func,\n  /**\n   * Customize the displayed rows label. Invoked with a `{ from, to, count, page }`\n   * object.\n   *\n   * For localization purposes, you can use the provided [translations](/material-ui/guides/localization/).\n   * @default function defaultLabelDisplayedRows({ from, to, count }) {\n   *   return `${from}–${to} of ${count !== -1 ? count : `more than ${to}`}`;\n   * }\n   */\n  labelDisplayedRows: PropTypes.func,\n  /**\n   * Customize the rows per page label.\n   *\n   * For localization purposes, you can use the provided [translations](/material-ui/guides/localization/).\n   * @default 'Rows per page:'\n   */\n  labelRowsPerPage: PropTypes.node,\n  /**\n   * Props applied to the next arrow [`IconButton`](/material-ui/api/icon-button/) element.\n   *\n   * This prop is an alias for `slotProps.actions.nextButton` and will be overriden by it if both are used.\n   * @deprecated Use `slotProps.actions.nextButton` instead.\n   */\n  nextIconButtonProps: PropTypes.object,\n  /**\n   * Callback fired when the page is changed.\n   *\n   * @param {React.MouseEvent<HTMLButtonElement> | null} event The event source of the callback.\n   * @param {number} page The page selected.\n   */\n  onPageChange: PropTypes.func.isRequired,\n  /**\n   * Callback fired when the number of rows per page is changed.\n   *\n   * @param {React.ChangeEvent<HTMLTextAreaElement | HTMLInputElement>} event The event source of the callback.\n   */\n  onRowsPerPageChange: PropTypes.func,\n  /**\n   * The zero-based index of the current page.\n   */\n  page: chainPropTypes(integerPropType.isRequired, props => {\n    const {\n      count,\n      page,\n      rowsPerPage\n    } = props;\n    if (count === -1) {\n      return null;\n    }\n    const newLastPage = Math.max(0, Math.ceil(count / rowsPerPage) - 1);\n    if (page < 0 || page > newLastPage) {\n      return new Error('MUI: The page prop of a TablePagination is out of range ' + `(0 to ${newLastPage}, but page is ${page}).`);\n    }\n    return null;\n  }),\n  /**\n   * The number of rows per page.\n   *\n   * Set -1 to display all the rows.\n   */\n  rowsPerPage: integerPropType.isRequired,\n  /**\n   * Customizes the options of the rows per page select field. If less than two options are\n   * available, no select field will be displayed.\n   * Use -1 for the value with a custom label to show all the rows.\n   * @default [10, 25, 50, 100]\n   */\n  rowsPerPageOptions: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n    label: PropTypes.string.isRequired,\n    value: PropTypes.number.isRequired\n  })]).isRequired),\n  /**\n   * Props applied to the rows per page [`Select`](/material-ui/api/select/) element.\n   *\n   * This prop is an alias for `slotProps.select` and will be overriden by it if both are used.\n   * @deprecated Use `slotProps.select` instead.\n   *\n   * @default {}\n   */\n  SelectProps: PropTypes.object,\n  /**\n   * If `true`, show the first-page button.\n   * @default false\n   */\n  showFirstButton: PropTypes.bool,\n  /**\n   * If `true`, show the last-page button.\n   * @default false\n   */\n  showLastButton: PropTypes.bool,\n  /**\n   * The props used for each slot inside the TablePagination.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    actions: PropTypes.shape({\n      firstButton: PropTypes.object,\n      firstButtonIcon: PropTypes.object,\n      lastButton: PropTypes.object,\n      lastButtonIcon: PropTypes.object,\n      nextButton: PropTypes.object,\n      nextButtonIcon: PropTypes.object,\n      previousButton: PropTypes.object,\n      previousButtonIcon: PropTypes.object\n    }),\n    select: PropTypes.object\n  }),\n  /**\n   * The components used for each slot inside the TablePagination.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    actions: PropTypes.shape({\n      firstButton: PropTypes.elementType,\n      firstButtonIcon: PropTypes.elementType,\n      lastButton: PropTypes.elementType,\n      lastButtonIcon: PropTypes.elementType,\n      nextButton: PropTypes.elementType,\n      nextButtonIcon: PropTypes.elementType,\n      previousButton: PropTypes.elementType,\n      previousButtonIcon: PropTypes.elementType\n    })\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default TablePagination;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}