# TrustVault - Portfolio Workflow Integration Tests

import pytest
from decimal import Decimal
from django.urls import reverse
from rest_framework import status
from apps.portfolio.models import Portfolio, Asset, Holding, Transaction


@pytest.mark.django_db
class TestCompletePortfolioWorkflow:
    """Test complete portfolio management workflow."""

    def test_complete_portfolio_lifecycle(self, authenticated_client, user):
        """Test complete portfolio lifecycle from creation to deletion."""
        
        # Step 1: Create a portfolio
        portfolio_data = {
            'name': 'My Investment Portfolio',
            'description': 'Long-term investment strategy',
            'portfolio_type': 'MODERATE',
            'currency': 'USD',
            'is_public': False,
        }
        
        response = authenticated_client.post(
            reverse('portfolio:portfolio-list'),
            data=portfolio_data,
            format='json'
        )
        
        assert response.status_code == status.HTTP_201_CREATED
        portfolio_id = response.data['id']
        
        # Step 2: Create assets for investment
        assets_data = [
            {
                'symbol': 'AAPL',
                'name': 'Apple Inc.',
                'asset_type': 'STOCK',
                'current_price': '150.00',
                'currency': 'USD',
                'sector': 'Technology',
                'country': 'US',
            },
            {
                'symbol': 'GOOGL',
                'name': 'Alphabet Inc.',
                'asset_type': 'STOCK',
                'current_price': '2500.00',
                'currency': 'USD',
                'sector': 'Technology',
                'country': 'US',
            },
            {
                'symbol': 'BTC',
                'name': 'Bitcoin',
                'asset_type': 'CRYPTO',
                'current_price': '45000.00',
                'currency': 'USD',
            }
        ]
        
        created_assets = []
        for asset_data in assets_data:
            asset = Asset.objects.create(**asset_data)
            created_assets.append(asset)
        
        # Step 3: Add initial cash deposit
        deposit_transaction = {
            'transaction_type': 'DEPOSIT',
            'total_amount': '10000.00',
            'fees': '0.00',
            'notes': 'Initial investment',
            'transaction_date': '2024-01-01T10:00:00Z'
        }
        
        response = authenticated_client.post(
            reverse('portfolio:transaction-list', kwargs={'portfolio_id': portfolio_id}),
            data=deposit_transaction,
            format='json'
        )
        
        assert response.status_code == status.HTTP_201_CREATED
        
        # Step 4: Buy some stocks
        buy_transactions = [
            {
                'asset_id': str(created_assets[0].id),  # AAPL
                'transaction_type': 'BUY',
                'quantity': '20',
                'price': '150.00',
                'total_amount': '3000.00',
                'fees': '5.00',
                'notes': 'Initial AAPL purchase',
                'transaction_date': '2024-01-02T10:00:00Z'
            },
            {
                'asset_id': str(created_assets[1].id),  # GOOGL
                'transaction_type': 'BUY',
                'quantity': '2',
                'price': '2500.00',
                'total_amount': '5000.00',
                'fees': '10.00',
                'notes': 'Initial GOOGL purchase',
                'transaction_date': '2024-01-03T10:00:00Z'
            },
            {
                'asset_id': str(created_assets[2].id),  # BTC
                'transaction_type': 'BUY',
                'quantity': '0.1',
                'price': '45000.00',
                'total_amount': '4500.00',
                'fees': '15.00',
                'notes': 'Initial BTC purchase',
                'transaction_date': '2024-01-04T10:00:00Z'
            }
        ]
        
        for transaction_data in buy_transactions:
            response = authenticated_client.post(
                reverse('portfolio:transaction-list', kwargs={'portfolio_id': portfolio_id}),
                data=transaction_data,
                format='json'
            )
            assert response.status_code == status.HTTP_201_CREATED
        
        # Step 5: Verify holdings were created
        response = authenticated_client.get(
            reverse('portfolio:holding-list', kwargs={'portfolio_id': portfolio_id})
        )
        
        assert response.status_code == status.HTTP_200_OK
        holdings = response.data
        assert len(holdings) == 3
        
        # Verify holding details
        aapl_holding = next(h for h in holdings if h['asset']['symbol'] == 'AAPL')
        assert Decimal(aapl_holding['quantity']) == Decimal('20')
        assert Decimal(aapl_holding['average_cost']) == Decimal('150.25')  # Including fees
        
        # Step 6: Sell some holdings
        sell_transaction = {
            'asset_id': str(created_assets[0].id),  # AAPL
            'transaction_type': 'SELL',
            'quantity': '10',
            'price': '160.00',
            'total_amount': '1600.00',
            'fees': '5.00',
            'notes': 'Partial AAPL sale',
            'transaction_date': '2024-02-01T10:00:00Z'
        }
        
        response = authenticated_client.post(
            reverse('portfolio:transaction-list', kwargs={'portfolio_id': portfolio_id}),
            data=sell_transaction,
            format='json'
        )
        
        assert response.status_code == status.HTTP_201_CREATED
        
        # Step 7: Verify updated holdings
        response = authenticated_client.get(
            reverse('portfolio:holding-list', kwargs={'portfolio_id': portfolio_id})
        )
        
        assert response.status_code == status.HTTP_200_OK
        holdings = response.data
        
        aapl_holding = next(h for h in holdings if h['asset']['symbol'] == 'AAPL')
        assert Decimal(aapl_holding['quantity']) == Decimal('10')  # Reduced after sale
        
        # Step 8: Check portfolio total value
        response = authenticated_client.get(
            reverse('portfolio:portfolio-detail', kwargs={'pk': portfolio_id})
        )
        
        assert response.status_code == status.HTTP_200_OK
        portfolio = response.data
        
        # Portfolio should have updated total value
        assert Decimal(portfolio['total_value']) > Decimal('0')
        
        # Step 9: View transaction history
        response = authenticated_client.get(
            reverse('portfolio:transaction-list', kwargs={'portfolio_id': portfolio_id})
        )
        
        assert response.status_code == status.HTTP_200_OK
        transactions = response.data
        assert len(transactions) == 5  # 1 deposit + 3 buys + 1 sell
        
        # Step 10: Update portfolio information
        updated_portfolio_data = {
            'name': 'Updated Portfolio Name',
            'description': 'Updated description',
            'portfolio_type': 'AGGRESSIVE',
            'currency': 'USD',
            'is_public': True,
        }
        
        response = authenticated_client.put(
            reverse('portfolio:portfolio-detail', kwargs={'pk': portfolio_id}),
            data=updated_portfolio_data,
            format='json'
        )
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['name'] == 'Updated Portfolio Name'
        assert response.data['portfolio_type'] == 'AGGRESSIVE'
        assert response.data['is_public'] is True
        
        # Step 11: Delete portfolio
        response = authenticated_client.delete(
            reverse('portfolio:portfolio-detail', kwargs={'pk': portfolio_id})
        )
        
        assert response.status_code == status.HTTP_204_NO_CONTENT
        
        # Verify portfolio is deleted
        response = authenticated_client.get(
            reverse('portfolio:portfolio-detail', kwargs={'pk': portfolio_id})
        )
        
        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_portfolio_performance_calculation(self, authenticated_client, user):
        """Test portfolio performance calculation with price changes."""
        
        # Create portfolio
        portfolio_data = {
            'name': 'Performance Test Portfolio',
            'portfolio_type': 'MODERATE',
            'currency': 'USD',
        }
        
        response = authenticated_client.post(
            reverse('portfolio:portfolio-list'),
            data=portfolio_data,
            format='json'
        )
        
        portfolio_id = response.data['id']
        
        # Create asset with initial price
        asset = Asset.objects.create(
            symbol='TEST',
            name='Test Stock',
            asset_type='STOCK',
            current_price='100.00',
            currency='USD'
        )
        
        # Buy shares
        buy_transaction = {
            'asset_id': str(asset.id),
            'transaction_type': 'BUY',
            'quantity': '10',
            'price': '100.00',
            'total_amount': '1000.00',
            'fees': '5.00',
        }
        
        response = authenticated_client.post(
            reverse('portfolio:transaction-list', kwargs={'portfolio_id': portfolio_id}),
            data=buy_transaction,
            format='json'
        )
        
        assert response.status_code == status.HTTP_201_CREATED
        
        # Update asset price (simulate market movement)
        asset.current_price = Decimal('120.00')
        asset.save()
        
        # Get updated portfolio
        response = authenticated_client.get(
            reverse('portfolio:portfolio-detail', kwargs={'pk': portfolio_id})
        )
        
        portfolio = response.data
        holdings = portfolio['holdings']
        
        # Verify profit calculation
        holding = holdings[0]
        expected_current_value = Decimal('10') * Decimal('120.00')  # 10 shares * $120
        expected_profit = expected_current_value - (Decimal('10') * Decimal('100.50'))  # Cost including fees
        
        assert Decimal(holding['current_value']) == expected_current_value
        assert Decimal(holding['profit_loss']) == expected_profit

    def test_multi_currency_portfolio(self, authenticated_client, user):
        """Test portfolio with multiple currencies."""
        
        # Create portfolio in USD
        portfolio_data = {
            'name': 'Multi-Currency Portfolio',
            'portfolio_type': 'MODERATE',
            'currency': 'USD',
        }
        
        response = authenticated_client.post(
            reverse('portfolio:portfolio-list'),
            data=portfolio_data,
            format='json'
        )
        
        portfolio_id = response.data['id']
        
        # Create assets in different currencies
        usd_asset = Asset.objects.create(
            symbol='AAPL',
            name='Apple Inc.',
            asset_type='STOCK',
            current_price='150.00',
            currency='USD'
        )
        
        eur_asset = Asset.objects.create(
            symbol='SAP',
            name='SAP SE',
            asset_type='STOCK',
            current_price='100.00',
            currency='EUR'
        )
        
        # Buy assets in different currencies
        transactions = [
            {
                'asset_id': str(usd_asset.id),
                'transaction_type': 'BUY',
                'quantity': '10',
                'price': '150.00',
                'total_amount': '1500.00',
                'fees': '5.00',
            },
            {
                'asset_id': str(eur_asset.id),
                'transaction_type': 'BUY',
                'quantity': '5',
                'price': '100.00',
                'total_amount': '500.00',  # This would need currency conversion
                'fees': '3.00',
            }
        ]
        
        for transaction_data in transactions:
            response = authenticated_client.post(
                reverse('portfolio:transaction-list', kwargs={'portfolio_id': portfolio_id}),
                data=transaction_data,
                format='json'
            )
            assert response.status_code == status.HTTP_201_CREATED
        
        # Verify portfolio handles multiple currencies
        response = authenticated_client.get(
            reverse('portfolio:portfolio-detail', kwargs={'pk': portfolio_id})
        )
        
        assert response.status_code == status.HTTP_200_OK
        portfolio = response.data
        
        # Should have holdings in both currencies
        assert len(portfolio['holdings']) == 2
        
        # Portfolio total should be in base currency (USD)
        assert portfolio['currency'] == 'USD'

    def test_dividend_transaction_workflow(self, authenticated_client, user):
        """Test dividend transaction handling."""
        
        # Create portfolio and asset
        portfolio_data = {
            'name': 'Dividend Portfolio',
            'portfolio_type': 'CONSERVATIVE',
            'currency': 'USD',
        }
        
        response = authenticated_client.post(
            reverse('portfolio:portfolio-list'),
            data=portfolio_data,
            format='json'
        )
        
        portfolio_id = response.data['id']
        
        asset = Asset.objects.create(
            symbol='MSFT',
            name='Microsoft Corporation',
            asset_type='STOCK',
            current_price='300.00',
            currency='USD'
        )
        
        # Buy shares
        buy_transaction = {
            'asset_id': str(asset.id),
            'transaction_type': 'BUY',
            'quantity': '100',
            'price': '300.00',
            'total_amount': '30000.00',
            'fees': '10.00',
        }
        
        response = authenticated_client.post(
            reverse('portfolio:transaction-list', kwargs={'portfolio_id': portfolio_id}),
            data=buy_transaction,
            format='json'
        )
        
        assert response.status_code == status.HTTP_201_CREATED
        
        # Record dividend payment
        dividend_transaction = {
            'asset_id': str(asset.id),
            'transaction_type': 'DIVIDEND',
            'quantity': '100',  # Number of shares
            'price': '0.68',    # Dividend per share
            'total_amount': '68.00',
            'fees': '0.00',
            'notes': 'Quarterly dividend payment',
        }
        
        response = authenticated_client.post(
            reverse('portfolio:transaction-list', kwargs={'portfolio_id': portfolio_id}),
            data=dividend_transaction,
            format='json'
        )
        
        assert response.status_code == status.HTTP_201_CREATED
        
        # Verify transaction was recorded
        response = authenticated_client.get(
            reverse('portfolio:transaction-list', kwargs={'portfolio_id': portfolio_id})
        )
        
        transactions = response.data
        dividend_tx = next(tx for tx in transactions if tx['transaction_type'] == 'DIVIDEND')
        
        assert Decimal(dividend_tx['total_amount']) == Decimal('68.00')
        assert dividend_tx['notes'] == 'Quarterly dividend payment'
