import _extends from "@babel/runtime/helpers/esm/extends";
import { pie as d3Pie } from 'd3-shape';
const getSortingComparator = (comparator = 'none') => {
  if (typeof comparator === 'function') {
    return comparator;
  }
  switch (comparator) {
    case 'none':
      return null;
    case 'desc':
      return (a, b) => b - a;
    case 'asc':
      return (a, b) => a - b;
    default:
      return null;
  }
};
const formatter = params => {
  const {
    seriesOrder,
    series
  } = params;
  const defaultizedSeries = {};
  seriesOrder.forEach(seriesId => {
    var _series$seriesId$star, _series$seriesId$endA, _series$seriesId$padd, _series$seriesId$sort;
    const arcs = d3Pie().startAngle(2 * Math.PI * ((_series$seriesId$star = series[seriesId].startAngle) != null ? _series$seriesId$star : 0) / 360).endAngle(2 * Math.PI * ((_series$seriesId$endA = series[seriesId].endAngle) != null ? _series$seriesId$endA : 360) / 360).padAngle(2 * Math.PI * ((_series$seriesId$padd = series[seriesId].paddingAngle) != null ? _series$seriesId$padd : 0) / 360).sortValues(getSortingComparator((_series$seriesId$sort = series[seriesId].sortingValues) != null ? _series$seriesId$sort : 'none'))(series[seriesId].data.map(piePoint => piePoint.value));
    defaultizedSeries[seriesId] = _extends({
      valueFormatter: item => item.value.toLocaleString()
    }, series[seriesId], {
      data: series[seriesId].data.map((item, index) => {
        var _item$id;
        return _extends({}, item, {
          id: (_item$id = item.id) != null ? _item$id : `auto-generated-pie-id-${seriesId}-${index}`
        }, arcs[index]);
      }).map(item => {
        var _series$seriesId$valu, _series$seriesId$valu2, _series$seriesId;
        return _extends({}, item, {
          formattedValue: (_series$seriesId$valu = (_series$seriesId$valu2 = (_series$seriesId = series[seriesId]).valueFormatter) == null ? void 0 : _series$seriesId$valu2.call(_series$seriesId, item)) != null ? _series$seriesId$valu : item.value.toLocaleString()
        });
      })
    });
  });
  return {
    seriesOrder,
    series: defaultizedSeries
  };
};
export default formatter;