{"ast": null, "code": "/**\n * Removes event handlers from the given object.\n * A field is considered an event handler if it is a function with a name beginning with `on`.\n *\n * @param object Object to remove event handlers from.\n * @returns Object with event handlers removed.\n */\nfunction omitEventHandlers(object) {\n  if (object === undefined) {\n    return {};\n  }\n  const result = {};\n  Object.keys(object).filter(prop => !(prop.match(/^on[A-Z]/) && typeof object[prop] === 'function')).forEach(prop => {\n    result[prop] = object[prop];\n  });\n  return result;\n}\nexport default omitEventHandlers;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}