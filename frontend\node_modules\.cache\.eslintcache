[{"C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\App.tsx": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\utils\\security.ts": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\store\\authStore.ts": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\NotFoundPage.tsx": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Auth\\LoginPage.tsx": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Auth\\RegisterPage.tsx": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Dashboard\\DashboardPage.tsx": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\PortfoliosPage.tsx": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\PortfolioDetailPage.tsx": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Security\\SecurityPage.tsx": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Profile\\ProfilePage.tsx": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\components\\Auth\\ProtectedRoute.tsx": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\components\\Layout\\Layout.tsx": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\services\\api.ts": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\CreatePortfolioPage.tsx": "16"}, {"size": 2864, "mtime": 1753617893661, "results": "17", "hashOfConfig": "18"}, {"size": 3741, "mtime": 1753628356301, "results": "19", "hashOfConfig": "18"}, {"size": 8283, "mtime": 1753621069166, "results": "20", "hashOfConfig": "18"}, {"size": 5460, "mtime": 1753617989103, "results": "21", "hashOfConfig": "18"}, {"size": 1980, "mtime": 1753618351980, "results": "22", "hashOfConfig": "18"}, {"size": 6779, "mtime": 1753618095645, "results": "23", "hashOfConfig": "18"}, {"size": 14205, "mtime": 1753621205385, "results": "24", "hashOfConfig": "18"}, {"size": 11100, "mtime": 1753618211292, "results": "25", "hashOfConfig": "18"}, {"size": 6875, "mtime": 1753618236618, "results": "26", "hashOfConfig": "18"}, {"size": 10738, "mtime": 1753707290444, "results": "27", "hashOfConfig": "18"}, {"size": 11860, "mtime": 1753621571203, "results": "28", "hashOfConfig": "18"}, {"size": 10407, "mtime": 1753621449358, "results": "29", "hashOfConfig": "18"}, {"size": 1043, "mtime": 1753618067250, "results": "30", "hashOfConfig": "18"}, {"size": 7218, "mtime": 1753618170921, "results": "31", "hashOfConfig": "18"}, {"size": 7873, "mtime": 1753617966849, "results": "32", "hashOfConfig": "18"}, {"size": 9283, "mtime": 1753708447486, "results": "33", "hashOfConfig": "18"}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "3ry0o3", {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\utils\\security.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\store\\authStore.ts", ["82"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\NotFoundPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Auth\\LoginPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Auth\\RegisterPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Dashboard\\DashboardPage.tsx", ["83"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\PortfoliosPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\PortfolioDetailPage.tsx", ["84", "85"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Security\\SecurityPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Profile\\ProfilePage.tsx", ["86"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\components\\Auth\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\components\\Layout\\Layout.tsx", ["87"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\services\\api.ts", ["88"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\CreatePortfolioPage.tsx", ["89", "90"], [], {"ruleId": "91", "severity": 1, "message": "92", "line": 6, "column": 16, "nodeType": "93", "messageId": "94", "endLine": 6, "endColumn": 26}, {"ruleId": "91", "severity": 1, "message": "95", "line": 10, "column": 3, "nodeType": "93", "messageId": "94", "endLine": 10, "endColumn": 8}, {"ruleId": "91", "severity": 1, "message": "96", "line": 21, "column": 3, "nodeType": "93", "messageId": "94", "endLine": 21, "endColumn": 10}, {"ruleId": "97", "severity": 2, "message": "98", "line": 52, "column": 42, "nodeType": "93", "endLine": 52, "endColumn": 50}, {"ruleId": "91", "severity": 1, "message": "99", "line": 32, "column": 10, "nodeType": "93", "messageId": "94", "endLine": 32, "endColumn": 14}, {"ruleId": "91", "severity": 1, "message": "100", "line": 33, "column": 3, "nodeType": "93", "messageId": "94", "endLine": 33, "endColumn": 14}, {"ruleId": "91", "severity": 1, "message": "101", "line": 17, "column": 3, "nodeType": "93", "messageId": "94", "endLine": 17, "endColumn": 11}, {"ruleId": "97", "severity": 2, "message": "102", "line": 73, "column": 35, "nodeType": "93", "endLine": 73, "endColumn": 46}, {"ruleId": "103", "severity": 2, "message": "104", "line": 102, "column": 9, "nodeType": "105", "endLine": 102, "endColumn": 22}, "@typescript-eslint/no-unused-vars", "'AuthTokens' is defined but never used.", "Identifier", "unusedVar", "'Paper' is defined but never used.", "'Tooltip' is defined but never used.", "react-hooks/rules-of-hooks", "React Hook \"useQuery\" is called conditionally. React Hooks must be called in the exact same order in every component render.", "'User' is defined but never used.", "'ChevronLeft' is defined but never used.", "'ApiError' is defined but never used.", "React Hook \"useMutation\" is called conditionally. React Hooks must be called in the exact same order in every component render.", "security/detect-object-injection", "Generic Object Injection Sink", "MemberExpression"]