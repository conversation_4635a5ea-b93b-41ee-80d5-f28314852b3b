[{"C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\App.tsx": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\utils\\security.ts": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\store\\authStore.ts": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\NotFoundPage.tsx": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Auth\\LoginPage.tsx": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Auth\\RegisterPage.tsx": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Dashboard\\DashboardPage.tsx": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\PortfoliosPage.tsx": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\PortfolioDetailPage.tsx": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Security\\SecurityPage.tsx": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Profile\\ProfilePage.tsx": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\components\\Auth\\ProtectedRoute.tsx": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\components\\Layout\\Layout.tsx": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\services\\api.ts": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\CreatePortfolioPage.tsx": "16", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\AddHoldingPage.tsx": "17", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\TransactionsPage.tsx": "18", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\AnalyticsPage.tsx": "19"}, {"size": 2864, "mtime": 1753617893661, "results": "20", "hashOfConfig": "21"}, {"size": 4181, "mtime": 1753710360730, "results": "22", "hashOfConfig": "21"}, {"size": 8283, "mtime": 1753621069166, "results": "23", "hashOfConfig": "21"}, {"size": 5460, "mtime": 1753617989103, "results": "24", "hashOfConfig": "21"}, {"size": 1980, "mtime": 1753618351980, "results": "25", "hashOfConfig": "21"}, {"size": 6779, "mtime": 1753618095645, "results": "26", "hashOfConfig": "21"}, {"size": 14205, "mtime": 1753621205385, "results": "27", "hashOfConfig": "21"}, {"size": 11100, "mtime": 1753618211292, "results": "28", "hashOfConfig": "21"}, {"size": 6875, "mtime": 1753618236618, "results": "29", "hashOfConfig": "21"}, {"size": 10774, "mtime": 1753710814608, "results": "30", "hashOfConfig": "21"}, {"size": 11860, "mtime": 1753621571203, "results": "31", "hashOfConfig": "21"}, {"size": 10407, "mtime": 1753621449358, "results": "32", "hashOfConfig": "21"}, {"size": 1043, "mtime": 1753618067250, "results": "33", "hashOfConfig": "21"}, {"size": 7218, "mtime": 1753618170921, "results": "34", "hashOfConfig": "21"}, {"size": 8267, "mtime": 1753710331020, "results": "35", "hashOfConfig": "21"}, {"size": 8492, "mtime": 1753711056467, "results": "36", "hashOfConfig": "21"}, {"size": 11320, "mtime": 1753711041407, "results": "37", "hashOfConfig": "21"}, {"size": 11677, "mtime": 1753710882340, "results": "38", "hashOfConfig": "21"}, {"size": 13417, "mtime": 1753711075580, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "15o5uiy", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\utils\\security.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\store\\authStore.ts", ["97"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\NotFoundPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Auth\\LoginPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Auth\\RegisterPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Dashboard\\DashboardPage.tsx", ["98"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\PortfoliosPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\PortfolioDetailPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Security\\SecurityPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Profile\\ProfilePage.tsx", ["99"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\components\\Auth\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\components\\Layout\\Layout.tsx", ["100"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\services\\api.ts", ["101"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\CreatePortfolioPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\AddHoldingPage.tsx", ["102", "103", "104", "105"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\TransactionsPage.tsx", ["106", "107"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\AnalyticsPage.tsx", [], [], {"ruleId": "108", "severity": 1, "message": "109", "line": 6, "column": 16, "nodeType": "110", "messageId": "111", "endLine": 6, "endColumn": 26}, {"ruleId": "108", "severity": 1, "message": "112", "line": 10, "column": 3, "nodeType": "110", "messageId": "111", "endLine": 10, "endColumn": 8}, {"ruleId": "108", "severity": 1, "message": "113", "line": 32, "column": 10, "nodeType": "110", "messageId": "111", "endLine": 32, "endColumn": 14}, {"ruleId": "108", "severity": 1, "message": "114", "line": 33, "column": 3, "nodeType": "110", "messageId": "111", "endLine": 33, "endColumn": 14}, {"ruleId": "108", "severity": 1, "message": "115", "line": 17, "column": 3, "nodeType": "110", "messageId": "111", "endLine": 17, "endColumn": 11}, {"ruleId": "108", "severity": 1, "message": "116", "line": 10, "column": 3, "nodeType": "110", "messageId": "111", "endLine": 10, "endColumn": 14}, {"ruleId": "108", "severity": 1, "message": "117", "line": 11, "column": 3, "nodeType": "110", "messageId": "111", "endLine": 11, "endColumn": 13}, {"ruleId": "108", "severity": 1, "message": "118", "line": 12, "column": 3, "nodeType": "110", "messageId": "111", "endLine": 12, "endColumn": 9}, {"ruleId": "108", "severity": 1, "message": "119", "line": 13, "column": 3, "nodeType": "110", "messageId": "111", "endLine": 13, "endColumn": 11}, {"ruleId": "108", "severity": 1, "message": "120", "line": 31, "column": 3, "nodeType": "110", "messageId": "111", "endLine": 31, "endColumn": 13}, {"ruleId": "108", "severity": 1, "message": "121", "line": 45, "column": 10, "nodeType": "110", "messageId": "111", "endLine": 45, "endColumn": 29}, "@typescript-eslint/no-unused-vars", "'AuthTokens' is defined but never used.", "Identifier", "unusedVar", "'Paper' is defined but never used.", "'User' is defined but never used.", "'ChevronLeft' is defined but never used.", "'ApiError' is defined but never used.", "'FormControl' is defined but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'FilterList' is defined but never used.", "'selectedTransaction' is assigned a value but never used."]