{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getStepButtonUtilityClass(slot) {\n  return generateUtilityClass('MuiStepButton', slot);\n}\nconst stepButtonClasses = generateUtilityClasses('MuiStepButton', ['root', 'horizontal', 'vertical', 'touchRipple']);\nexport default stepButtonClasses;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}