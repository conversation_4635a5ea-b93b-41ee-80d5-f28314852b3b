# TrustVault - Core Views

import logging
from datetime import timed<PERSON>ta
from django.http import JsonResponse
from django.db import connection
from django.core.cache import cache
from django.conf import settings
from django.utils import timezone
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import AllowAny
from drf_spectacular.utils import extend_schema

logger = logging.getLogger(__name__)


class HealthCheckView(APIView):
    """Health check endpoint for monitoring."""
    
    permission_classes = [AllowAny]
    
    @extend_schema(
        summary="Health Check",
        description="Check the health status of all system components",
        responses={200: {"description": "System is healthy"}},
        tags=["Health"]
    )
    def get(self, request):
        """Perform comprehensive health check."""
        
        health_status = {
            'status': 'healthy',
            'timestamp': timezone.now().isoformat(),
            'version': '1.0.0',
            'components': {}
        }
        
        # Check database connection
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                health_status['components']['database'] = {
                    'status': 'healthy',
                    'response_time_ms': 0  # Could measure actual response time
                }
        except Exception as e:
            health_status['components']['database'] = {
                'status': 'unhealthy',
                'error': str(e)
            }
            health_status['status'] = 'unhealthy'
        
        # Check Redis connection
        try:
            cache.set('health_check', 'ok', 10)
            cache.get('health_check')
            health_status['components']['redis'] = {
                'status': 'healthy'
            }
        except Exception as e:
            health_status['components']['redis'] = {
                'status': 'unhealthy',
                'error': str(e)
            }
            health_status['status'] = 'unhealthy'
        
        # Check Vault connection (if configured)
        try:
            import hvac
            client = hvac.Client(url=settings.VAULT_URL)
            if client.sys.is_initialized():
                health_status['components']['vault'] = {
                    'status': 'healthy'
                }
            else:
                health_status['components']['vault'] = {
                    'status': 'unhealthy',
                    'error': 'Vault not initialized'
                }
        except Exception as e:
            health_status['components']['vault'] = {
                'status': 'unhealthy',
                'error': str(e)
            }
        
        # Determine overall status
        if health_status['status'] == 'unhealthy':
            return Response(health_status, status=status.HTTP_503_SERVICE_UNAVAILABLE)
        
        return Response(health_status, status=status.HTTP_200_OK)


class SystemStatusView(APIView):
    """System status endpoint with detailed metrics."""
    
    @extend_schema(
        summary="System Status",
        description="Get detailed system status and metrics",
        responses={200: {"description": "System status information"}},
        tags=["System"]
    )
    def get(self, request):
        """Get system status and metrics."""
        
        from django.db import models
        from apps.authentication.models import User
        from apps.core.models import AuditLog, SecurityEvent
        
        status_info = {
            'system': {
                'uptime': self._get_uptime(),
                'version': '1.0.0',
                'environment': 'production' if not settings.DEBUG else 'development',
            },
            'database': {
                'total_users': User.objects.count(),
                'active_users': User.objects.filter(is_active=True).count(),
                'audit_logs_count': AuditLog.objects.count(),
                'security_events_count': SecurityEvent.objects.count(),
            },
            'security': {
                'recent_security_events': SecurityEvent.objects.filter(
                    created_at__gte=timezone.now() - timedelta(hours=24)
                ).count(),
                'unresolved_events': SecurityEvent.objects.filter(
                    is_resolved=False
                ).count(),
            }
        }
        
        return Response(status_info)
    
    def _get_uptime(self):
        """Get system uptime (simplified)."""
        # This is a simplified implementation
        # In production, you might want to track actual application start time
        return "Available"


# Error handlers
def bad_request(request, exception):
    """Handle 400 errors."""
    logger.warning(f"Bad request from {request.META.get('REMOTE_ADDR')}: {exception}")
    return JsonResponse({
        'error': 'Bad Request',
        'message': 'The request could not be understood by the server.',
        'status_code': 400
    }, status=400)


def permission_denied(request, exception):
    """Handle 403 errors."""
    logger.warning(f"Permission denied for {request.META.get('REMOTE_ADDR')}: {exception}")
    return JsonResponse({
        'error': 'Permission Denied',
        'message': 'You do not have permission to access this resource.',
        'status_code': 403
    }, status=403)


def page_not_found(request, exception):
    """Handle 404 errors."""
    logger.info(f"Page not found: {request.path} from {request.META.get('REMOTE_ADDR')}")
    return JsonResponse({
        'error': 'Not Found',
        'message': 'The requested resource was not found.',
        'status_code': 404
    }, status=404)


def server_error(request):
    """Handle 500 errors."""
    logger.error(f"Server error for {request.path} from {request.META.get('REMOTE_ADDR')}")
    return JsonResponse({
        'error': 'Internal Server Error',
        'message': 'An internal server error occurred.',
        'status_code': 500
    }, status=500)
