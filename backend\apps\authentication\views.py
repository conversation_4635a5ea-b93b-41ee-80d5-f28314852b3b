# TrustVault - Authentication Views

import logging
import qrcode
import io
import base64
from django.contrib.auth import login, logout
from django.utils import timezone
from django.conf import settings
from rest_framework import status, generics, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.views import TokenObtainPairView
from django_otp.models import Device
from django_otp.plugins.otp_totp.models import TOTPDevice
from drf_spectacular.utils import extend_schema, OpenApiResponse
from .models import User, LoginAttempt, UserSession
from .serializers import (
    UserRegistrationSerializer, UserLoginSerializer, UserProfileSerializer,
    ChangePasswordSerializer, MFASetupSerializer, LoginAttemptSerializer,
    UserSessionSerializer
)
from apps.core.models import AuditLog

logger = logging.getLogger(__name__)


class UserRegistrationView(generics.CreateAPIView):
    """User registration endpoint."""
    
    queryset = User.objects.all()
    serializer_class = UserRegistrationSerializer
    permission_classes = [permissions.AllowAny]
    
    @extend_schema(
        summary="User Registration",
        description="Register a new user account",
        responses={
            201: OpenApiResponse(description="User created successfully"),
            400: OpenApiResponse(description="Validation errors"),
        },
        tags=["Authentication"]
    )
    def post(self, request, *args, **kwargs):
        """Register new user."""
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            
            # Log registration
            AuditLog.objects.create(
                user=user,
                action='CREATE',
                resource_type='User',
                resource_id=str(user.id),
                ip_address=self._get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                details={'email': user.email}
            )
            
            logger.info(f"New user registered: {user.email}")
            
            return Response({
                'message': 'User registered successfully',
                'user_id': user.id
            }, status=status.HTTP_201_CREATED)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    def _get_client_ip(self, request):
        """Get client IP address."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class UserLoginView(APIView):
    """User login endpoint with JWT token generation."""
    
    permission_classes = [permissions.AllowAny]
    
    @extend_schema(
        summary="User Login",
        description="Authenticate user and return JWT tokens",
        request=UserLoginSerializer,
        responses={
            200: OpenApiResponse(description="Login successful"),
            400: OpenApiResponse(description="Invalid credentials"),
            423: OpenApiResponse(description="Account locked"),
        },
        tags=["Authentication"]
    )
    def post(self, request):
        """Authenticate user and return tokens."""
        serializer = UserLoginSerializer(data=request.data, context={'request': request})
        
        if serializer.is_valid():
            user = serializer.validated_data['user']
            
            # Check if MFA is required
            if user.is_mfa_enabled:
                # For MFA users, return a temporary token that requires MFA verification
                return Response({
                    'mfa_required': True,
                    'message': 'MFA verification required',
                    'user_id': user.id
                }, status=status.HTTP_200_OK)
            
            # Generate JWT tokens
            refresh = RefreshToken.for_user(user)
            access_token = refresh.access_token
            
            # Create user session
            session = UserSession.objects.create(
                user=user,
                session_key=str(refresh),
                ip_address=self._get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                expires_at=timezone.now() + timezone.timedelta(days=7)
            )
            
            # Update user login info
            user.last_login = timezone.now()
            user.last_login_ip = self._get_client_ip(request)
            user.last_login_user_agent = request.META.get('HTTP_USER_AGENT', '')
            user.save(update_fields=['last_login', 'last_login_ip', 'last_login_user_agent'])
            
            # Log successful login
            AuditLog.objects.create(
                user=user,
                action='LOGIN',
                resource_type='User',
                resource_id=str(user.id),
                ip_address=self._get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                details={'session_id': str(session.id)}
            )
            
            logger.info(f"User logged in: {user.email}")
            
            return Response({
                'access_token': str(access_token),
                'refresh_token': str(refresh),
                'user': UserProfileSerializer(user).data,
                'expires_in': settings.SIMPLE_JWT['ACCESS_TOKEN_LIFETIME'].total_seconds()
            }, status=status.HTTP_200_OK)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    def _get_client_ip(self, request):
        """Get client IP address."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class UserLogoutView(APIView):
    """User logout endpoint."""
    
    @extend_schema(
        summary="User Logout",
        description="Logout user and invalidate tokens",
        responses={200: OpenApiResponse(description="Logout successful")},
        tags=["Authentication"]
    )
    def post(self, request):
        """Logout user."""
        try:
            # Get refresh token from request
            refresh_token = request.data.get('refresh_token')
            if refresh_token:
                token = RefreshToken(refresh_token)
                token.blacklist()
            
            # Deactivate user session
            sessions = UserSession.objects.filter(
                user=request.user,
                is_active=True
            )
            sessions.update(is_active=False)
            
            # Log logout
            AuditLog.objects.create(
                user=request.user,
                action='LOGOUT',
                resource_type='User',
                resource_id=str(request.user.id),
                ip_address=self._get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
            )
            
            logger.info(f"User logged out: {request.user.email}")
            
            return Response({
                'message': 'Logout successful'
            }, status=status.HTTP_200_OK)
        
        except Exception as e:
            logger.error(f"Logout error: {str(e)}")
            return Response({
                'message': 'Logout completed'
            }, status=status.HTTP_200_OK)
    
    def _get_client_ip(self, request):
        """Get client IP address."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class UserProfileView(generics.RetrieveUpdateAPIView):
    """User profile endpoint."""
    
    serializer_class = UserProfileSerializer
    
    @extend_schema(
        summary="Get User Profile",
        description="Get current user profile information",
        responses={200: UserProfileSerializer},
        tags=["Authentication"]
    )
    def get(self, request, *args, **kwargs):
        """Get user profile."""
        return super().get(request, *args, **kwargs)
    
    @extend_schema(
        summary="Update User Profile",
        description="Update current user profile information",
        request=UserProfileSerializer,
        responses={200: UserProfileSerializer},
        tags=["Authentication"]
    )
    def put(self, request, *args, **kwargs):
        """Update user profile."""
        return super().put(request, *args, **kwargs)
    
    def get_object(self):
        """Get current user."""
        return self.request.user
    
    def perform_update(self, serializer):
        """Log profile update."""
        user = serializer.save()
        
        AuditLog.objects.create(
            user=user,
            action='UPDATE',
            resource_type='UserProfile',
            resource_id=str(user.id),
            ip_address=self._get_client_ip(self.request),
            user_agent=self.request.META.get('HTTP_USER_AGENT', ''),
            details={'updated_fields': list(serializer.validated_data.keys())}
        )
        
        logger.info(f"User profile updated: {user.email}")
    
    def _get_client_ip(self, request):
        """Get client IP address."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class ChangePasswordView(APIView):
    """Change password endpoint."""
    
    @extend_schema(
        summary="Change Password",
        description="Change user password",
        request=ChangePasswordSerializer,
        responses={200: OpenApiResponse(description="Password changed successfully")},
        tags=["Authentication"]
    )
    def post(self, request):
        """Change user password."""
        serializer = ChangePasswordSerializer(data=request.data, context={'request': request})
        
        if serializer.is_valid():
            serializer.save()
            
            # Log password change
            AuditLog.objects.create(
                user=request.user,
                action='UPDATE',
                resource_type='UserPassword',
                resource_id=str(request.user.id),
                ip_address=self._get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                severity='HIGH'
            )
            
            logger.info(f"Password changed for user: {request.user.email}")
            
            return Response({
                'message': 'Password changed successfully'
            }, status=status.HTTP_200_OK)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    def _get_client_ip(self, request):
        """Get client IP address."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
