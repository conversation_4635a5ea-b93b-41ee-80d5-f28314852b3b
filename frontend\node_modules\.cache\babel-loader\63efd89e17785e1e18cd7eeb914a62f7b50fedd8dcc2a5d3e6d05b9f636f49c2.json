{"ast": null, "code": "'use client';\n\n// based on https://github.com/WICG/focus-visible/blob/v4.1.5/src/focus-visible.js\nimport * as React from 'react';\nimport { Timeout } from '../useTimeout/useTimeout';\nlet hadKeyboardEvent = true;\nlet hadFocusVisibleRecently = false;\nconst hadFocusVisibleRecentlyTimeout = new Timeout();\nconst inputTypesWhitelist = {\n  text: true,\n  search: true,\n  url: true,\n  tel: true,\n  email: true,\n  password: true,\n  number: true,\n  date: true,\n  month: true,\n  week: true,\n  time: true,\n  datetime: true,\n  'datetime-local': true\n};\n\n/**\n * Computes whether the given element should automatically trigger the\n * `focus-visible` class being added, i.e. whether it should always match\n * `:focus-visible` when focused.\n * @param {Element} node\n * @returns {boolean}\n */\nfunction focusTriggersKeyboardModality(node) {\n  const {\n    type,\n    tagName\n  } = node;\n  if (tagName === 'INPUT' && inputTypesWhitelist[type] && !node.readOnly) {\n    return true;\n  }\n  if (tagName === 'TEXTAREA' && !node.readOnly) {\n    return true;\n  }\n  if (node.isContentEditable) {\n    return true;\n  }\n  return false;\n}\n\n/**\n * Keep track of our keyboard modality state with `hadKeyboardEvent`.\n * If the most recent user interaction was via the keyboard;\n * and the key press did not include a meta, alt/option, or control key;\n * then the modality is keyboard. Otherwise, the modality is not keyboard.\n * @param {KeyboardEvent} event\n */\nfunction handleKeyDown(event) {\n  if (event.metaKey || event.altKey || event.ctrlKey) {\n    return;\n  }\n  hadKeyboardEvent = true;\n}\n\n/**\n * If at any point a user clicks with a pointing device, ensure that we change\n * the modality away from keyboard.\n * This avoids the situation where a user presses a key on an already focused\n * element, and then clicks on a different element, focusing it with a\n * pointing device, while we still think we're in keyboard modality.\n */\nfunction handlePointerDown() {\n  hadKeyboardEvent = false;\n}\nfunction handleVisibilityChange() {\n  if (this.visibilityState === 'hidden') {\n    // If the tab becomes active again, the browser will handle calling focus\n    // on the element (Safari actually calls it twice).\n    // If this tab change caused a blur on an element with focus-visible,\n    // re-apply the class when the user switches back to the tab.\n    if (hadFocusVisibleRecently) {\n      hadKeyboardEvent = true;\n    }\n  }\n}\nfunction prepare(doc) {\n  doc.addEventListener('keydown', handleKeyDown, true);\n  doc.addEventListener('mousedown', handlePointerDown, true);\n  doc.addEventListener('pointerdown', handlePointerDown, true);\n  doc.addEventListener('touchstart', handlePointerDown, true);\n  doc.addEventListener('visibilitychange', handleVisibilityChange, true);\n}\nexport function teardown(doc) {\n  doc.removeEventListener('keydown', handleKeyDown, true);\n  doc.removeEventListener('mousedown', handlePointerDown, true);\n  doc.removeEventListener('pointerdown', handlePointerDown, true);\n  doc.removeEventListener('touchstart', handlePointerDown, true);\n  doc.removeEventListener('visibilitychange', handleVisibilityChange, true);\n}\nfunction isFocusVisible(event) {\n  const {\n    target\n  } = event;\n  try {\n    return target.matches(':focus-visible');\n  } catch (error) {\n    // Browsers not implementing :focus-visible will throw a SyntaxError.\n    // We use our own heuristic for those browsers.\n    // Rethrow might be better if it's not the expected error but do we really\n    // want to crash if focus-visible malfunctioned?\n  }\n\n  // No need for validFocusTarget check. The user does that by attaching it to\n  // focusable events only.\n  return hadKeyboardEvent || focusTriggersKeyboardModality(target);\n}\nexport default function useIsFocusVisible() {\n  const ref = React.useCallback(node => {\n    if (node != null) {\n      prepare(node.ownerDocument);\n    }\n  }, []);\n  const isFocusVisibleRef = React.useRef(false);\n\n  /**\n   * Should be called if a blur event is fired\n   */\n  function handleBlurVisible() {\n    // checking against potential state variable does not suffice if we focus and blur synchronously.\n    // React wouldn't have time to trigger a re-render so `focusVisible` would be stale.\n    // Ideally we would adjust `isFocusVisible(event)` to look at `relatedTarget` for blur events.\n    // This doesn't work in IE11 due to https://github.com/facebook/react/issues/3751\n    // TODO: check again if React releases their internal changes to focus event handling (https://github.com/facebook/react/pull/19186).\n    if (isFocusVisibleRef.current) {\n      // To detect a tab/window switch, we look for a blur event followed\n      // rapidly by a visibility change.\n      // If we don't see a visibility change within 100ms, it's probably a\n      // regular focus change.\n      hadFocusVisibleRecently = true;\n      hadFocusVisibleRecentlyTimeout.start(100, () => {\n        hadFocusVisibleRecently = false;\n      });\n      isFocusVisibleRef.current = false;\n      return true;\n    }\n    return false;\n  }\n\n  /**\n   * Should be called if a blur event is fired\n   */\n  function handleFocusVisible(event) {\n    if (isFocusVisible(event)) {\n      isFocusVisibleRef.current = true;\n      return true;\n    }\n    return false;\n  }\n  return {\n    isFocusVisibleRef,\n    onFocus: handleFocusVisible,\n    onBlur: handleBlurVisible,\n    ref\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}